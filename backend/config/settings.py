"""
Django settings for config project.

Generated by 'django-admin startproject' using Django 5.0.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""

import os
from pathlib import Path
import pymysql
from dotenv import load_dotenv

# 加载.env文件
load_dotenv(override=True)  # 添加 override=True 参数，确保覆盖系统环境变量

pymysql.install_as_MySQLdb()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('DJANGO_SECRET_KEY', 'django-insecure-your-secret-key-here')

DEBUG = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1', '************', 'www.cooltrade.xyz', 'cooltrade.xyz']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'rest_framework.authtoken',
    'corsheaders',
    'user',
    'CryptoAnalyst',
    'website.apps.WebsiteConfig',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',  # 必须在 CommonMiddleware 之前
    'config.middleware.DatabaseHealthCheckMiddleware',  # Database health check
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'config.middleware.ConnectionCleanupMiddleware',  # Connection cleanup
]

ROOT_URLCONF = 'config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            os.path.join(BASE_DIR, 'CryptoAnalyst', 'templates'),
        ],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'cooltrade',
        'USER': 'root',
        'PASSWORD': '@Liuzhao-9575@',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            # Add connection health checks and timeouts
            'connect_timeout': 60,
            'read_timeout': 30,
            'write_timeout': 30,
        },
        'CONN_MAX_AGE': 300,  # Keep connections for 5 minutes, then refresh
        'CONN_HEALTH_CHECKS': True,  # Enable connection health checks
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = 'zh-Hans'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# Cache configuration
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
        'TIMEOUT': 3600,  # 1 hour default timeout
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
        }
    }
}
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]
# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 用户模型配置
AUTH_USER_MODEL = 'user.User'

# 邮件配置
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'  # 使用Gmail SMTP服务器
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_USE_SSL = False
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER')  # 从环境变量获取
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD')  # 从环境变量获取
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', '<EMAIL>')

# 检查邮件配置
if not EMAIL_HOST_USER or not EMAIL_HOST_PASSWORD:
    raise ValueError("""
    邮件配置错误！请设置以下环境变量：
    EMAIL_HOST_USER=<EMAIL>
    EMAIL_HOST_PASSWORD=your_app_password
    DEFAULT_FROM_EMAIL=<EMAIL>
    """)

# 邮件调试
EMAIL_DEBUG = True
EMAIL_TIMEOUT = 30  # 设置超时时间

# 邮件模板
EMAIL_TEMPLATE = '''
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Cooltrade Verification Code</title>
  <style>
    body {{ background: #f6f8fa; font-family: system-ui, Arial, sans-serif; color: #222; margin: 0; padding: 0; }}
    .container {{ max-width: 420px; margin: 40px auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 12px #0001; padding: 32px 28px 24px 28px; }}
    .logo {{ display: flex; justify-content: center; margin-bottom: 18px; }}
    .logo img {{ width: 56px; height: 56px; border-radius: 12px; box-shadow: 0 2px 8px #0002; }}
    .brand {{ text-align: center; font-size: 1.6rem; font-weight: 700; color: #f97316; margin-bottom: 18px; letter-spacing: 1px; }}
    .code-box {{ background: linear-gradient(90deg, #f97316 0%, #f43f5e 100%); color: #fff; font-size: 2.2rem; font-weight: bold; letter-spacing: 6px; text-align: center; border-radius: 10px; padding: 18px 0; margin: 18px 0 24px 0; }}
    .desc {{ font-size: 1.1rem; color: #444; text-align: center; margin-bottom: 18px; }}
    .footer {{ color: #888; font-size: 0.95rem; text-align: center; margin-top: 32px; }}
  </style>
</head>
<body>
  <div class="container">
    <div class="logo">
      <img src="https://www.cooltrade.xyz/static/images/logo.png" alt="Cooltrade Logo" />
    </div>
    <div class="brand">Cooltrade</div>
    <div class="desc">Your verification code is:</div>
    <div class="code-box">{code}</div>
    <div class="desc">This code is valid for 10 minutes. Please do not share it with anyone.<br>If you did not request this code, please ignore this email.</div>
    <div class="footer">&copy; 2024 Cooltrade. All rights reserved.</div>
  </div>
</body>
</html>
'''

# REST Framework配置
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.TokenAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
        'rest_framework.parsers.FormParser',
        'rest_framework.parsers.MultiPartParser',
    ],
}

# Celery settings
CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'UTC'

# Celery Beat settings
# 定时任务配置已移至 celery.py 中

# Binance API配置
BINANCE_API_KEY = os.getenv('BINANCE_API_KEY')
BINANCE_API_SECRET = os.getenv('BINANCE_API_SECRET')

# CoinGecko API配置
COINGECKO_API_KEY = os.getenv('COINGECKO_API_KEY')

# Coze API配置
COZE_API_KEY = os.getenv('COZE_API_KEY')
COZE_API_URL = os.getenv('COZE_API_URL')
COZE_SPACE_ID = os.getenv('COZE_SPACE_ID')

# Coze Bot ID 配置
COZE_BOT_ID_ZH = os.getenv('COZE_BOT_ID_ZH', '7504984466486984721')  # 中文 Bot ID
COZE_BOT_ID_EN = os.getenv('COZE_BOT_ID_EN', '7504983775349489680')  # 英文 Bot ID
COZE_BOT_ID_JA = os.getenv('COZE_BOT_ID_JA', '7504985637318475792')  # 日文 Bot ID
COZE_BOT_ID_KO = os.getenv('COZE_BOT_ID_KO', '7504984181148581905')  # 韩文 Bot ID

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'logs/debug.log',
            'formatter': 'verbose',
        },
        'crypto_analyst_file': {
            'class': 'logging.FileHandler',
            'filename': 'logs/crypto_analyst.log',
            'formatter': 'verbose',
        },
        'django_file': {
            'class': 'logging.FileHandler',
            'filename': 'logs/django.log',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'django_file'],
            'level': 'INFO',
            'propagate': True,
        },
        'CryptoAnalyst': {
            'handlers': ['console', 'crypto_analyst_file'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'celery': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': True,
        },
        'tasks': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

# CORS 配置
# 临时启用所有来源以进行调试 - 生产环境中应该设置为 False
CORS_ALLOW_ALL_ORIGINS = True  # 临时设置为 True 进行调试
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [
    "chrome-extension://llakgdikmaijodjohfcagjpgkmflceed",  # 您的扩展 ID
    "chrome-extension://donikojkgchpmgdbfodnpbjhfpiehfhd",
    "http://localhost:3001",
    "http://localhost:5000",  # Vite 默认端口
    "http://localhost:5001",  # 您当前使用的端口
    "http://127.0.0.1:3001",
    "http://127.0.0.1:5000",
    "http://127.0.0.1:5001",
    "http://************:8000",  # 添加本地开发服务器
    "http://************:8000",  # 您提到的服务器地址
    "https://www.cooltrade.xyz",  # 生产环境
]

CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'expires',  # 允许 Expires header
]

CORS_EXPOSE_HEADERS = ['Content-Type', 'X-CSRFToken']
CORS_PREFLIGHT_MAX_AGE = 86400  # 24小时
