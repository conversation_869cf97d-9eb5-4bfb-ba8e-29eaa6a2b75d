export default {
  common: {
    app_name: '<PERSON>trade',
    loading: 'Loading...',
    refresh: 'Refresh',
    refreshing: 'Refreshing...',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    back: 'Back',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    language: 'Language',
    sending: 'Sending...',
    registering: 'Registering...',
    submitting: 'Submitting...',
    retry: 'Retry',
    privacy_policy: 'Privacy Policy',
    about_us: 'About Us',
    no_data: 'No data available',
    load_data: 'Load Data',
    popular_tokens: 'Popular Tokens',
    quick_switch: 'Quick Switch',
    search: 'Search'
  },
  auth: {
    login: 'Login',
    register: 'Register',
    logout: 'Logout',
    email: 'Email',
    password: 'Password',
    confirm_password: 'Confirm Password',
    verification_code: 'Verification Code',
    send_code: 'Send Code',
    forgot_password: 'Forgot Password',
    reset_password: 'Reset Password',
    change_password: 'Change Password',
    current_password: 'Current Password',
    new_password: 'New Password',
    confirm_new_password: 'Confirm Password',
    invitation_code: 'Invitation Code',
    login_success: 'Login Successful',
    register_success: 'Registration Successful',
    logout_success: 'Logout Successful',
    password_changed: 'Password Changed',
    password_reset: 'Password Reset',
    code_sent: 'Verification Code Sent',
    no_account: 'No account?',
    register_now: 'Register Now',
    have_account: 'Have an account?',
    login_now: 'Login Now',
    email_placeholder: 'Enter your email',
    password_placeholder: 'Enter your password',
    verification_code_placeholder: 'Enter verification code',
    invitation_code_placeholder: 'Enter invitation code',
    retry_in_seconds: 'Retry in {seconds}s',
    password_requirements: 'Password must be at least 6 characters and contain both letters and numbers',
    password_changed_success: 'Password changed successfully',
    login_required_for_password_change: 'Please login to change password',
    go_to_login: 'Go to Login',
    enter_current_and_new_password: 'Please enter your current and new password',
    new_password_placeholder: 'Enter new password',
    confirm_new_password_placeholder: 'Confirm new password',
    resetting: 'Resetting...',
    reset_success: 'Password Reset Successful',
    reset_success_message: 'Your password has been reset successfully. Please login with your new password.',
    back_to_login: 'Back to Login'
  },
  profile: {
    profile: 'Profile',
    username: 'Username',
    email: 'Email',
    created_at: 'Created At',
    updated_at: 'Updated At',
    registration_time: 'Registration Time',
    language_preference: 'Language Preference',
    generate_invitation: 'Generate Invitation Code',
    invitation_codes: 'Invitation Codes',
    language_settings: 'Language Settings',
    select_language: 'Select Language',
    chinese: 'Chinese',
    english: 'English',
    japanese: 'Japanese',
    korean: 'Korean'
  },
  analysis: {
    technical_analysis: 'Technical Analysis',
    market_data: 'Market Data',
    trend_analysis: 'Trend Analysis',
    indicators: 'Indicators',
    trading_advice: 'Trading Advice',
    risk_assessment: 'Risk Assessment',
    current_price: 'Current Price',
    last_update: 'Last Update',
    refresh_data: 'Refresh Data',
    refreshing: 'Refreshing...',
    up_probability: 'Up Probability',
    sideways_probability: 'Sideways Probability',
    down_probability: 'Down Probability',
    trend_summary: 'Trend Summary',
    action: 'Action',
    reason: 'Reason',
    entry_price: 'Entry Price',
    stop_loss: 'Stop Loss',
    take_profit: 'Take Profit',
    risk_level: 'Risk Level',
    risk_score: 'Risk Score',
    risk_details: 'Risk Details',
    high_risk: 'High Risk',
    medium_risk: 'Medium Risk',
    low_risk: 'Low Risk',
    market_report: '{symbol} Market Analysis',
    snapshot_price: 'Snapshot Price',
    share_to_twitter: 'Share to Twitter',
    save_image: 'Save Image',
    uptrend: 'Uptrend',
    sideways: 'Sideways',
    downtrend: 'Downtrend',
    market_trend_analysis: 'Market Trend Analysis',
    technical_indicators: 'Technical Indicators',
    recommended_action: 'Recommended Action',
    risk_factors: 'Risk Factors',
    refreshing_data: 'Refreshing Data',
    refreshing_data_ellipsis: 'Refreshing data...',
    calculating_indicators: 'Fetching market data and calculating technical indicators...',
    analyzing_trends: 'Analyzing trends and probabilities...',
    generating_advice: 'Generating trading advice and risk assessment...',
    finalizing_data: 'Finalizing data integration...',
    force_refresh: 'Force Refresh',
    minute_ago: '{n} min ago',
    hour_ago: '{n} hours ago',
    day_ago: '{n} days ago',
    preparing_analysis_report: 'Preparing analysis report...',
    generating_new_report: 'Generating new analysis report, please wait...',
    please_wait: 'Please wait, this may take some time',
    timeout_error: 'Request timeout. Server is processing, please try again later.',
    refresh_report: 'Refresh Report',
    refresh_report_too_soon: 'The report cannot be refreshed until 12 hours have passed'
  },
  tokenNotFound: {
    title: '{symbol} Data Not Found',
    description: 'This token is not yet in our database. Click the button below to get the latest data.',
    refreshButton: 'Get Latest Market Data',
    not_supported: 'This token is not supported yet, please try other tokens'
  },
  indicators: {
    rsi: 'RSI',
    macd: 'MACD',
    bollinger_bands: 'Bollinger Bands',
    bias: 'BIAS',
    psy: 'PSY',
    dmi: 'DMI',
    vwap: 'VWAP',
    funding_rate: 'Funding Rate',
    exchange_netflow: 'Exchange Netflow',
    nupl: 'NUPL',
    mayer_multiple: 'Mayer Multiple'
  },
  errors: {
    network_error: 'Network Error',
    server_error: 'Server Error',
    not_found: 'Not Found',
    unauthorized: 'Unauthorized',
    forbidden: 'Forbidden',
    validation_error: 'Validation Error',
    unknown_error: 'Unknown Error',
    token_expired: 'Login expired, please login again',
    invalid_credentials: 'Invalid email or password',
    invalid_code: 'Invalid or expired verification code',
    passwords_not_match: 'Passwords do not match',
    email_already_registered: 'Email already registered',
    email_not_registered: 'Email not registered',
    invalid_invitation_code: 'Invalid or used invitation code',
    weak_password: 'Password is too weak',
    fill_all_fields: 'Please fill in all required fields',
    login_failed_no_token: 'Login failed: No token received',
    login_failed_server_error: 'Login failed: Server response error',
    login_failed_check_input: 'Login failed, please check your input',
    too_many_attempts: 'Too many login attempts, please try again later',
    network_timeout: 'Network connection timeout, please check your network',
    login_failed_try_later: 'Login failed, please try again later',
    email_required: 'Please enter your email',
    password_required: 'Please enter your password',
    verification_code_required: 'Please enter verification code',
    invitation_code_required: 'Please enter invitation code',
    invalid_email_format: 'Invalid email format',
    send_code_failed: 'Failed to send verification code, please try again later',
    send_code_failed_log: 'Failed to send verification code:',
    registration_failed: 'Registration failed, please check your input',
    registration_failed_log: 'Registration failed:',
    try_reload_or_later: 'Please try reloading or try again later',
    password_change_failed: 'Failed to change password, please try again later',
    password_change_failed_log: 'Failed to change password:',
    password_too_short: 'Password must be at least 6 characters',
    password_must_contain_letters_numbers: 'Password must contain both letters and numbers',
    server_timeout: 'Server timeout, please try again later',
    not_logged_in: 'Not logged in',
    no_response_from_server: 'No response from server',
    refresh_failed: 'Refresh failed, please try again'
  },
  nav: {
    market: 'Market',
    points: 'Points',
    settings: 'Settings'
  },
  points: {
    my_points: 'My Points',
    total_points: 'Total Points',
    ranking: 'Ranking',
    earn_points: 'Earn Points',
    invite_friends: 'Invite Friends',
    invite: 'Invite',
    daily_trade: 'Daily Trade',
    points: 'Points',
    go_trade: 'Go Trade',
    history: 'Points History',
    history_all: 'All',
    history_earned: 'Earned',
    history_used: 'Used',
    no_history: 'No points history',
    invitation_code: 'Invitation Code',
    invitation_records: 'Invitation Records',
    no_invitation_records: 'No invitation records yet',
    total_invited: '{count} people invited',
    invitation_reward: 'Earn {points} points for each friend who registers with your code',
    copy_success: 'Copied successfully',
    share_invitation_title: 'Join Cooltrade',
    share_invitation_text: 'I\'m using Cooltrade for crypto analysis and I invite you to join! Use my invitation code {code} to register and we\'ll both get {points} points.',
    user: 'User',
    your_invitation_code: 'Your Invitation Code',
    share: 'Share',
    invitation_reward_desc: 'Invitation Reward',
    daily_trade_desc: 'Daily Trade Reward',
    used_for_discount: 'Used for Discount',
    registered_at: 'Registered at'
  },
  indicatorExplanations: {
    RSI: 'Relative Strength Index (RSI), measures price momentum and overbought/oversold conditions.',
    BIAS: 'Bias, measures the deviation of price from the moving average.',
    PSY: "Psychological Line, reflects market participants' psychological changes.",
    VWAP: 'Volume Weighted Average Price, reflects the true trading value of the market.',
    FundingRate: 'Funding Rate, reflects the balance of long and short forces in the contract market.',
    ExchangeNetflow: 'Exchange Netflow, reflects the direction of capital flow.',
    NUPL: 'Net Unrealized Profit/Loss, reflects the overall profit and loss status of the market.',
    MayerMultiple: 'Mayer Multiple, the ratio of the current price to the 200-day moving average.',
    MACD: 'Moving Average Convergence Divergence, used to judge trend strength and turning points.',
    BollingerBands: 'Bollinger Bands, measures price volatility and support/resistance levels.',
    DMI: 'Directional Movement Index, used to judge trend direction and strength.'
  },
  market: {
    crypto: 'Crypto',
    stock: 'Stock'
  },
  search: {
    title: 'Search Assets',
    placeholder: 'Search symbols or names...',
    searching: 'Searching...',
    no_results: 'No results found',
    popular: 'Popular Assets'
  },
  favorites: {
    add: 'Add to Favorites',
    remove: 'Remove from Favorites',
    title: 'My Favorites',
    empty: 'No favorites yet',
    added: 'Added to favorites',
    removed: 'Removed from favorites'
  }
}
