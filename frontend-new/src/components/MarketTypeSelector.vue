<template>
  <div class="market-type-selector">
    <div class="flex items-center space-x-2 bg-gray-800/50 rounded-lg p-1">
      <button
        v-for="market in marketTypes"
        :key="market.value"
        :class="[
          'flex-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200',
          selectedMarket === market.value
            ? 'bg-blue-600 text-white shadow-lg'
            : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
        ]"
        @click="selectMarket(market.value)"
      >
        <i :class="market.icon" class="mr-2"></i>
        {{ t(market.label) }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useEnhancedI18n } from '@/utils/i18n-helper'

const { t } = useEnhancedI18n()

export interface MarketType {
  value: 'crypto' | 'stock'
  label: string
  icon: string
}

interface Props {
  modelValue: 'crypto' | 'stock'
}

interface Emits {
  (e: 'update:modelValue', value: 'crypto' | 'stock'): void
  (e: 'change', value: 'crypto' | 'stock'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const selectedMarket = ref<'crypto' | 'stock'>(props.modelValue)

const marketTypes: MarketType[] = [
  {
    value: 'crypto',
    label: 'market.crypto',
    icon: 'ri-currency-line'
  },
  {
    value: 'stock',
    label: 'market.stock',
    icon: 'ri-line-chart-line'
  }
]

const selectMarket = (market: 'crypto' | 'stock') => {
  if (selectedMarket.value === market) return
  
  selectedMarket.value = market
  emit('update:modelValue', market)
  emit('change', market)
}

// 监听外部变化
watch(() => props.modelValue, (newValue) => {
  selectedMarket.value = newValue
})
</script>

<style scoped>
.market-type-selector {
  @apply w-full;
}
</style>
