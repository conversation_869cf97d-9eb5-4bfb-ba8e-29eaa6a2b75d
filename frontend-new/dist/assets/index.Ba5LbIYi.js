var A=Object.defineProperty,e=Object.defineProperties,t=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertyNames,n=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,a=(A,e)=>(e=Symbol[A])?e:Symbol.for("Symbol."+A),i=(e,t,r)=>t in e?A(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,l=(A,e)=>{for(var t in e||(e={}))s.call(e,t)&&i(A,t,e[t]);if(n)for(var t of n(e))o.call(e,t)&&i(A,t,e[t]);return A},c=(A,r)=>e(A,t(r)),u=(A,e,t)=>new Promise(((r,n)=>{var s=A=>{try{a(t.next(A))}catch(e){n(e)}},o=A=>{try{a(t.throw(A))}catch(e){n(e)}},a=A=>A.done?r(A.value):Promise.resolve(A.value).then(s,o);a((t=t.apply(A,e)).next())})),d=function(A,e){this[0]=A,this[1]=e},B=(A,e,t)=>{var r=(A,e,n,s)=>{try{var o=t[A](e),a=(e=o.value)instanceof d,i=o.done;Promise.resolve(a?e[0]:e).then((t=>a?r("return"===A?A:"next",e[1]?{done:t.done,value:t.value}:t,n,s):n({value:t,done:i}))).catch((A=>r("throw",A,n,s)))}catch(l){s(l)}},n=A=>s[A]=e=>new Promise(((t,n)=>r(A,e,t,n))),s={};return t=t.apply(A,e),s[a("asyncIterator")]=()=>s,n("next"),n("throw"),n("return"),s},g=A=>{var e,t=A[a("asyncIterator")],r=!1,n={};return null==t?(t=A[a("iterator")](),e=A=>n[A]=e=>t[A](e)):(t=t.call(A),e=A=>n[A]=e=>{if(r){if(r=!1,"throw"===A)throw e;return e}return r=!0,{done:!1,value:new d(new Promise((r=>{var n=t[A](e);n instanceof Object||(A=>{throw TypeError(A)})("Object expected"),r(n)})),1)}}),n[a("iterator")]=()=>n,e("next"),"throw"in t?e("throw"):n.throw=A=>{throw A},"return"in t&&e("return"),n};import{ao as p,r as f,s as w,c as h,j as m,l as Q,x as y,af as C,O as U,g as v,i as F,h as b,S as _,P as x,$ as E,y as H,J as I,A as k,B as L,M as S,z as K,G as D,L as T,D as O,u as M,aw as R,a6 as P,V as N,I as V,ak as G,n as J,H as X,C as j,ag as W,ax as Y,ay as Z,az as z,at as q,aA as $}from"./vendor.CgHCx9DO.js";import{E as AA,a as eA,i as tA}from"./ui.D8CjPntM.js";var rA,nA,sA=(rA={"assets/index.Ba5LbIYi.js"(A){!function(){const A=document.createElement("link").relList;if(!(A&&A.supports&&A.supports("modulepreload"))){for(const A of document.querySelectorAll('link[rel="modulepreload"]'))e(A);new MutationObserver((A=>{for(const t of A)if("childList"===t.type)for(const A of t.addedNodes)"LINK"===A.tagName&&"modulepreload"===A.rel&&e(A)})).observe(document,{childList:!0,subtree:!0})}function e(A){if(A.ep)return;A.ep=!0;const e=function(A){const e={};return A.integrity&&(e.integrity=A.integrity),A.referrerPolicy&&(e.referrerPolicy=A.referrerPolicy),"use-credentials"===A.crossOrigin?e.credentials="include":"anonymous"===A.crossOrigin?e.credentials="omit":e.credentials="same-origin",e}(A);fetch(A.href,e)}}();
/*!
     * html2canvas 1.4.1 <https://html2canvas.hertzen.com>
     * Copyright (c) 2022 Niklas von Hertzen <https://hertzen.com>
     * Released under MIT License
     */
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.
    
    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.
    
    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */
var e=function(A,t){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(A,e){A.__proto__=e}||function(A,e){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(A[t]=e[t])})(A,t)};function t(A,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=A}e(A,t),A.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var r=function(){return r=Object.assign||function(A){for(var e,t=1,r=arguments.length;t<r;t++)for(var n in e=arguments[t])Object.prototype.hasOwnProperty.call(e,n)&&(A[n]=e[n]);return A},r.apply(this,arguments)};function n(A,e,t,r){return new(t||(t=Promise))((function(e,n){function s(A){try{a(r.next(A))}catch(e){n(e)}}function o(A){try{a(r.throw(A))}catch(e){n(e)}}function a(A){var r;A.done?e(A.value):(r=A.value,r instanceof t?r:new t((function(A){A(r)}))).then(s,o)}a((r=r.apply(A,[])).next())}))}function s(A,e){var t,r,n,s,o={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;o;)try{if(t=1,r&&(n=2&s[0]?r.return:s[0]?r.throw||((n=r.return)&&n.call(r),0):r.next)&&!(n=n.call(r,s[1])).done)return n;switch(r=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,r=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!((n=(n=o.trys).length>0&&n[n.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){o.label=s[1];break}if(6===s[0]&&o.label<n[1]){o.label=n[1],n=s;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(s);break}n[2]&&o.ops.pop(),o.trys.pop();continue}s=e.call(A,o)}catch(a){s=[6,a],r=0}finally{t=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}}for(var o=function(){function A(A,e,t,r){this.left=A,this.top=e,this.width=t,this.height=r}return A.prototype.add=function(e,t,r,n){return new A(this.left+e,this.top+t,this.width+r,this.height+n)},A.fromClientRect=function(e,t){return new A(t.left+e.windowBounds.left,t.top+e.windowBounds.top,t.width,t.height)},A.fromDOMRectList=function(e,t){var r=Array.from(t).find((function(A){return 0!==A.width}));return r?new A(r.left+e.windowBounds.left,r.top+e.windowBounds.top,r.width,r.height):A.EMPTY},A.EMPTY=new A(0,0,0,0),A}(),i=function(A,e){return o.fromClientRect(A,e.getBoundingClientRect())},rA=function(A){for(var e=[],t=0,r=A.length;t<r;){var n=A.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var s=A.charCodeAt(t++);56320==(64512&s)?e.push(((1023&n)<<10)+(1023&s)+65536):(e.push(n),t--)}else e.push(n)}return e},nA=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var r=[],n=-1,s="";++n<t;){var o=A[n];o<=65535?r.push(o):(o-=65536,r.push(55296+(o>>10),o%1024+56320)),(n+1===t||r.length>16384)&&(s+=String.fromCharCode.apply(String,r),r.length=0)}return s},sA="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",oA="undefined"==typeof Uint8Array?[]:new Uint8Array(256),aA=0;aA<64;aA++)oA[sA.charCodeAt(aA)]=aA;for(var iA="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",lA="undefined"==typeof Uint8Array?[]:new Uint8Array(256),cA=0;cA<64;cA++)lA[iA.charCodeAt(cA)]=cA;for(var uA=function(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))},dA=function(){function A(A,e,t,r,n,s){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=r,this.index=n,this.data=s}return A.prototype.get=function(A){var e;if(A>=0){if(A<55296||A>56319&&A<=65535)return e=((e=this.index[A>>5])<<2)+(31&A),this.data[e];if(A<=65535)return e=((e=this.index[2048+(A-55296>>5)])<<2)+(31&A),this.data[e];if(A<this.highStart)return e=2080+(A>>11),e=this.index[e],e+=A>>5&63,e=((e=this.index[e])<<2)+(31&A),this.data[e];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),BA="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",gA="undefined"==typeof Uint8Array?[]:new Uint8Array(256),pA=0;pA<64;pA++)gA[BA.charCodeAt(pA)]=pA;var fA,wA,hA,mA,QA,yA,CA,UA,vA=10,FA=13,bA=15,_A=17,xA=18,EA=19,HA=20,IA=21,kA=22,LA=24,SA=25,KA=26,DA=27,TA=28,OA=30,MA=32,RA=33,PA=34,NA=35,VA=37,GA=38,JA=39,XA=40,jA=42,WA=[9001,65288],YA="×",ZA="÷",zA=(mA=function(A){var e,t,r,n,s,o=.75*A.length,a=A.length,i=0;"="===A[A.length-1]&&(o--,"="===A[A.length-2]&&o--);var l="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?new ArrayBuffer(o):new Array(o),c=Array.isArray(l)?l:new Uint8Array(l);for(e=0;e<a;e+=4)t=lA[A.charCodeAt(e)],r=lA[A.charCodeAt(e+1)],n=lA[A.charCodeAt(e+2)],s=lA[A.charCodeAt(e+3)],c[i++]=t<<2|r>>4,c[i++]=(15&r)<<4|n>>2,c[i++]=(3&n)<<6|63&s;return l}("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"),QA=Array.isArray(mA)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t}(mA):new Uint32Array(mA),yA=Array.isArray(mA)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t}(mA):new Uint16Array(mA),CA=uA(yA,12,QA[4]/2),UA=2===QA[5]?uA(yA,(24+QA[4])/2):(fA=QA,wA=Math.ceil((24+QA[4])/4),fA.slice?fA.slice(wA,hA):new Uint32Array(Array.prototype.slice.call(fA,wA,hA))),new dA(QA[0],QA[1],QA[2],QA[3],CA,UA)),qA=[OA,36],$A=[1,2,3,5],Ae=[vA,8],ee=[DA,KA],te=$A.concat(Ae),re=[GA,JA,XA,PA,NA],ne=[bA,FA],se=function(A,e,t,r){var n=r[t];if(Array.isArray(A)?-1!==A.indexOf(n):A===n)for(var s=t;s<=r.length;){if((i=r[++s])===e)return!0;if(i!==vA)break}if(n===vA)for(s=t;s>0;){var o=r[--s];if(Array.isArray(A)?-1!==A.indexOf(o):A===o)for(var a=t;a<=r.length;){var i;if((i=r[++a])===e)return!0;if(i!==vA)break}if(o!==vA)break}return!1},oe=function(A,e){for(var t=A;t>=0;){var r=e[t];if(r!==vA)return r;t--}return 0},ae=function(A,e,t,r,n){if(0===t[r])return YA;var s=r-1;if(Array.isArray(n)&&!0===n[s])return YA;var o=s-1,a=s+1,i=e[s],l=o>=0?e[o]:0,c=e[a];if(2===i&&3===c)return YA;if(-1!==$A.indexOf(i))return"!";if(-1!==$A.indexOf(c))return YA;if(-1!==Ae.indexOf(c))return YA;if(8===oe(s,e))return ZA;if(11===zA.get(A[s]))return YA;if((i===MA||i===RA)&&11===zA.get(A[a]))return YA;if(7===i||7===c)return YA;if(9===i)return YA;if(-1===[vA,FA,bA].indexOf(i)&&9===c)return YA;if(-1!==[_A,xA,EA,LA,TA].indexOf(c))return YA;if(oe(s,e)===kA)return YA;if(se(23,kA,s,e))return YA;if(se([_A,xA],IA,s,e))return YA;if(se(12,12,s,e))return YA;if(i===vA)return ZA;if(23===i||23===c)return YA;if(16===c||16===i)return ZA;if(-1!==[FA,bA,IA].indexOf(c)||14===i)return YA;if(36===l&&-1!==ne.indexOf(i))return YA;if(i===TA&&36===c)return YA;if(c===HA)return YA;if(-1!==qA.indexOf(c)&&i===SA||-1!==qA.indexOf(i)&&c===SA)return YA;if(i===DA&&-1!==[VA,MA,RA].indexOf(c)||-1!==[VA,MA,RA].indexOf(i)&&c===KA)return YA;if(-1!==qA.indexOf(i)&&-1!==ee.indexOf(c)||-1!==ee.indexOf(i)&&-1!==qA.indexOf(c))return YA;if(-1!==[DA,KA].indexOf(i)&&(c===SA||-1!==[kA,bA].indexOf(c)&&e[a+1]===SA)||-1!==[kA,bA].indexOf(i)&&c===SA||i===SA&&-1!==[SA,TA,LA].indexOf(c))return YA;if(-1!==[SA,TA,LA,_A,xA].indexOf(c))for(var u=s;u>=0;){if((d=e[u])===SA)return YA;if(-1===[TA,LA].indexOf(d))break;u--}if(-1!==[DA,KA].indexOf(c))for(u=-1!==[_A,xA].indexOf(i)?o:s;u>=0;){var d;if((d=e[u])===SA)return YA;if(-1===[TA,LA].indexOf(d))break;u--}if(GA===i&&-1!==[GA,JA,PA,NA].indexOf(c)||-1!==[JA,PA].indexOf(i)&&-1!==[JA,XA].indexOf(c)||-1!==[XA,NA].indexOf(i)&&c===XA)return YA;if(-1!==re.indexOf(i)&&-1!==[HA,KA].indexOf(c)||-1!==re.indexOf(c)&&i===DA)return YA;if(-1!==qA.indexOf(i)&&-1!==qA.indexOf(c))return YA;if(i===LA&&-1!==qA.indexOf(c))return YA;if(-1!==qA.concat(SA).indexOf(i)&&c===kA&&-1===WA.indexOf(A[a])||-1!==qA.concat(SA).indexOf(c)&&i===xA)return YA;if(41===i&&41===c){for(var B=t[s],g=1;B>0&&41===e[--B];)g++;if(g%2!=0)return YA}return i===MA&&c===RA?YA:ZA},ie=function(A,e){e||(e={lineBreak:"normal",wordBreak:"normal"});var t=function(A,e){void 0===e&&(e="strict");var t=[],r=[],n=[];return A.forEach((function(A,s){var o=zA.get(A);if(o>50?(n.push(!0),o-=50):n.push(!1),-1!==["normal","auto","loose"].indexOf(e)&&-1!==[8208,8211,12316,12448].indexOf(A))return r.push(s),t.push(16);if(4===o||11===o){if(0===s)return r.push(s),t.push(OA);var a=t[s-1];return-1===te.indexOf(a)?(r.push(r[s-1]),t.push(a)):(r.push(s),t.push(OA))}return r.push(s),31===o?t.push("strict"===e?IA:VA):o===jA||29===o?t.push(OA):43===o?A>=131072&&A<=196605||A>=196608&&A<=262141?t.push(VA):t.push(OA):void t.push(o)})),[r,t,n]}(A,e.lineBreak),r=t[0],n=t[1],s=t[2];return"break-all"!==e.wordBreak&&"break-word"!==e.wordBreak||(n=n.map((function(A){return-1!==[SA,OA,jA].indexOf(A)?VA:A}))),[r,n,"keep-all"===e.wordBreak?s.map((function(e,t){return e&&A[t]>=19968&&A[t]<=40959})):void 0]},le=function(){function A(A,e,t,r){this.codePoints=A,this.required="!"===e,this.start=t,this.end=r}return A.prototype.slice=function(){return nA.apply(void 0,this.codePoints.slice(this.start,this.end))},A}(),ce=45,ue=43,de=-1,Be=function(A){return A>=48&&A<=57},ge=function(A){return Be(A)||A>=65&&A<=70||A>=97&&A<=102},pe=function(A){return 10===A||9===A||32===A},fe=function(A){return function(A){return function(A){return A>=97&&A<=122}(A)||function(A){return A>=65&&A<=90}(A)}(A)||function(A){return A>=128}(A)||95===A},we=function(A){return fe(A)||Be(A)||A===ce},he=function(A){return A>=0&&A<=8||11===A||A>=14&&A<=31||127===A},me=function(A,e){return 92===A&&10!==e},Qe=function(A,e,t){return A===ce?fe(e)||me(e,t):!!fe(A)||!(92!==A||!me(A,e))},ye=function(A,e,t){return A===ue||A===ce?!!Be(e)||46===e&&Be(t):Be(46===A?e:A)},Ce=function(A){var e=0,t=1;A[e]!==ue&&A[e]!==ce||(A[e]===ce&&(t=-1),e++);for(var r=[];Be(A[e]);)r.push(A[e++]);var n=r.length?parseInt(nA.apply(void 0,r),10):0;46===A[e]&&e++;for(var s=[];Be(A[e]);)s.push(A[e++]);var o=s.length,a=o?parseInt(nA.apply(void 0,s),10):0;69!==A[e]&&101!==A[e]||e++;var i=1;A[e]!==ue&&A[e]!==ce||(A[e]===ce&&(i=-1),e++);for(var l=[];Be(A[e]);)l.push(A[e++]);var c=l.length?parseInt(nA.apply(void 0,l),10):0;return t*(n+a*Math.pow(10,-o))*Math.pow(10,i*c)},Ue={type:2},ve={type:3},Fe={type:4},be={type:13},_e={type:8},xe={type:21},Ee={type:9},He={type:10},Ie={type:11},ke={type:12},Le={type:14},Se={type:23},Ke={type:1},De={type:25},Te={type:24},Oe={type:26},Me={type:27},Re={type:28},Pe={type:29},Ne={type:31},Ve={type:32},Ge=function(){function A(){this._value=[]}return A.prototype.write=function(A){this._value=this._value.concat(rA(A))},A.prototype.read=function(){for(var A=[],e=this.consumeToken();e!==Ve;)A.push(e),e=this.consumeToken();return A},A.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case 34:return this.consumeStringToken(34);case 35:var e=this.peekCodePoint(0),t=this.peekCodePoint(1),r=this.peekCodePoint(2);if(we(e)||me(t,r)){var n=Qe(e,t,r)?2:1;return{type:5,value:this.consumeName(),flags:n}}break;case 36:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),be;break;case 39:return this.consumeStringToken(39);case 40:return Ue;case 41:return ve;case 42:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),Le;break;case ue:if(ye(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case 44:return Fe;case ce:var s=A,o=this.peekCodePoint(0),a=this.peekCodePoint(1);if(ye(s,o,a))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(Qe(s,o,a))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(o===ce&&62===a)return this.consumeCodePoint(),this.consumeCodePoint(),Te;break;case 46:if(ye(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case 47:if(42===this.peekCodePoint(0))for(this.consumeCodePoint();;){var i=this.consumeCodePoint();if(42===i&&47===(i=this.consumeCodePoint()))return this.consumeToken();if(i===de)return this.consumeToken()}break;case 58:return Oe;case 59:return Me;case 60:if(33===this.peekCodePoint(0)&&this.peekCodePoint(1)===ce&&this.peekCodePoint(2)===ce)return this.consumeCodePoint(),this.consumeCodePoint(),De;break;case 64:var l=this.peekCodePoint(0),c=this.peekCodePoint(1),u=this.peekCodePoint(2);if(Qe(l,c,u))return{type:7,value:this.consumeName()};break;case 91:return Re;case 92:if(me(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case 93:return Pe;case 61:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),_e;break;case 123:return Ie;case 125:return ke;case 117:case 85:var d=this.peekCodePoint(0),B=this.peekCodePoint(1);return d!==ue||!ge(B)&&63!==B||(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case 124:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),Ee;if(124===this.peekCodePoint(0))return this.consumeCodePoint(),xe;break;case 126:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),He;break;case de:return Ve}return pe(A)?(this.consumeWhiteSpace(),Ne):Be(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):fe(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:nA(A)}},A.prototype.consumeCodePoint=function(){var A=this._value.shift();return void 0===A?-1:A},A.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},A.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},A.prototype.consumeUnicodeRangeToken=function(){for(var A=[],e=this.consumeCodePoint();ge(e)&&A.length<6;)A.push(e),e=this.consumeCodePoint();for(var t=!1;63===e&&A.length<6;)A.push(e),e=this.consumeCodePoint(),t=!0;if(t)return{type:30,start:parseInt(nA.apply(void 0,A.map((function(A){return 63===A?48:A}))),16),end:parseInt(nA.apply(void 0,A.map((function(A){return 63===A?70:A}))),16)};var r=parseInt(nA.apply(void 0,A),16);if(this.peekCodePoint(0)===ce&&ge(this.peekCodePoint(1))){this.consumeCodePoint(),e=this.consumeCodePoint();for(var n=[];ge(e)&&n.length<6;)n.push(e),e=this.consumeCodePoint();return{type:30,start:r,end:parseInt(nA.apply(void 0,n),16)}}return{type:30,start:r,end:r}},A.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return"url"===A.toLowerCase()&&40===this.peekCodePoint(0)?(this.consumeCodePoint(),this.consumeUrlToken()):40===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},A.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===de)return{type:22,value:""};var e=this.peekCodePoint(0);if(39===e||34===e){var t=this.consumeStringToken(this.consumeCodePoint());return 0===t.type&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===de||41===this.peekCodePoint(0))?(this.consumeCodePoint(),{type:22,value:t.value}):(this.consumeBadUrlRemnants(),Se)}for(;;){var r=this.consumeCodePoint();if(r===de||41===r)return{type:22,value:nA.apply(void 0,A)};if(pe(r))return this.consumeWhiteSpace(),this.peekCodePoint(0)===de||41===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:22,value:nA.apply(void 0,A)}):(this.consumeBadUrlRemnants(),Se);if(34===r||39===r||40===r||he(r))return this.consumeBadUrlRemnants(),Se;if(92===r){if(!me(r,this.peekCodePoint(0)))return this.consumeBadUrlRemnants(),Se;A.push(this.consumeEscapedCodePoint())}else A.push(r)}},A.prototype.consumeWhiteSpace=function(){for(;pe(this.peekCodePoint(0));)this.consumeCodePoint()},A.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(41===A||A===de)return;me(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},A.prototype.consumeStringSlice=function(A){for(var e="";A>0;){var t=Math.min(5e4,A);e+=nA.apply(void 0,this._value.splice(0,t)),A-=t}return this._value.shift(),e},A.prototype.consumeStringToken=function(A){for(var e="",t=0;;){var r=this._value[t];if(r===de||void 0===r||r===A)return{type:0,value:e+=this.consumeStringSlice(t)};if(10===r)return this._value.splice(0,t),Ke;if(92===r){var n=this._value[t+1];n!==de&&void 0!==n&&(10===n?(e+=this.consumeStringSlice(t),t=-1,this._value.shift()):me(r,n)&&(e+=this.consumeStringSlice(t),e+=nA(this.consumeEscapedCodePoint()),t=-1))}t++}},A.prototype.consumeNumber=function(){var A=[],e=4,t=this.peekCodePoint(0);for(t!==ue&&t!==ce||A.push(this.consumeCodePoint());Be(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0);var r=this.peekCodePoint(1);if(46===t&&Be(r))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;Be(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0),r=this.peekCodePoint(1);var n=this.peekCodePoint(2);if((69===t||101===t)&&((r===ue||r===ce)&&Be(n)||Be(r)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;Be(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[Ce(A),e]},A.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),e=A[0],t=A[1],r=this.peekCodePoint(0),n=this.peekCodePoint(1),s=this.peekCodePoint(2);return Qe(r,n,s)?{type:15,number:e,flags:t,unit:this.consumeName()}:37===r?(this.consumeCodePoint(),{type:16,number:e,flags:t}):{type:17,number:e,flags:t}},A.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(ge(A)){for(var e=nA(A);ge(this.peekCodePoint(0))&&e.length<6;)e+=nA(this.consumeCodePoint());pe(this.peekCodePoint(0))&&this.consumeCodePoint();var t=parseInt(e,16);return 0===t||function(A){return A>=55296&&A<=57343}(t)||t>1114111?65533:t}return A===de?65533:A},A.prototype.consumeName=function(){for(var A="";;){var e=this.consumeCodePoint();if(we(e))A+=nA(e);else{if(!me(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),A;A+=nA(this.consumeEscapedCodePoint())}}},A}(),Je=function(){function A(A){this._tokens=A}return A.create=function(e){var t=new Ge;return t.write(e),new A(t.read())},A.parseValue=function(e){return A.create(e).parseComponentValue()},A.parseValues=function(e){return A.create(e).parseComponentValues()},A.prototype.parseComponentValue=function(){for(var A=this.consumeToken();31===A.type;)A=this.consumeToken();if(32===A.type)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);var e=this.consumeComponentValue();do{A=this.consumeToken()}while(31===A.type);if(32===A.type)return e;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},A.prototype.parseComponentValues=function(){for(var A=[];;){var e=this.consumeComponentValue();if(32===e.type)return A;A.push(e),A.push()}},A.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},A.prototype.consumeSimpleBlock=function(A){for(var e={type:A,values:[]},t=this.consumeToken();;){if(32===t.type||At(t,A))return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue()),t=this.consumeToken()}},A.prototype.consumeFunction=function(A){for(var e={name:A.value,values:[],type:18};;){var t=this.consumeToken();if(32===t.type||3===t.type)return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue())}},A.prototype.consumeToken=function(){var A=this._tokens.shift();return void 0===A?Ve:A},A.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},A}(),Xe=function(A){return 15===A.type},je=function(A){return 17===A.type},We=function(A){return 20===A.type},Ye=function(A){return 0===A.type},Ze=function(A,e){return We(A)&&A.value===e},ze=function(A){return 31!==A.type},qe=function(A){return 31!==A.type&&4!==A.type},$e=function(A){var e=[],t=[];return A.forEach((function(A){if(4===A.type){if(0===t.length)throw new Error("Error parsing function args, zero tokens for arg");return e.push(t),void(t=[])}31!==A.type&&t.push(A)})),t.length&&e.push(t),e},At=function(A,e){return 11===e&&12===A.type||28===e&&29===A.type||2===e&&3===A.type},et=function(A){return 17===A.type||15===A.type},tt=function(A){return 16===A.type||et(A)},rt=function(A){return A.length>1?[A[0],A[1]]:[A[0]]},nt={type:17,number:0,flags:4},st={type:16,number:50,flags:4},ot={type:16,number:100,flags:4},at=function(A,e,t){var r=A[0],n=A[1];return[it(r,e),it(void 0!==n?n:r,t)]},it=function(A,e){if(16===A.type)return A.number/100*e;if(Xe(A))switch(A.unit){case"rem":case"em":return 16*A.number;default:return A.number}return A.number},lt="grad",ct="turn",ut=function(A,e){if(15===e.type)switch(e.unit){case"deg":return Math.PI*e.number/180;case lt:return Math.PI/200*e.number;case"rad":return e.number;case ct:return 2*Math.PI*e.number}throw new Error("Unsupported angle type")},dt=function(A){return 15===A.type&&("deg"===A.unit||A.unit===lt||"rad"===A.unit||A.unit===ct)},Bt=function(A){switch(A.filter(We).map((function(A){return A.value})).join(" ")){case"to bottom right":case"to right bottom":case"left top":case"top left":return[nt,nt];case"to top":case"bottom":return gt(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[nt,ot];case"to right":case"left":return gt(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[ot,ot];case"to bottom":case"top":return gt(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[ot,nt];case"to left":case"right":return gt(270)}return 0},gt=function(A){return Math.PI*A/180},pt=function(A,e){if(18===e.type){var t=Ft[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported color function "'+e.name+'"');return t(A,e.values)}if(5===e.type){if(3===e.value.length){var r=e.value.substring(0,1),n=e.value.substring(1,2),s=e.value.substring(2,3);return ht(parseInt(r+r,16),parseInt(n+n,16),parseInt(s+s,16),1)}if(4===e.value.length){r=e.value.substring(0,1),n=e.value.substring(1,2),s=e.value.substring(2,3);var o=e.value.substring(3,4);return ht(parseInt(r+r,16),parseInt(n+n,16),parseInt(s+s,16),parseInt(o+o,16)/255)}if(6===e.value.length)return r=e.value.substring(0,2),n=e.value.substring(2,4),s=e.value.substring(4,6),ht(parseInt(r,16),parseInt(n,16),parseInt(s,16),1);if(8===e.value.length)return r=e.value.substring(0,2),n=e.value.substring(2,4),s=e.value.substring(4,6),o=e.value.substring(6,8),ht(parseInt(r,16),parseInt(n,16),parseInt(s,16),parseInt(o,16)/255)}if(20===e.type){var a=_t[e.value.toUpperCase()];if(void 0!==a)return a}return _t.TRANSPARENT},ft=function(A){return!(255&A)},wt=function(A){var e=255&A,t=255&A>>8,r=255&A>>16,n=255&A>>24;return e<255?"rgba("+n+","+r+","+t+","+e/255+")":"rgb("+n+","+r+","+t+")"},ht=function(A,e,t,r){return(A<<24|e<<16|t<<8|Math.round(255*r))>>>0},mt=function(A,e){if(17===A.type)return A.number;if(16===A.type){var t=3===e?1:255;return 3===e?A.number/100*t:Math.round(A.number/100*t)}return 0},Qt=function(A,e){var t=e.filter(qe);if(3===t.length){var r=t.map(mt),n=r[0],s=r[1],o=r[2];return ht(n,s,o,1)}if(4===t.length){var a=t.map(mt),i=(n=a[0],s=a[1],o=a[2],a[3]);return ht(n,s,o,i)}return 0};function yt(A,e,t){return t<0&&(t+=1),t>=1&&(t-=1),t<1/6?(e-A)*t*6+A:t<.5?e:t<2/3?6*(e-A)*(2/3-t)+A:A}var Ct,Ut,vt=function(A,e){var t=e.filter(qe),r=t[0],n=t[1],s=t[2],o=t[3],a=(17===r.type?gt(r.number):ut(A,r))/(2*Math.PI),i=tt(n)?n.number/100:0,l=tt(s)?s.number/100:0,c=void 0!==o&&tt(o)?it(o,1):1;if(0===i)return ht(255*l,255*l,255*l,1);var u=l<=.5?l*(i+1):l+i-l*i,d=2*l-u,B=yt(d,u,a+1/3),g=yt(d,u,a),p=yt(d,u,a-1/3);return ht(255*B,255*g,255*p,c)},Ft={hsl:vt,hsla:vt,rgb:Qt,rgba:Qt},bt=function(A,e){return pt(A,Je.create(e).parseComponentValue())},_t={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},xt={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(A,e){return e.map((function(A){if(We(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0}))}},Et={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Ht=function(A,e){var t=pt(A,e[0]),r=e[1];return r&&tt(r)?{color:t,stop:r}:{color:t,stop:null}},It=function(A,e){var t=A[0],r=A[A.length-1];null===t.stop&&(t.stop=nt),null===r.stop&&(r.stop=ot);for(var n=[],s=0,o=0;o<A.length;o++){var a=A[o].stop;if(null!==a){var i=it(a,e);i>s?n.push(i):n.push(s),s=i}else n.push(null)}var l=null;for(o=0;o<n.length;o++){var c=n[o];if(null===c)null===l&&(l=o);else if(null!==l){for(var u=o-l,d=(c-n[l-1])/(u+1),B=1;B<=u;B++)n[l+B-1]=d*B;l=null}}return A.map((function(A,t){return{color:A.color,stop:Math.max(Math.min(1,n[t]/e),0)}}))},kt=function(A,e,t){var r="number"==typeof A?A:function(A,e,t){var r=e/2,n=t/2,s=it(A[0],e)-r,o=n-it(A[1],t);return(Math.atan2(o,s)+2*Math.PI)%(2*Math.PI)}(A,e,t),n=Math.abs(e*Math.sin(r))+Math.abs(t*Math.cos(r)),s=e/2,o=t/2,a=n/2,i=Math.sin(r-Math.PI/2)*a,l=Math.cos(r-Math.PI/2)*a;return[n,s-l,s+l,o-i,o+i]},Lt=function(A,e){return Math.sqrt(A*A+e*e)},St=function(A,e,t,r,n){return[[0,0],[0,e],[A,0],[A,e]].reduce((function(A,e){var s=e[0],o=e[1],a=Lt(t-s,r-o);return(n?a<A.optimumDistance:a>A.optimumDistance)?{optimumCorner:e,optimumDistance:a}:A}),{optimumDistance:n?1/0:-1/0,optimumCorner:null}).optimumCorner},Kt=function(A,e){var t=gt(180),r=[];return $e(e).forEach((function(e,n){if(0===n){var s=e[0];if(20===s.type&&-1!==["top","left","right","bottom"].indexOf(s.value))return void(t=Bt(e));if(dt(s))return void(t=(ut(A,s)+gt(270))%gt(360))}var o=Ht(A,e);r.push(o)})),{angle:t,stops:r,type:1}},Dt="closest-side",Tt="farthest-side",Ot="closest-corner",Mt="farthest-corner",Rt="circle",Pt="ellipse",Nt="cover",Vt="contain",Gt=function(A,e){var t=0,r=3,n=[],s=[];return $e(e).forEach((function(e,o){var a=!0;if(0===o?a=e.reduce((function(A,e){if(We(e))switch(e.value){case"center":return s.push(st),!1;case"top":case"left":return s.push(nt),!1;case"right":case"bottom":return s.push(ot),!1}else if(tt(e)||et(e))return s.push(e),!1;return A}),a):1===o&&(a=e.reduce((function(A,e){if(We(e))switch(e.value){case Rt:return t=0,!1;case Pt:return t=1,!1;case Vt:case Dt:return r=0,!1;case Tt:return r=1,!1;case Ot:return r=2,!1;case Nt:case Mt:return r=3,!1}else if(et(e)||tt(e))return Array.isArray(r)||(r=[]),r.push(e),!1;return A}),a)),a){var i=Ht(A,e);n.push(i)}})),{size:r,shape:t,stops:n,position:s,type:2}},Jt=function(A,e){if(22===e.type){var t={url:e.value,type:0};return A.cache.addImage(e.value),t}if(18===e.type){var r=Xt[e.name];if(void 0===r)throw new Error('Attempting to parse an unsupported image function "'+e.name+'"');return r(A,e.values)}throw new Error("Unsupported image type "+e.type)},Xt={"linear-gradient":function(A,e){var t=gt(180),r=[];return $e(e).forEach((function(e,n){if(0===n){var s=e[0];if(20===s.type&&"to"===s.value)return void(t=Bt(e));if(dt(s))return void(t=ut(A,s))}var o=Ht(A,e);r.push(o)})),{angle:t,stops:r,type:1}},"-moz-linear-gradient":Kt,"-ms-linear-gradient":Kt,"-o-linear-gradient":Kt,"-webkit-linear-gradient":Kt,"radial-gradient":function(A,e){var t=0,r=3,n=[],s=[];return $e(e).forEach((function(e,o){var a=!0;if(0===o){var i=!1;a=e.reduce((function(A,e){if(i)if(We(e))switch(e.value){case"center":return s.push(st),A;case"top":case"left":return s.push(nt),A;case"right":case"bottom":return s.push(ot),A}else(tt(e)||et(e))&&s.push(e);else if(We(e))switch(e.value){case Rt:return t=0,!1;case Pt:return t=1,!1;case"at":return i=!0,!1;case Dt:return r=0,!1;case Nt:case Tt:return r=1,!1;case Vt:case Ot:return r=2,!1;case Mt:return r=3,!1}else if(et(e)||tt(e))return Array.isArray(r)||(r=[]),r.push(e),!1;return A}),a)}if(a){var l=Ht(A,e);n.push(l)}})),{size:r,shape:t,stops:n,position:s,type:2}},"-moz-radial-gradient":Gt,"-ms-radial-gradient":Gt,"-o-radial-gradient":Gt,"-webkit-radial-gradient":Gt,"-webkit-gradient":function(A,e){var t=gt(180),r=[],n=1;return $e(e).forEach((function(e,t){var s=e[0];if(0===t){if(We(s)&&"linear"===s.value)return void(n=1);if(We(s)&&"radial"===s.value)return void(n=2)}if(18===s.type)if("from"===s.name){var o=pt(A,s.values[0]);r.push({stop:nt,color:o})}else if("to"===s.name)o=pt(A,s.values[0]),r.push({stop:ot,color:o});else if("color-stop"===s.name){var a=s.values.filter(qe);if(2===a.length){o=pt(A,a[1]);var i=a[0];je(i)&&r.push({stop:{type:16,number:100*i.number,flags:i.flags},color:o})}}})),1===n?{angle:(t+gt(180))%gt(360),stops:r,type:n}:{size:3,shape:0,stops:r,position:[],type:n}}},jt={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(A,e){if(0===e.length)return[];var t=e[0];return 20===t.type&&"none"===t.value?[]:e.filter((function(A){return qe(A)&&function(A){return!(20===A.type&&"none"===A.value||18===A.type&&!Xt[A.name])}(A)})).map((function(e){return Jt(A,e)}))}},Wt={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(A,e){return e.map((function(A){if(We(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0}))}},Yt={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(A,e){return $e(e).map((function(A){return A.filter(tt)})).map(rt)}},Zt={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(A,e){return $e(e).map((function(A){return A.filter(We).map((function(A){return A.value})).join(" ")})).map(zt)}},zt=function(A){switch(A){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;default:return 0}};(Ut=Ct||(Ct={})).AUTO="auto",Ut.CONTAIN="contain",Ut.COVER="cover";var qt,$t,Ar={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(A,e){return $e(e).map((function(A){return A.filter(er)}))}},er=function(A){return We(A)||tt(A)},tr=function(A){return{name:"border-"+A+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},rr=tr("top"),nr=tr("right"),sr=tr("bottom"),or=tr("left"),ar=function(A){return{name:"border-radius-"+A,initialValue:"0 0",prefix:!1,type:1,parse:function(A,e){return rt(e.filter(tt))}}},ir=ar("top-left"),lr=ar("top-right"),cr=ar("bottom-right"),ur=ar("bottom-left"),dr=function(A){return{name:"border-"+A+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,e){switch(e){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},Br=dr("top"),gr=dr("right"),pr=dr("bottom"),fr=dr("left"),wr=function(A){return{name:"border-"+A+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,e){return Xe(e)?e.number:0}}},hr=wr("top"),mr=wr("right"),Qr=wr("bottom"),yr=wr("left"),Cr={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Ur={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(A,e){return"rtl"===e?1:0}},vr={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(A,e){return e.filter(We).reduce((function(A,e){return A|Fr(e.value)}),0)}},Fr=function(A){switch(A){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},br={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},_r={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(A,e){return 20===e.type&&"normal"===e.value?0:17===e.type||15===e.type?e.number:0}};($t=qt||(qt={})).NORMAL="normal",$t.STRICT="strict";var xr,Er,Hr={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){return"strict"===e?qt.STRICT:qt.NORMAL}},Ir={name:"line-height",initialValue:"normal",prefix:!1,type:4},kr=function(A,e){return We(A)&&"normal"===A.value?1.2*e:17===A.type?e*A.number:tt(A)?it(A,e):e},Lr={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(A,e){return 20===e.type&&"none"===e.value?null:Jt(A,e)}},Sr={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(A,e){return"inside"===e?0:1}},Kr={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;default:return-1}}},Dr=function(A){return{name:"margin-"+A,initialValue:"0",prefix:!1,type:4}},Tr=Dr("top"),Or=Dr("right"),Mr=Dr("bottom"),Rr=Dr("left"),Pr={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(A,e){return e.filter(We).map((function(A){switch(A.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;default:return 0}}))}},Nr={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){return"break-word"===e?"break-word":"normal"}},Vr=function(A){return{name:"padding-"+A,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},Gr=Vr("top"),Jr=Vr("right"),Xr=Vr("bottom"),jr=Vr("left"),Wr={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(A,e){switch(e){case"right":return 2;case"center":case"justify":return 1;default:return 0}}},Yr={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(A,e){switch(e){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},Zr={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return 1===e.length&&Ze(e[0],"none")?[]:$e(e).map((function(e){for(var t={color:_t.TRANSPARENT,offsetX:nt,offsetY:nt,blur:nt},r=0,n=0;n<e.length;n++){var s=e[n];et(s)?(0===r?t.offsetX=s:1===r?t.offsetY=s:t.blur=s,r++):t.color=pt(A,s)}return t}))}},zr={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},qr={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(A,e){if(20===e.type&&"none"===e.value)return null;if(18===e.type){var t=$r[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported transform function "'+e.name+'"');return t(e.values)}return null}},$r={matrix:function(A){var e=A.filter((function(A){return 17===A.type})).map((function(A){return A.number}));return 6===e.length?e:null},matrix3d:function(A){var e=A.filter((function(A){return 17===A.type})).map((function(A){return A.number})),t=e[0],r=e[1];e[2],e[3];var n=e[4],s=e[5];e[6],e[7],e[8],e[9],e[10],e[11];var o=e[12],a=e[13];return e[14],e[15],16===e.length?[t,r,n,s,o,a]:null}},An={type:16,number:50,flags:4},en=[An,An],tn={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(A,e){var t=e.filter(tt);return 2!==t.length?en:[t[0],t[1]]}},rn={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"hidden":return 1;case"collapse":return 2;default:return 0}}};(Er=xr||(xr={})).NORMAL="normal",Er.BREAK_ALL="break-all",Er.KEEP_ALL="keep-all";for(var nn={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){switch(e){case"break-all":return xr.BREAK_ALL;case"keep-all":return xr.KEEP_ALL;default:return xr.NORMAL}}},sn={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(A,e){if(20===e.type)return{auto:!0,order:0};if(je(e))return{auto:!1,order:e.number};throw new Error("Invalid z-index number parsed")}},on=function(A,e){if(15===e.type)switch(e.unit.toLowerCase()){case"s":return 1e3*e.number;case"ms":return e.number}throw new Error("Unsupported time type")},an={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(A,e){return je(e)?e.number:1}},ln={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},cn={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(A,e){return e.filter(We).map((function(A){switch(A.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0})).filter((function(A){return 0!==A}))}},un={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(A,e){var t=[],r=[];return e.forEach((function(A){switch(A.type){case 20:case 0:t.push(A.value);break;case 17:t.push(A.number.toString());break;case 4:r.push(t.join(" ")),t.length=0}})),t.length&&r.push(t.join(" ")),r.map((function(A){return-1===A.indexOf(" ")?A:"'"+A+"'"}))}},dn={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},Bn={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(A,e){return je(e)?e.number:We(e)&&"bold"===e.value?700:400}},gn={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return e.filter(We).map((function(A){return A.value}))}},pn={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){switch(e){case"oblique":return"oblique";case"italic":return"italic";default:return"normal"}}},fn=function(A,e){return 0!==(A&e)},wn={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(A,e){if(0===e.length)return[];var t=e[0];return 20===t.type&&"none"===t.value?[]:e}},hn={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(20===t.type&&"none"===t.value)return null;for(var r=[],n=e.filter(ze),s=0;s<n.length;s++){var o=n[s],a=n[s+1];if(20===o.type){var i=a&&je(a)?a.number:1;r.push({counter:o.value,increment:i})}}return r}},mn={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return[];for(var t=[],r=e.filter(ze),n=0;n<r.length;n++){var s=r[n],o=r[n+1];if(We(s)&&"none"!==s.value){var a=o&&je(o)?o.number:0;t.push({counter:s.value,reset:a})}}return t}},Qn={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(A,e){return e.filter(Xe).map((function(e){return on(A,e)}))}},yn={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(20===t.type&&"none"===t.value)return null;var r=[],n=e.filter(Ye);if(n.length%2!=0)return null;for(var s=0;s<n.length;s+=2){var o=n[s].value,a=n[s+1].value;r.push({open:o,close:a})}return r}},Cn=function(A,e,t){if(!A)return"";var r=A[Math.min(e,A.length-1)];return r?t?r.open:r.close:""},Un={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return 1===e.length&&Ze(e[0],"none")?[]:$e(e).map((function(e){for(var t={color:255,offsetX:nt,offsetY:nt,blur:nt,spread:nt,inset:!1},r=0,n=0;n<e.length;n++){var s=e[n];Ze(s,"inset")?t.inset=!0:et(s)?(0===r?t.offsetX=s:1===r?t.offsetY=s:2===r?t.blur=s:t.spread=s,r++):t.color=pt(A,s)}return t}))}},vn={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(A,e){var t=[];return e.filter(We).forEach((function(A){switch(A.value){case"stroke":t.push(1);break;case"fill":t.push(0);break;case"markers":t.push(2)}})),[0,1,2].forEach((function(A){-1===t.indexOf(A)&&t.push(A)})),t}},Fn={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},bn={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(A,e){return Xe(e)?e.number:0}},_n=function(){function A(A,e){var t,r;this.animationDuration=Hn(A,Qn,e.animationDuration),this.backgroundClip=Hn(A,xt,e.backgroundClip),this.backgroundColor=Hn(A,Et,e.backgroundColor),this.backgroundImage=Hn(A,jt,e.backgroundImage),this.backgroundOrigin=Hn(A,Wt,e.backgroundOrigin),this.backgroundPosition=Hn(A,Yt,e.backgroundPosition),this.backgroundRepeat=Hn(A,Zt,e.backgroundRepeat),this.backgroundSize=Hn(A,Ar,e.backgroundSize),this.borderTopColor=Hn(A,rr,e.borderTopColor),this.borderRightColor=Hn(A,nr,e.borderRightColor),this.borderBottomColor=Hn(A,sr,e.borderBottomColor),this.borderLeftColor=Hn(A,or,e.borderLeftColor),this.borderTopLeftRadius=Hn(A,ir,e.borderTopLeftRadius),this.borderTopRightRadius=Hn(A,lr,e.borderTopRightRadius),this.borderBottomRightRadius=Hn(A,cr,e.borderBottomRightRadius),this.borderBottomLeftRadius=Hn(A,ur,e.borderBottomLeftRadius),this.borderTopStyle=Hn(A,Br,e.borderTopStyle),this.borderRightStyle=Hn(A,gr,e.borderRightStyle),this.borderBottomStyle=Hn(A,pr,e.borderBottomStyle),this.borderLeftStyle=Hn(A,fr,e.borderLeftStyle),this.borderTopWidth=Hn(A,hr,e.borderTopWidth),this.borderRightWidth=Hn(A,mr,e.borderRightWidth),this.borderBottomWidth=Hn(A,Qr,e.borderBottomWidth),this.borderLeftWidth=Hn(A,yr,e.borderLeftWidth),this.boxShadow=Hn(A,Un,e.boxShadow),this.color=Hn(A,Cr,e.color),this.direction=Hn(A,Ur,e.direction),this.display=Hn(A,vr,e.display),this.float=Hn(A,br,e.cssFloat),this.fontFamily=Hn(A,un,e.fontFamily),this.fontSize=Hn(A,dn,e.fontSize),this.fontStyle=Hn(A,pn,e.fontStyle),this.fontVariant=Hn(A,gn,e.fontVariant),this.fontWeight=Hn(A,Bn,e.fontWeight),this.letterSpacing=Hn(A,_r,e.letterSpacing),this.lineBreak=Hn(A,Hr,e.lineBreak),this.lineHeight=Hn(A,Ir,e.lineHeight),this.listStyleImage=Hn(A,Lr,e.listStyleImage),this.listStylePosition=Hn(A,Sr,e.listStylePosition),this.listStyleType=Hn(A,Kr,e.listStyleType),this.marginTop=Hn(A,Tr,e.marginTop),this.marginRight=Hn(A,Or,e.marginRight),this.marginBottom=Hn(A,Mr,e.marginBottom),this.marginLeft=Hn(A,Rr,e.marginLeft),this.opacity=Hn(A,an,e.opacity);var n=Hn(A,Pr,e.overflow);this.overflowX=n[0],this.overflowY=n[n.length>1?1:0],this.overflowWrap=Hn(A,Nr,e.overflowWrap),this.paddingTop=Hn(A,Gr,e.paddingTop),this.paddingRight=Hn(A,Jr,e.paddingRight),this.paddingBottom=Hn(A,Xr,e.paddingBottom),this.paddingLeft=Hn(A,jr,e.paddingLeft),this.paintOrder=Hn(A,vn,e.paintOrder),this.position=Hn(A,Yr,e.position),this.textAlign=Hn(A,Wr,e.textAlign),this.textDecorationColor=Hn(A,ln,null!==(t=e.textDecorationColor)&&void 0!==t?t:e.color),this.textDecorationLine=Hn(A,cn,null!==(r=e.textDecorationLine)&&void 0!==r?r:e.textDecoration),this.textShadow=Hn(A,Zr,e.textShadow),this.textTransform=Hn(A,zr,e.textTransform),this.transform=Hn(A,qr,e.transform),this.transformOrigin=Hn(A,tn,e.transformOrigin),this.visibility=Hn(A,rn,e.visibility),this.webkitTextStrokeColor=Hn(A,Fn,e.webkitTextStrokeColor),this.webkitTextStrokeWidth=Hn(A,bn,e.webkitTextStrokeWidth),this.wordBreak=Hn(A,nn,e.wordBreak),this.zIndex=Hn(A,sn,e.zIndex)}return A.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&0===this.visibility},A.prototype.isTransparent=function(){return ft(this.backgroundColor)},A.prototype.isTransformed=function(){return null!==this.transform},A.prototype.isPositioned=function(){return 0!==this.position},A.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},A.prototype.isFloating=function(){return 0!==this.float},A.prototype.isInlineLevel=function(){return fn(this.display,4)||fn(this.display,33554432)||fn(this.display,268435456)||fn(this.display,536870912)||fn(this.display,67108864)||fn(this.display,134217728)},A}(),xn=function(){return function(A,e){this.content=Hn(A,wn,e.content),this.quotes=Hn(A,yn,e.quotes)}}(),En=function(){return function(A,e){this.counterIncrement=Hn(A,hn,e.counterIncrement),this.counterReset=Hn(A,mn,e.counterReset)}}(),Hn=function(A,e,t){var r=new Ge,n=null!=t?t.toString():e.initialValue;r.write(n);var s=new Je(r.read());switch(e.type){case 2:var o=s.parseComponentValue();return e.parse(A,We(o)?o.value:e.initialValue);case 0:return e.parse(A,s.parseComponentValue());case 1:return e.parse(A,s.parseComponentValues());case 4:return s.parseComponentValue();case 3:switch(e.format){case"angle":return ut(A,s.parseComponentValue());case"color":return pt(A,s.parseComponentValue());case"image":return Jt(A,s.parseComponentValue());case"length":var a=s.parseComponentValue();return et(a)?a:nt;case"length-percentage":var i=s.parseComponentValue();return tt(i)?i:nt;case"time":return on(A,s.parseComponentValue())}}},In=function(A,e){var t=function(A){switch(A.getAttribute("data-html2canvas-debug")){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}}(A);return 1===t||e===t},kn=function(){return function(A,e){this.context=A,this.textNodes=[],this.elements=[],this.flags=0,In(e,3),this.styles=new _n(A,window.getComputedStyle(e,null)),Ts(e)&&(this.styles.animationDuration.some((function(A){return A>0}))&&(e.style.animationDuration="0s"),null!==this.styles.transform&&(e.style.transform="none")),this.bounds=i(this.context,e),In(e,4)&&(this.flags|=16)}}(),Ln="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Sn="undefined"==typeof Uint8Array?[]:new Uint8Array(256),Kn=0;Kn<64;Kn++)Sn[Ln.charCodeAt(Kn)]=Kn;for(var Dn=function(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))},Tn=function(){function A(A,e,t,r,n,s){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=r,this.index=n,this.data=s}return A.prototype.get=function(A){var e;if(A>=0){if(A<55296||A>56319&&A<=65535)return e=((e=this.index[A>>5])<<2)+(31&A),this.data[e];if(A<=65535)return e=((e=this.index[2048+(A-55296>>5)])<<2)+(31&A),this.data[e];if(A<this.highStart)return e=2080+(A>>11),e=this.index[e],e+=A>>5&63,e=((e=this.index[e])<<2)+(31&A),this.data[e];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),On="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Mn="undefined"==typeof Uint8Array?[]:new Uint8Array(256),Rn=0;Rn<64;Rn++)Mn[On.charCodeAt(Rn)]=Rn;var Pn,Nn,Vn=8,Gn=9,Jn=11,Xn=12,jn=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var r=[],n=-1,s="";++n<t;){var o=A[n];o<=65535?r.push(o):(o-=65536,r.push(55296+(o>>10),o%1024+56320)),(n+1===t||r.length>16384)&&(s+=String.fromCharCode.apply(String,r),r.length=0)}return s},Wn=function(A){var e=function(A){var e,t,r,n,s,o=.75*A.length,a=A.length,i=0;"="===A[A.length-1]&&(o--,"="===A[A.length-2]&&o--);var l="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?new ArrayBuffer(o):new Array(o),c=Array.isArray(l)?l:new Uint8Array(l);for(e=0;e<a;e+=4)t=Sn[A.charCodeAt(e)],r=Sn[A.charCodeAt(e+1)],n=Sn[A.charCodeAt(e+2)],s=Sn[A.charCodeAt(e+3)],c[i++]=t<<2|r>>4,c[i++]=(15&r)<<4|n>>2,c[i++]=(3&n)<<6|63&s;return l}(A),t=Array.isArray(e)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t}(e):new Uint32Array(e),r=Array.isArray(e)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t}(e):new Uint16Array(e),n=Dn(r,12,t[4]/2),s=2===t[5]?Dn(r,(24+t[4])/2):function(A,e,t){return A.slice?A.slice(e,t):new Uint32Array(Array.prototype.slice.call(A,e,t))}(t,Math.ceil((24+t[4])/4));return new Tn(t[0],t[1],t[2],t[3],n,s)}("AAAAAAAAAAAAEA4AGBkAAFAaAAACAAAAAAAIABAAGAAwADgACAAQAAgAEAAIABAACAAQAAgAEAAIABAACAAQAAgAEAAIABAAQABIAEQATAAIABAACAAQAAgAEAAIABAAVABcAAgAEAAIABAACAAQAGAAaABwAHgAgACIAI4AlgAIABAAmwCjAKgAsAC2AL4AvQDFAMoA0gBPAVYBWgEIAAgACACMANoAYgFkAWwBdAF8AX0BhQGNAZUBlgGeAaMBlQGWAasBswF8AbsBwwF0AcsBYwHTAQgA2wG/AOMBdAF8AekB8QF0AfkB+wHiAHQBfAEIAAMC5gQIAAsCEgIIAAgAFgIeAggAIgIpAggAMQI5AkACygEIAAgASAJQAlgCYAIIAAgACAAKBQoFCgUTBRMFGQUrBSsFCAAIAAgACAAIAAgACAAIAAgACABdAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACABoAmgCrwGvAQgAbgJ2AggAHgEIAAgACADnAXsCCAAIAAgAgwIIAAgACAAIAAgACACKAggAkQKZAggAPADJAAgAoQKkAqwCsgK6AsICCADJAggA0AIIAAgACAAIANYC3gIIAAgACAAIAAgACABAAOYCCAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAkASoB+QIEAAgACAA8AEMCCABCBQgACABJBVAFCAAIAAgACAAIAAgACAAIAAgACABTBVoFCAAIAFoFCABfBWUFCAAIAAgACAAIAAgAbQUIAAgACAAIAAgACABzBXsFfQWFBYoFigWKBZEFigWKBYoFmAWfBaYFrgWxBbkFCAAIAAgACAAIAAgACAAIAAgACAAIAMEFCAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAMgFCADQBQgACAAIAAgACAAIAAgACAAIAAgACAAIAO4CCAAIAAgAiQAIAAgACABAAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAD0AggACAD8AggACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIANYFCAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAMDvwAIAAgAJAIIAAgACAAIAAgACAAIAAgACwMTAwgACAB9BOsEGwMjAwgAKwMyAwsFYgE3A/MEPwMIAEUDTQNRAwgAWQOsAGEDCAAIAAgACAAIAAgACABpAzQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFIQUoBSwFCAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACABtAwgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACABMAEwACAAIAAgACAAIABgACAAIAAgACAC/AAgACAAyAQgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACACAAIAAwAAgACAAIAAgACAAIAAgACAAIAAAARABIAAgACAAIABQASAAIAAgAIABwAEAAjgCIABsAqAC2AL0AigDQAtwC+IJIQqVAZUBWQqVAZUBlQGVAZUBlQGrC5UBlQGVAZUBlQGVAZUBlQGVAXsKlQGVAbAK6wsrDGUMpQzlDJUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAfAKAAuZA64AtwCJALoC6ADwAAgAuACgA/oEpgO6AqsD+AAIAAgAswMIAAgACAAIAIkAuwP5AfsBwwPLAwgACAAIAAgACADRA9kDCAAIAOED6QMIAAgACAAIAAgACADuA/YDCAAIAP4DyQAIAAgABgQIAAgAXQAOBAgACAAIAAgACAAIABMECAAIAAgACAAIAAgACAD8AAQBCAAIAAgAGgQiBCoECAExBAgAEAEIAAgACAAIAAgACAAIAAgACAAIAAgACAA4BAgACABABEYECAAIAAgATAQYAQgAVAQIAAgACAAIAAgACAAIAAgACAAIAFoECAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgAOQEIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAB+BAcACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAEABhgSMBAgACAAIAAgAlAQIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAwAEAAQABAADAAMAAwADAAQABAAEAAQABAAEAAQABHATAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgAdQMIAAgACAAIAAgACAAIAMkACAAIAAgAfQMIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACACFA4kDCAAIAAgACAAIAOcBCAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAIcDCAAIAAgACAAIAAgACAAIAAgACAAIAJEDCAAIAAgACADFAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACABgBAgAZgQIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgAbAQCBXIECAAIAHkECAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACABAAJwEQACjBKoEsgQIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAC6BMIECAAIAAgACAAIAAgACABmBAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgAxwQIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAGYECAAIAAgAzgQIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgAigWKBYoFigWKBYoFigWKBd0FXwUIAOIF6gXxBYoF3gT5BQAGCAaKBYoFigWKBYoFigWKBYoFigWKBYoFigXWBIoFigWKBYoFigWKBYoFigWKBYsFEAaKBYoFigWKBYoFigWKBRQGCACKBYoFigWKBQgACAAIANEECAAIABgGigUgBggAJgYIAC4GMwaKBYoF0wQ3Bj4GigWKBYoFigWKBYoFigWKBYoFigWKBYoFigUIAAgACAAIAAgACAAIAAgAigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWLBf///////wQABAAEAAQABAAEAAQABAAEAAQAAwAEAAQAAgAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAQADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAUAAAAFAAUAAAAFAAUAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAEAAQABAAEAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUAAQAAAAUABQAFAAUABQAFAAAAAAAFAAUAAAAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAFAAUAAQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABwAFAAUABQAFAAAABwAHAAcAAAAHAAcABwAFAAEAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAFAAUABQAFAAcABwAFAAUAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAAAAQABAAAAAAAAAAAAAAAFAAUABQAFAAAABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAHAAcABwAHAAcAAAAHAAcAAAAAAAUABQAHAAUAAQAHAAEABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABwABAAUABQAFAAUAAAAAAAAAAAAAAAEAAQABAAEAAQABAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABwAFAAUAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUAAQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQABQANAAQABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQABAAEAAQABAAEAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAEAAQABAAEAAQABAAEAAQABAAEAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAQABAAEAAQABAAEAAQABAAAAAAAAAAAAAAAAAAAAAAABQAHAAUABQAFAAAAAAAAAAcABQAFAAUABQAFAAQABAAEAAQABAAEAAQABAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUAAAAFAAUABQAFAAUAAAAFAAUABQAAAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAAAAAAAAAAAAUABQAFAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAHAAUAAAAHAAcABwAFAAUABQAFAAUABQAFAAUABwAHAAcABwAFAAcABwAAAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABwAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAUABwAHAAUABQAFAAUAAAAAAAcABwAAAAAABwAHAAUAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAABQAFAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAABwAHAAcABQAFAAAAAAAAAAAABQAFAAAAAAAFAAUABQAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAFAAUABQAFAAUAAAAFAAUABwAAAAcABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAFAAUABwAFAAUABQAFAAAAAAAHAAcAAAAAAAcABwAFAAAAAAAAAAAAAAAAAAAABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAcABwAAAAAAAAAHAAcABwAAAAcABwAHAAUAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAABQAHAAcABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABwAHAAcABwAAAAUABQAFAAAABQAFAAUABQAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAcABQAHAAcABQAHAAcAAAAFAAcABwAAAAcABwAFAAUAAAAAAAAAAAAAAAAAAAAFAAUAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAUABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAFAAcABwAFAAUABQAAAAUAAAAHAAcABwAHAAcABwAHAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAHAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAABwAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAUAAAAFAAAAAAAAAAAABwAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABwAFAAUABQAFAAUAAAAFAAUAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABwAFAAUABQAFAAUABQAAAAUABQAHAAcABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABQAFAAAAAAAAAAAABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAcABQAFAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAHAAUABQAFAAUABQAFAAUABwAHAAcABwAHAAcABwAHAAUABwAHAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABwAHAAcABwAFAAUABwAHAAcAAAAAAAAAAAAHAAcABQAHAAcABwAHAAcABwAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAcABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABQAHAAUABQAFAAUABQAFAAUAAAAFAAAABQAAAAAABQAFAAUABQAFAAUABQAFAAcABwAHAAcABwAHAAUABQAFAAUABQAFAAUABQAFAAUAAAAAAAUABQAFAAUABQAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABwAFAAcABwAHAAcABwAFAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAUABQAFAAUABwAHAAUABQAHAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAcABQAFAAcABwAHAAUABwAFAAUABQAHAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAcABwAHAAcABwAHAAUABQAFAAUABQAFAAUABQAHAAcABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUAAAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAcABQAFAAUABQAFAAUABQAAAAAAAAAAAAUAAAAAAAAAAAAAAAAABQAAAAAABwAFAAUAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAAABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUAAAAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAABQAAAAAAAAAFAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAUABQAHAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABwAHAAcABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAFAAUABQAHAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAcABwAFAAUABQAFAAcABwAFAAUABwAHAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAcABwAFAAUABwAHAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAFAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAFAAUABQAAAAAABQAFAAAAAAAAAAAAAAAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABQAFAAcABwAAAAAAAAAAAAAABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABwAFAAcABwAFAAcABwAAAAcABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAAAAAAAAAAAAAAAAAFAAUABQAAAAUABQAAAAAAAAAAAAAABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABQAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABwAFAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAcABQAFAAUABQAFAAUABQAFAAUABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAFAAUABQAHAAcABQAHAAUABQAAAAAAAAAAAAAAAAAFAAAABwAHAAcABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABwAHAAcABwAAAAAABwAHAAAAAAAHAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAAAAAAFAAUABQAFAAUABQAFAAAAAAAAAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAFAAUABQAFAAUABQAFAAUABwAHAAUABQAFAAcABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAHAAcABQAFAAUABQAFAAUABwAFAAcABwAFAAcABQAFAAcABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAHAAcABQAFAAUABQAAAAAABwAHAAcABwAFAAUABwAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABwAHAAUABQAFAAUABQAFAAUABQAHAAcABQAHAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABwAFAAcABwAFAAUABQAFAAUABQAHAAUAAAAAAAAAAAAAAAAAAAAAAAcABwAFAAUABQAFAAcABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAFAAUABQAFAAUABQAFAAUABQAHAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAFAAUABQAFAAAAAAAFAAUABwAHAAcABwAFAAAAAAAAAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABwAHAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABQAFAAUABQAFAAUABQAAAAUABQAFAAUABQAFAAcABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAAAHAAUABQAFAAUABQAFAAUABwAFAAUABwAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUAAAAAAAAABQAAAAUABQAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAHAAcAAAAFAAUAAAAHAAcABQAHAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABwAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAAAAAAAAAAAAAAAAAAABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAUABQAFAAAAAAAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAAAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAAAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAAABQAFAAUABQAFAAUABQAAAAUABQAAAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAFAAUABQAFAAUADgAOAA4ADgAOAA4ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAAAAAAAAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAMAAwADAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkAAAAAAAAAAAAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAAAAAAAAAAAAsADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwACwAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAAAAAAAAAAAAAAAAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAOAAAAAAAAAAAADgAOAA4AAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAAAA4ADgAOAA4ADgAOAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AAAAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AAAAAAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAAAA4AAAAOAAAAAAAAAAAAAAAAAA4AAAAAAAAAAAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAADgAAAAAAAAAAAA4AAAAOAAAAAAAAAAAADgAOAA4AAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAA4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAAAAAAAA4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAAAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4AAAAAAA4ADgAOAA4ADgAOAA4ADgAOAAAADgAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4AAAAAAAAAAAAAAAAAAAAAAA4ADgAOAA4ADgAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AAAAOAA4ADgAOAA4ADgAAAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AAAAAAAAAAAA="),Yn="×",Zn=function(A){return Wn.get(A)},zn=function(A,e,t){var r=t-2,n=e[r],s=e[t-1],o=e[t];if(2===s&&3===o)return Yn;if(2===s||3===s||4===s)return"÷";if(2===o||3===o||4===o)return"÷";if(s===Vn&&-1!==[Vn,Gn,Jn,Xn].indexOf(o))return Yn;if(!(s!==Jn&&s!==Gn||o!==Gn&&10!==o))return Yn;if((s===Xn||10===s)&&10===o)return Yn;if(13===o||5===o)return Yn;if(7===o)return Yn;if(1===s)return Yn;if(13===s&&14===o){for(;5===n;)n=e[--r];if(14===n)return Yn}if(15===s&&15===o){for(var a=0;15===n;)a++,n=e[--r];if(a%2==0)return Yn}return"÷"},qn=function(A){var e=function(A){for(var e=[],t=0,r=A.length;t<r;){var n=A.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var s=A.charCodeAt(t++);56320==(64512&s)?e.push(((1023&n)<<10)+(1023&s)+65536):(e.push(n),t--)}else e.push(n)}return e}(A),t=e.length,r=0,n=0,s=e.map(Zn);return{next:function(){if(r>=t)return{done:!0,value:null};for(var A=Yn;r<t&&(A=zn(0,s,++r))===Yn;);if(A!==Yn||r===t){var o=jn.apply(null,e.slice(n,r));return n=r,{value:o,done:!1}}return{done:!0,value:null}}}},$n=function(A){return 0===A[0]&&255===A[1]&&0===A[2]&&255===A[3]},As=function(A,e,t,r,n){var s="http://www.w3.org/2000/svg",o=document.createElementNS(s,"svg"),a=document.createElementNS(s,"foreignObject");return o.setAttributeNS(null,"width",A.toString()),o.setAttributeNS(null,"height",e.toString()),a.setAttributeNS(null,"width","100%"),a.setAttributeNS(null,"height","100%"),a.setAttributeNS(null,"x",t.toString()),a.setAttributeNS(null,"y",r.toString()),a.setAttributeNS(null,"externalResourcesRequired","true"),o.appendChild(a),a.appendChild(n),o},es=function(A){return new Promise((function(e,t){var r=new Image;r.onload=function(){return e(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},ts={get SUPPORT_RANGE_BOUNDS(){var A=function(A){if(A.createRange){var e=A.createRange();if(e.getBoundingClientRect){var t=A.createElement("boundtest");t.style.height="123px",t.style.display="block",A.body.appendChild(t),e.selectNode(t);var r=e.getBoundingClientRect(),n=Math.round(r.height);if(A.body.removeChild(t),123===n)return!0}}return!1}(document);return Object.defineProperty(ts,"SUPPORT_RANGE_BOUNDS",{value:A}),A},get SUPPORT_WORD_BREAKING(){var A=ts.SUPPORT_RANGE_BOUNDS&&function(A){var e=A.createElement("boundtest");e.style.width="50px",e.style.display="block",e.style.fontSize="12px",e.style.letterSpacing="0px",e.style.wordSpacing="0px",A.body.appendChild(e);var t=A.createRange();e.innerHTML="function"==typeof"".repeat?"&#128104;".repeat(10):"";var r=e.firstChild,n=rA(r.data).map((function(A){return nA(A)})),s=0,o={},a=n.every((function(A,e){t.setStart(r,s),t.setEnd(r,s+A.length);var n=t.getBoundingClientRect();s+=A.length;var a=n.x>o.x||n.y>o.y;return o=n,0===e||a}));return A.body.removeChild(e),a}(document);return Object.defineProperty(ts,"SUPPORT_WORD_BREAKING",{value:A}),A},get SUPPORT_SVG_DRAWING(){var A=function(A){var e=new Image,t=A.createElement("canvas"),r=t.getContext("2d");if(!r)return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{r.drawImage(e,0,0),t.toDataURL()}catch(n){return!1}return!0}(document);return Object.defineProperty(ts,"SUPPORT_SVG_DRAWING",{value:A}),A},get SUPPORT_FOREIGNOBJECT_DRAWING(){var A="function"==typeof Array.from&&"function"==typeof window.fetch?function(A){var e=A.createElement("canvas"),t=100;e.width=t,e.height=t;var r=e.getContext("2d");if(!r)return Promise.reject(!1);r.fillStyle="rgb(0, 255, 0)",r.fillRect(0,0,t,t);var n=new Image,s=e.toDataURL();n.src=s;var o=As(t,t,0,0,n);return r.fillStyle="red",r.fillRect(0,0,t,t),es(o).then((function(e){r.drawImage(e,0,0);var n=r.getImageData(0,0,t,t).data;r.fillStyle="red",r.fillRect(0,0,t,t);var o=A.createElement("div");return o.style.backgroundImage="url("+s+")",o.style.height=t+"px",$n(n)?es(As(t,t,0,0,o)):Promise.reject(!1)})).then((function(A){return r.drawImage(A,0,0),$n(r.getImageData(0,0,t,t).data)})).catch((function(){return!1}))}(document):Promise.resolve(!1);return Object.defineProperty(ts,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:A}),A},get SUPPORT_CORS_IMAGES(){var A=void 0!==(new Image).crossOrigin;return Object.defineProperty(ts,"SUPPORT_CORS_IMAGES",{value:A}),A},get SUPPORT_RESPONSE_TYPE(){var A="string"==typeof(new XMLHttpRequest).responseType;return Object.defineProperty(ts,"SUPPORT_RESPONSE_TYPE",{value:A}),A},get SUPPORT_CORS_XHR(){var A="withCredentials"in new XMLHttpRequest;return Object.defineProperty(ts,"SUPPORT_CORS_XHR",{value:A}),A},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var A=!("undefined"==typeof Intl||!Intl.Segmenter);return Object.defineProperty(ts,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:A}),A}},rs=function(){return function(A,e){this.text=A,this.bounds=e}}(),ns=function(A,e){var t=e.ownerDocument;if(t){var r=t.createElement("html2canvaswrapper");r.appendChild(e.cloneNode(!0));var n=e.parentNode;if(n){n.replaceChild(r,e);var s=i(A,r);return r.firstChild&&n.replaceChild(r.firstChild,r),s}}return o.EMPTY},ss=function(A,e,t){var r=A.ownerDocument;if(!r)throw new Error("Node has no owner document");var n=r.createRange();return n.setStart(A,e),n.setEnd(A,e+t),n},os=function(A){if(ts.SUPPORT_NATIVE_TEXT_SEGMENTATION){var e=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(e.segment(A)).map((function(A){return A.segment}))}return function(A){for(var e,t=qn(A),r=[];!(e=t.next()).done;)e.value&&r.push(e.value.slice());return r}(A)},as=function(A,e){return 0!==e.letterSpacing?os(A):function(A,e){if(ts.SUPPORT_NATIVE_TEXT_SEGMENTATION){var t=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(t.segment(A)).map((function(A){return A.segment}))}return ls(A,e)}(A,e)},is=[32,160,4961,65792,65793,4153,4241],ls=function(A,e){for(var t,r=function(A,e){var t=rA(A),r=ie(t,e),n=r[0],s=r[1],o=r[2],a=t.length,i=0,l=0;return{next:function(){if(l>=a)return{done:!0,value:null};for(var A=YA;l<a&&(A=ae(t,s,n,++l,o))===YA;);if(A!==YA||l===a){var e=new le(t,A,i,l);return i=l,{value:e,done:!1}}return{done:!0,value:null}}}}(A,{lineBreak:e.lineBreak,wordBreak:"break-word"===e.overflowWrap?"break-word":e.wordBreak}),n=[],s=function(){if(t.value){var A=t.value.slice(),e=rA(A),r="";e.forEach((function(A){-1===is.indexOf(A)?r+=nA(A):(r.length&&n.push(r),n.push(nA(A)),r="")})),r.length&&n.push(r)}};!(t=r.next()).done;)s();return n},cs=function(){return function(A,e,t){this.text=us(e.data,t.textTransform),this.textBounds=function(A,e,t,r){var n=as(e,t),s=[],a=0;return n.forEach((function(e){if(t.textDecorationLine.length||e.trim().length>0)if(ts.SUPPORT_RANGE_BOUNDS){var n=ss(r,a,e.length).getClientRects();if(n.length>1){var i=os(e),l=0;i.forEach((function(e){s.push(new rs(e,o.fromDOMRectList(A,ss(r,l+a,e.length).getClientRects()))),l+=e.length}))}else s.push(new rs(e,o.fromDOMRectList(A,n)))}else{var c=r.splitText(e.length);s.push(new rs(e,ns(A,r))),r=c}else ts.SUPPORT_RANGE_BOUNDS||(r=r.splitText(e.length));a+=e.length})),s}(A,this.text,t,e)}}(),us=function(A,e){switch(e){case 1:return A.toLowerCase();case 3:return A.replace(ds,Bs);case 2:return A.toUpperCase();default:return A}},ds=/(^|\s|:|-|\(|\))([a-z])/g,Bs=function(A,e,t){return A.length>0?e+t.toUpperCase():A},gs=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.src=t.currentSrc||t.src,r.intrinsicWidth=t.naturalWidth,r.intrinsicHeight=t.naturalHeight,r.context.cache.addImage(r.src),r}return t(e,A),e}(kn),ps=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.canvas=t,r.intrinsicWidth=t.width,r.intrinsicHeight=t.height,r}return t(e,A),e}(kn),fs=function(A){function e(e,t){var r=A.call(this,e,t)||this,n=new XMLSerializer,s=i(e,t);return t.setAttribute("width",s.width+"px"),t.setAttribute("height",s.height+"px"),r.svg="data:image/svg+xml,"+encodeURIComponent(n.serializeToString(t)),r.intrinsicWidth=t.width.baseVal.value,r.intrinsicHeight=t.height.baseVal.value,r.context.cache.addImage(r.svg),r}return t(e,A),e}(kn),ws=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.value=t.value,r}return t(e,A),e}(kn),hs=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.start=t.start,r.reversed="boolean"==typeof t.reversed&&!0===t.reversed,r}return t(e,A),e}(kn),ms=[{type:15,flags:0,unit:"px",number:3}],Qs=[{type:16,flags:0,number:50}],ys="checkbox",Cs="radio",Us="password",vs=707406591,Fs=function(A){function e(e,t){var r,n,s,a=A.call(this,e,t)||this;switch(a.type=t.type.toLowerCase(),a.checked=t.checked,a.value=0===(n=(r=t).type===Us?new Array(r.value.length+1).join("•"):r.value).length?r.placeholder||"":n,a.type!==ys&&a.type!==Cs||(a.styles.backgroundColor=3739148031,a.styles.borderTopColor=a.styles.borderRightColor=a.styles.borderBottomColor=a.styles.borderLeftColor=2779096575,a.styles.borderTopWidth=a.styles.borderRightWidth=a.styles.borderBottomWidth=a.styles.borderLeftWidth=1,a.styles.borderTopStyle=a.styles.borderRightStyle=a.styles.borderBottomStyle=a.styles.borderLeftStyle=1,a.styles.backgroundClip=[0],a.styles.backgroundOrigin=[0],a.bounds=(s=a.bounds).width>s.height?new o(s.left+(s.width-s.height)/2,s.top,s.height,s.height):s.width<s.height?new o(s.left,s.top+(s.height-s.width)/2,s.width,s.width):s),a.type){case ys:a.styles.borderTopRightRadius=a.styles.borderTopLeftRadius=a.styles.borderBottomRightRadius=a.styles.borderBottomLeftRadius=ms;break;case Cs:a.styles.borderTopRightRadius=a.styles.borderTopLeftRadius=a.styles.borderBottomRightRadius=a.styles.borderBottomLeftRadius=Qs}return a}return t(e,A),e}(kn),bs=function(A){function e(e,t){var r=A.call(this,e,t)||this,n=t.options[t.selectedIndex||0];return r.value=n&&n.text||"",r}return t(e,A),e}(kn),_s=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.value=t.value,r}return t(e,A),e}(kn),xs=function(A){function e(e,t){var r=A.call(this,e,t)||this;r.src=t.src,r.width=parseInt(t.width,10)||0,r.height=parseInt(t.height,10)||0,r.backgroundColor=r.styles.backgroundColor;try{if(t.contentWindow&&t.contentWindow.document&&t.contentWindow.document.documentElement){r.tree=ks(e,t.contentWindow.document.documentElement);var n=t.contentWindow.document.documentElement?bt(e,getComputedStyle(t.contentWindow.document.documentElement).backgroundColor):_t.TRANSPARENT,s=t.contentWindow.document.body?bt(e,getComputedStyle(t.contentWindow.document.body).backgroundColor):_t.TRANSPARENT;r.backgroundColor=ft(n)?ft(s)?r.styles.backgroundColor:s:n}}catch(o){}return r}return t(e,A),e}(kn),Es=["OL","UL","MENU"],Hs=function(A,e,t,r){for(var n=e.firstChild,s=void 0;n;n=s)if(s=n.nextSibling,Ks(n)&&n.data.trim().length>0)t.textNodes.push(new cs(A,n,t.styles));else if(Ds(n))if(zs(n)&&n.assignedNodes)n.assignedNodes().forEach((function(e){return Hs(A,e,t,r)}));else{var o=Is(A,n);o.styles.isVisible()&&(Ls(n,o,r)?o.flags|=4:Ss(o.styles)&&(o.flags|=2),-1!==Es.indexOf(n.tagName)&&(o.flags|=8),t.elements.push(o),n.slot,n.shadowRoot?Hs(A,n.shadowRoot,o,r):Ys(n)||Ns(n)||Zs(n)||Hs(A,n,o,r))}},Is=function(A,e){return Xs(e)?new gs(A,e):Gs(e)?new ps(A,e):Ns(e)?new fs(A,e):Ms(e)?new ws(A,e):Rs(e)?new hs(A,e):Ps(e)?new Fs(A,e):Zs(e)?new bs(A,e):Ys(e)?new _s(A,e):js(e)?new xs(A,e):new kn(A,e)},ks=function(A,e){var t=Is(A,e);return t.flags|=4,Hs(A,e,t,t),t},Ls=function(A,e,t){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||Vs(A)&&t.styles.isTransparent()},Ss=function(A){return A.isPositioned()||A.isFloating()},Ks=function(A){return A.nodeType===Node.TEXT_NODE},Ds=function(A){return A.nodeType===Node.ELEMENT_NODE},Ts=function(A){return Ds(A)&&void 0!==A.style&&!Os(A)},Os=function(A){return"object"==typeof A.className},Ms=function(A){return"LI"===A.tagName},Rs=function(A){return"OL"===A.tagName},Ps=function(A){return"INPUT"===A.tagName},Ns=function(A){return"svg"===A.tagName},Vs=function(A){return"BODY"===A.tagName},Gs=function(A){return"CANVAS"===A.tagName},Js=function(A){return"VIDEO"===A.tagName},Xs=function(A){return"IMG"===A.tagName},js=function(A){return"IFRAME"===A.tagName},Ws=function(A){return"STYLE"===A.tagName},Ys=function(A){return"TEXTAREA"===A.tagName},Zs=function(A){return"SELECT"===A.tagName},zs=function(A){return"SLOT"===A.tagName},qs=function(A){return A.tagName.indexOf("-")>0},$s=function(){function A(){this.counters={}}return A.prototype.getCounterValue=function(A){var e=this.counters[A];return e&&e.length?e[e.length-1]:1},A.prototype.getCounterValues=function(A){var e=this.counters[A];return e||[]},A.prototype.pop=function(A){var e=this;A.forEach((function(A){return e.counters[A].pop()}))},A.prototype.parse=function(A){var e=this,t=A.counterIncrement,r=A.counterReset,n=!0;null!==t&&t.forEach((function(A){var t=e.counters[A.counter];t&&0!==A.increment&&(n=!1,t.length||t.push(1),t[Math.max(0,t.length-1)]+=A.increment)}));var s=[];return n&&r.forEach((function(A){var t=e.counters[A.counter];s.push(A.counter),t||(t=e.counters[A.counter]=[]),t.push(A.reset)})),s},A}(),Ao={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},eo={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},to={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},ro={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},no=function(A,e,t,r,n,s){return A<e||A>t?go(A,n,s.length>0):r.integers.reduce((function(e,t,n){for(;A>=t;)A-=t,e+=r.values[n];return e}),"")+s},so=function(A,e,t,r){var n="";do{t||A--,n=r(A)+n,A/=e}while(A*e>=e);return n},oo=function(A,e,t,r,n){var s=t-e+1;return(A<0?"-":"")+(so(Math.abs(A),s,r,(function(A){return nA(Math.floor(A%s)+e)}))+n)},ao=function(A,e,t){void 0===t&&(t=". ");var r=e.length;return so(Math.abs(A),r,!1,(function(A){return e[Math.floor(A%r)]}))+t},io=function(A,e,t,r,n,s){if(A<-9999||A>9999)return go(A,4,n.length>0);var o=Math.abs(A),a=n;if(0===o)return e[0]+a;for(var i=0;o>0&&i<=4;i++){var l=o%10;0===l&&fn(s,1)&&""!==a?a=e[l]+a:l>1||1===l&&0===i||1===l&&1===i&&fn(s,2)||1===l&&1===i&&fn(s,4)&&A>100||1===l&&i>1&&fn(s,8)?a=e[l]+(i>0?t[i-1]:"")+a:1===l&&i>0&&(a=t[i-1]+a),o=Math.floor(o/10)}return(A<0?r:"")+a},lo="十百千萬",co="拾佰仟萬",uo="マイナス",Bo="마이너스",go=function(A,e,t){var r=t?". ":"",n=t?"、":"",s=t?", ":"",o=t?" ":"";switch(e){case 0:return"•"+o;case 1:return"◦"+o;case 2:return"◾"+o;case 5:var a=oo(A,48,57,!0,r);return a.length<4?"0"+a:a;case 4:return ao(A,"〇一二三四五六七八九",n);case 6:return no(A,1,3999,Ao,3,r).toLowerCase();case 7:return no(A,1,3999,Ao,3,r);case 8:return oo(A,945,969,!1,r);case 9:return oo(A,97,122,!1,r);case 10:return oo(A,65,90,!1,r);case 11:return oo(A,1632,1641,!0,r);case 12:case 49:return no(A,1,9999,eo,3,r);case 35:return no(A,1,9999,eo,3,r).toLowerCase();case 13:return oo(A,2534,2543,!0,r);case 14:case 30:return oo(A,6112,6121,!0,r);case 15:return ao(A,"子丑寅卯辰巳午未申酉戌亥",n);case 16:return ao(A,"甲乙丙丁戊己庚辛壬癸",n);case 17:case 48:return io(A,"零一二三四五六七八九",lo,"負",n,14);case 47:return io(A,"零壹貳參肆伍陸柒捌玖",co,"負",n,15);case 42:return io(A,"零一二三四五六七八九",lo,"负",n,14);case 41:return io(A,"零壹贰叁肆伍陆柒捌玖",co,"负",n,15);case 26:return io(A,"〇一二三四五六七八九","十百千万",uo,n,0);case 25:return io(A,"零壱弐参四伍六七八九","拾百千万",uo,n,7);case 31:return io(A,"영일이삼사오육칠팔구","십백천만",Bo,s,7);case 33:return io(A,"零一二三四五六七八九","十百千萬",Bo,s,0);case 32:return io(A,"零壹貳參四五六七八九","拾百千",Bo,s,7);case 18:return oo(A,2406,2415,!0,r);case 20:return no(A,1,19999,ro,3,r);case 21:return oo(A,2790,2799,!0,r);case 22:return oo(A,2662,2671,!0,r);case 22:return no(A,1,10999,to,3,r);case 23:return ao(A,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case 24:return ao(A,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case 27:return oo(A,3302,3311,!0,r);case 28:return ao(A,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",n);case 29:return ao(A,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",n);case 34:return oo(A,3792,3801,!0,r);case 37:return oo(A,6160,6169,!0,r);case 38:return oo(A,4160,4169,!0,r);case 39:return oo(A,2918,2927,!0,r);case 40:return oo(A,1776,1785,!0,r);case 43:return oo(A,3046,3055,!0,r);case 44:return oo(A,3174,3183,!0,r);case 45:return oo(A,3664,3673,!0,r);case 46:return oo(A,3872,3881,!0,r);default:return oo(A,48,57,!0,r)}},po="data-html2canvas-ignore",fo=function(){function A(A,e,t){if(this.context=A,this.options=t,this.scrolledElements=[],this.referenceElement=e,this.counters=new $s,this.quoteDepth=0,!e.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(e.ownerDocument.documentElement,!1)}return A.prototype.toIFrame=function(A,e){var t=this,r=mo(A,e);if(!r.contentWindow)return Promise.reject("Unable to find iframe window");var o=A.defaultView.pageXOffset,a=A.defaultView.pageYOffset,i=r.contentWindow,l=i.document,c=Co(r).then((function(){return n(t,0,void 0,(function(){var A,t;return s(this,(function(n){switch(n.label){case 0:return this.scrolledElements.forEach(_o),i&&(i.scrollTo(e.left,e.top),!/(iPad|iPhone|iPod)/g.test(navigator.userAgent)||i.scrollY===e.top&&i.scrollX===e.left||(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(i.scrollX-e.left,i.scrollY-e.top,0,0))),A=this.options.onclone,void 0===(t=this.clonedReferenceElement)?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:l.fonts&&l.fonts.ready?[4,l.fonts.ready]:[3,2];case 1:n.sent(),n.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,yo(l)]:[3,4];case 3:n.sent(),n.label=4;case 4:return"function"==typeof A?[2,Promise.resolve().then((function(){return A(l,t)})).then((function(){return r}))]:[2,r]}}))}))}));return l.open(),l.write(Fo(document.doctype)+"<html></html>"),bo(this.referenceElement.ownerDocument,o,a),l.replaceChild(l.adoptNode(this.documentElement),l.documentElement),l.close(),c},A.prototype.createElementClone=function(A){if(In(A,2),Gs(A))return this.createCanvasClone(A);if(Js(A))return this.createVideoClone(A);if(Ws(A))return this.createStyleClone(A);var e=A.cloneNode(!1);return Xs(e)&&(Xs(A)&&A.currentSrc&&A.currentSrc!==A.src&&(e.src=A.currentSrc,e.srcset=""),"lazy"===e.loading&&(e.loading="eager")),qs(e)?this.createCustomElementClone(e):e},A.prototype.createCustomElementClone=function(A){var e=document.createElement("html2canvascustomelement");return vo(A.style,e),e},A.prototype.createStyleClone=function(A){try{var e=A.sheet;if(e&&e.cssRules){var t=[].slice.call(e.cssRules,0).reduce((function(A,e){return e&&"string"==typeof e.cssText?A+e.cssText:A}),""),r=A.cloneNode(!1);return r.textContent=t,r}}catch(n){if(this.context.logger.error("Unable to access cssRules property",n),"SecurityError"!==n.name)throw n}return A.cloneNode(!1)},A.prototype.createCanvasClone=function(A){var e;if(this.options.inlineImages&&A.ownerDocument){var t=A.ownerDocument.createElement("img");try{return t.src=A.toDataURL(),t}catch(i){this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}var r=A.cloneNode(!1);try{r.width=A.width,r.height=A.height;var n=A.getContext("2d"),s=r.getContext("2d");if(s)if(!this.options.allowTaint&&n)s.putImageData(n.getImageData(0,0,A.width,A.height),0,0);else{var o=null!==(e=A.getContext("webgl2"))&&void 0!==e?e:A.getContext("webgl");if(o){var a=o.getContextAttributes();!1===(null==a?void 0:a.preserveDrawingBuffer)&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A)}s.drawImage(A,0,0)}return r}catch(i){this.context.logger.info("Unable to clone canvas as it is tainted",A)}return r},A.prototype.createVideoClone=function(A){var e=A.ownerDocument.createElement("canvas");e.width=A.offsetWidth,e.height=A.offsetHeight;var t=e.getContext("2d");try{return t&&(t.drawImage(A,0,0,e.width,e.height),this.options.allowTaint||t.getImageData(0,0,e.width,e.height)),e}catch(n){this.context.logger.info("Unable to clone video as it is tainted",A)}var r=A.ownerDocument.createElement("canvas");return r.width=A.offsetWidth,r.height=A.offsetHeight,r},A.prototype.appendChildNode=function(A,e,t){Ds(e)&&("SCRIPT"===e.tagName||e.hasAttribute(po)||"function"==typeof this.options.ignoreElements&&this.options.ignoreElements(e))||this.options.copyStyles&&Ds(e)&&Ws(e)||A.appendChild(this.cloneNode(e,t))},A.prototype.cloneChildNodes=function(A,e,t){for(var r=this,n=A.shadowRoot?A.shadowRoot.firstChild:A.firstChild;n;n=n.nextSibling)if(Ds(n)&&zs(n)&&"function"==typeof n.assignedNodes){var s=n.assignedNodes();s.length&&s.forEach((function(A){return r.appendChildNode(e,A,t)}))}else this.appendChildNode(e,n,t)},A.prototype.cloneNode=function(A,e){if(Ks(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var t=A.ownerDocument.defaultView;if(t&&Ds(A)&&(Ts(A)||Os(A))){var r=this.createElementClone(A);r.style.transitionProperty="none";var n=t.getComputedStyle(A),s=t.getComputedStyle(A,":before"),o=t.getComputedStyle(A,":after");this.referenceElement===A&&Ts(r)&&(this.clonedReferenceElement=r),Vs(r)&&Io(r);var a=this.counters.parse(new En(this.context,n)),i=this.resolvePseudoContent(A,r,s,Pn.BEFORE);qs(A)&&(e=!0),Js(A)||this.cloneChildNodes(A,r,e),i&&r.insertBefore(i,r.firstChild);var l=this.resolvePseudoContent(A,r,o,Pn.AFTER);return l&&r.appendChild(l),this.counters.pop(a),(n&&(this.options.copyStyles||Os(A))&&!js(A)||e)&&vo(n,r),0===A.scrollTop&&0===A.scrollLeft||this.scrolledElements.push([r,A.scrollLeft,A.scrollTop]),(Ys(A)||Zs(A))&&(Ys(r)||Zs(r))&&(r.value=A.value),r}return A.cloneNode(!1)},A.prototype.resolvePseudoContent=function(A,e,t,r){var n=this;if(t){var s=t.content,o=e.ownerDocument;if(o&&s&&"none"!==s&&"-moz-alt-content"!==s&&"none"!==t.display){this.counters.parse(new En(this.context,t));var a=new xn(this.context,t),i=o.createElement("html2canvaspseudoelement");vo(t,i),a.content.forEach((function(e){if(0===e.type)i.appendChild(o.createTextNode(e.value));else if(22===e.type){var t=o.createElement("img");t.src=e.value,t.style.opacity="1",i.appendChild(t)}else if(18===e.type){if("attr"===e.name){var r=e.values.filter(We);r.length&&i.appendChild(o.createTextNode(A.getAttribute(r[0].value)||""))}else if("counter"===e.name){var s=e.values.filter(qe),l=s[0],c=s[1];if(l&&We(l)){var u=n.counters.getCounterValue(l.value),d=c&&We(c)?Kr.parse(n.context,c.value):3;i.appendChild(o.createTextNode(go(u,d,!1)))}}else if("counters"===e.name){var B=e.values.filter(qe),g=(l=B[0],B[1]);if(c=B[2],l&&We(l)){var p=n.counters.getCounterValues(l.value),f=c&&We(c)?Kr.parse(n.context,c.value):3,w=g&&0===g.type?g.value:"",h=p.map((function(A){return go(A,f,!1)})).join(w);i.appendChild(o.createTextNode(h))}}}else if(20===e.type)switch(e.value){case"open-quote":i.appendChild(o.createTextNode(Cn(a.quotes,n.quoteDepth++,!0)));break;case"close-quote":i.appendChild(o.createTextNode(Cn(a.quotes,--n.quoteDepth,!1)));break;default:i.appendChild(o.createTextNode(e.value))}})),i.className=xo+" "+Eo;var l=r===Pn.BEFORE?" "+xo:" "+Eo;return Os(e)?e.className.baseValue+=l:e.className+=l,i}}},A.destroy=function(A){return!!A.parentNode&&(A.parentNode.removeChild(A),!0)},A}();(Nn=Pn||(Pn={}))[Nn.BEFORE=0]="BEFORE",Nn[Nn.AFTER=1]="AFTER";var wo,ho,mo=function(A,e){var t=A.createElement("iframe");return t.className="html2canvas-container",t.style.visibility="hidden",t.style.position="fixed",t.style.left="-10000px",t.style.top="0px",t.style.border="0",t.width=e.width.toString(),t.height=e.height.toString(),t.scrolling="no",t.setAttribute(po,"true"),A.body.appendChild(t),t},Qo=function(A){return new Promise((function(e){A.complete?e():A.src?(A.onload=e,A.onerror=e):e()}))},yo=function(A){return Promise.all([].slice.call(A.images,0).map(Qo))},Co=function(A){return new Promise((function(e,t){var r=A.contentWindow;if(!r)return t("No window assigned for iframe");var n=r.document;r.onload=A.onload=function(){r.onload=A.onload=null;var t=setInterval((function(){n.body.childNodes.length>0&&"complete"===n.readyState&&(clearInterval(t),e(A))}),50)}}))},Uo=["all","d","content"],vo=function(A,e){for(var t=A.length-1;t>=0;t--){var r=A.item(t);-1===Uo.indexOf(r)&&e.style.setProperty(r,A.getPropertyValue(r))}return e},Fo=function(A){var e="";return A&&(e+="<!DOCTYPE ",A.name&&(e+=A.name),A.internalSubset&&(e+=A.internalSubset),A.publicId&&(e+='"'+A.publicId+'"'),A.systemId&&(e+='"'+A.systemId+'"'),e+=">"),e},bo=function(A,e,t){A&&A.defaultView&&(e!==A.defaultView.pageXOffset||t!==A.defaultView.pageYOffset)&&A.defaultView.scrollTo(e,t)},_o=function(A){var e=A[0],t=A[1],r=A[2];e.scrollLeft=t,e.scrollTop=r},xo="___html2canvas___pseudoelement_before",Eo="___html2canvas___pseudoelement_after",Ho='{\n    content: "" !important;\n    display: none !important;\n}',Io=function(A){ko(A,"."+xo+":before"+Ho+"\n         ."+Eo+":after"+Ho)},ko=function(A,e){var t=A.ownerDocument;if(t){var r=t.createElement("style");r.textContent=e,A.appendChild(r)}},Lo=function(){function A(){}return A.getOrigin=function(e){var t=A._link;return t?(t.href=e,t.href=t.href,t.protocol+t.hostname+t.port):"about:blank"},A.isSameOrigin=function(e){return A.getOrigin(e)===A._origin},A.setContext=function(e){A._link=e.document.createElement("a"),A._origin=A.getOrigin(e.location.href)},A._origin="about:blank",A}(),So=function(){function A(A,e){this.context=A,this._options=e,this._cache={}}return A.prototype.addImage=function(A){var e=Promise.resolve();return this.has(A)?e:Po(A)||Oo(A)?((this._cache[A]=this.loadImage(A)).catch((function(){})),e):e},A.prototype.match=function(A){return this._cache[A]},A.prototype.loadImage=function(A){return n(this,0,void 0,(function(){var e,t,r,n,o=this;return s(this,(function(s){switch(s.label){case 0:return e=Lo.isSameOrigin(A),t=!Mo(A)&&!0===this._options.useCORS&&ts.SUPPORT_CORS_IMAGES&&!e,r=!Mo(A)&&!e&&!Po(A)&&"string"==typeof this._options.proxy&&ts.SUPPORT_CORS_XHR&&!t,e||!1!==this._options.allowTaint||Mo(A)||Po(A)||r||t?(n=A,r?[4,this.proxy(n)]:[3,2]):[2];case 1:n=s.sent(),s.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise((function(A,e){var r=new Image;r.onload=function(){return A(r)},r.onerror=e,(Ro(n)||t)&&(r.crossOrigin="anonymous"),r.src=n,!0===r.complete&&setTimeout((function(){return A(r)}),500),o._options.imageTimeout>0&&setTimeout((function(){return e("Timed out ("+o._options.imageTimeout+"ms) loading image")}),o._options.imageTimeout)}))];case 3:return[2,s.sent()]}}))}))},A.prototype.has=function(A){return void 0!==this._cache[A]},A.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},A.prototype.proxy=function(A){var e=this,t=this._options.proxy;if(!t)throw new Error("No proxy defined");var r=A.substring(0,256);return new Promise((function(n,s){var o=ts.SUPPORT_RESPONSE_TYPE?"blob":"text",a=new XMLHttpRequest;a.onload=function(){if(200===a.status)if("text"===o)n(a.response);else{var A=new FileReader;A.addEventListener("load",(function(){return n(A.result)}),!1),A.addEventListener("error",(function(A){return s(A)}),!1),A.readAsDataURL(a.response)}else s("Failed to proxy resource "+r+" with status code "+a.status)},a.onerror=s;var i=t.indexOf("?")>-1?"&":"?";if(a.open("GET",""+t+i+"url="+encodeURIComponent(A)+"&responseType="+o),"text"!==o&&a instanceof XMLHttpRequest&&(a.responseType=o),e._options.imageTimeout){var l=e._options.imageTimeout;a.timeout=l,a.ontimeout=function(){return s("Timed out ("+l+"ms) proxying "+r)}}a.send()}))},A}(),Ko=/^data:image\/svg\+xml/i,Do=/^data:image\/.*;base64,/i,To=/^data:image\/.*/i,Oo=function(A){return ts.SUPPORT_SVG_DRAWING||!No(A)},Mo=function(A){return To.test(A)},Ro=function(A){return Do.test(A)},Po=function(A){return"blob"===A.substr(0,4)},No=function(A){return"svg"===A.substr(-3).toLowerCase()||Ko.test(A)},Vo=function(){function A(A,e){this.type=0,this.x=A,this.y=e}return A.prototype.add=function(e,t){return new A(this.x+e,this.y+t)},A}(),Go=function(A,e,t){return new Vo(A.x+(e.x-A.x)*t,A.y+(e.y-A.y)*t)},Jo=function(){function A(A,e,t,r){this.type=1,this.start=A,this.startControl=e,this.endControl=t,this.end=r}return A.prototype.subdivide=function(e,t){var r=Go(this.start,this.startControl,e),n=Go(this.startControl,this.endControl,e),s=Go(this.endControl,this.end,e),o=Go(r,n,e),a=Go(n,s,e),i=Go(o,a,e);return t?new A(this.start,r,o,i):new A(i,a,s,this.end)},A.prototype.add=function(e,t){return new A(this.start.add(e,t),this.startControl.add(e,t),this.endControl.add(e,t),this.end.add(e,t))},A.prototype.reverse=function(){return new A(this.end,this.endControl,this.startControl,this.start)},A}(),Xo=function(A){return 1===A.type},jo=function(){return function(A){var e=A.styles,t=A.bounds,r=at(e.borderTopLeftRadius,t.width,t.height),n=r[0],s=r[1],o=at(e.borderTopRightRadius,t.width,t.height),a=o[0],i=o[1],l=at(e.borderBottomRightRadius,t.width,t.height),c=l[0],u=l[1],d=at(e.borderBottomLeftRadius,t.width,t.height),B=d[0],g=d[1],p=[];p.push((n+a)/t.width),p.push((B+c)/t.width),p.push((s+g)/t.height),p.push((i+u)/t.height);var f=Math.max.apply(Math,p);f>1&&(n/=f,s/=f,a/=f,i/=f,c/=f,u/=f,B/=f,g/=f);var w=t.width-a,h=t.height-u,m=t.width-c,Q=t.height-g,y=e.borderTopWidth,C=e.borderRightWidth,U=e.borderBottomWidth,v=e.borderLeftWidth,F=it(e.paddingTop,A.bounds.width),b=it(e.paddingRight,A.bounds.width),_=it(e.paddingBottom,A.bounds.width),x=it(e.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=n>0||s>0?Wo(t.left+v/3,t.top+y/3,n-v/3,s-y/3,wo.TOP_LEFT):new Vo(t.left+v/3,t.top+y/3),this.topRightBorderDoubleOuterBox=n>0||s>0?Wo(t.left+w,t.top+y/3,a-C/3,i-y/3,wo.TOP_RIGHT):new Vo(t.left+t.width-C/3,t.top+y/3),this.bottomRightBorderDoubleOuterBox=c>0||u>0?Wo(t.left+m,t.top+h,c-C/3,u-U/3,wo.BOTTOM_RIGHT):new Vo(t.left+t.width-C/3,t.top+t.height-U/3),this.bottomLeftBorderDoubleOuterBox=B>0||g>0?Wo(t.left+v/3,t.top+Q,B-v/3,g-U/3,wo.BOTTOM_LEFT):new Vo(t.left+v/3,t.top+t.height-U/3),this.topLeftBorderDoubleInnerBox=n>0||s>0?Wo(t.left+2*v/3,t.top+2*y/3,n-2*v/3,s-2*y/3,wo.TOP_LEFT):new Vo(t.left+2*v/3,t.top+2*y/3),this.topRightBorderDoubleInnerBox=n>0||s>0?Wo(t.left+w,t.top+2*y/3,a-2*C/3,i-2*y/3,wo.TOP_RIGHT):new Vo(t.left+t.width-2*C/3,t.top+2*y/3),this.bottomRightBorderDoubleInnerBox=c>0||u>0?Wo(t.left+m,t.top+h,c-2*C/3,u-2*U/3,wo.BOTTOM_RIGHT):new Vo(t.left+t.width-2*C/3,t.top+t.height-2*U/3),this.bottomLeftBorderDoubleInnerBox=B>0||g>0?Wo(t.left+2*v/3,t.top+Q,B-2*v/3,g-2*U/3,wo.BOTTOM_LEFT):new Vo(t.left+2*v/3,t.top+t.height-2*U/3),this.topLeftBorderStroke=n>0||s>0?Wo(t.left+v/2,t.top+y/2,n-v/2,s-y/2,wo.TOP_LEFT):new Vo(t.left+v/2,t.top+y/2),this.topRightBorderStroke=n>0||s>0?Wo(t.left+w,t.top+y/2,a-C/2,i-y/2,wo.TOP_RIGHT):new Vo(t.left+t.width-C/2,t.top+y/2),this.bottomRightBorderStroke=c>0||u>0?Wo(t.left+m,t.top+h,c-C/2,u-U/2,wo.BOTTOM_RIGHT):new Vo(t.left+t.width-C/2,t.top+t.height-U/2),this.bottomLeftBorderStroke=B>0||g>0?Wo(t.left+v/2,t.top+Q,B-v/2,g-U/2,wo.BOTTOM_LEFT):new Vo(t.left+v/2,t.top+t.height-U/2),this.topLeftBorderBox=n>0||s>0?Wo(t.left,t.top,n,s,wo.TOP_LEFT):new Vo(t.left,t.top),this.topRightBorderBox=a>0||i>0?Wo(t.left+w,t.top,a,i,wo.TOP_RIGHT):new Vo(t.left+t.width,t.top),this.bottomRightBorderBox=c>0||u>0?Wo(t.left+m,t.top+h,c,u,wo.BOTTOM_RIGHT):new Vo(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=B>0||g>0?Wo(t.left,t.top+Q,B,g,wo.BOTTOM_LEFT):new Vo(t.left,t.top+t.height),this.topLeftPaddingBox=n>0||s>0?Wo(t.left+v,t.top+y,Math.max(0,n-v),Math.max(0,s-y),wo.TOP_LEFT):new Vo(t.left+v,t.top+y),this.topRightPaddingBox=a>0||i>0?Wo(t.left+Math.min(w,t.width-C),t.top+y,w>t.width+C?0:Math.max(0,a-C),Math.max(0,i-y),wo.TOP_RIGHT):new Vo(t.left+t.width-C,t.top+y),this.bottomRightPaddingBox=c>0||u>0?Wo(t.left+Math.min(m,t.width-v),t.top+Math.min(h,t.height-U),Math.max(0,c-C),Math.max(0,u-U),wo.BOTTOM_RIGHT):new Vo(t.left+t.width-C,t.top+t.height-U),this.bottomLeftPaddingBox=B>0||g>0?Wo(t.left+v,t.top+Math.min(Q,t.height-U),Math.max(0,B-v),Math.max(0,g-U),wo.BOTTOM_LEFT):new Vo(t.left+v,t.top+t.height-U),this.topLeftContentBox=n>0||s>0?Wo(t.left+v+x,t.top+y+F,Math.max(0,n-(v+x)),Math.max(0,s-(y+F)),wo.TOP_LEFT):new Vo(t.left+v+x,t.top+y+F),this.topRightContentBox=a>0||i>0?Wo(t.left+Math.min(w,t.width+v+x),t.top+y+F,w>t.width+v+x?0:a-v+x,i-(y+F),wo.TOP_RIGHT):new Vo(t.left+t.width-(C+b),t.top+y+F),this.bottomRightContentBox=c>0||u>0?Wo(t.left+Math.min(m,t.width-(v+x)),t.top+Math.min(h,t.height+y+F),Math.max(0,c-(C+b)),u-(U+_),wo.BOTTOM_RIGHT):new Vo(t.left+t.width-(C+b),t.top+t.height-(U+_)),this.bottomLeftContentBox=B>0||g>0?Wo(t.left+v+x,t.top+Q,Math.max(0,B-(v+x)),g-(U+_),wo.BOTTOM_LEFT):new Vo(t.left+v+x,t.top+t.height-(U+_))}}();(ho=wo||(wo={}))[ho.TOP_LEFT=0]="TOP_LEFT",ho[ho.TOP_RIGHT=1]="TOP_RIGHT",ho[ho.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",ho[ho.BOTTOM_LEFT=3]="BOTTOM_LEFT";var Wo=function(A,e,t,r,n){var s=(Math.sqrt(2)-1)/3*4,o=t*s,a=r*s,i=A+t,l=e+r;switch(n){case wo.TOP_LEFT:return new Jo(new Vo(A,l),new Vo(A,l-a),new Vo(i-o,e),new Vo(i,e));case wo.TOP_RIGHT:return new Jo(new Vo(A,e),new Vo(A+o,e),new Vo(i,l-a),new Vo(i,l));case wo.BOTTOM_RIGHT:return new Jo(new Vo(i,e),new Vo(i,e+a),new Vo(A+o,l),new Vo(A,l));case wo.BOTTOM_LEFT:default:return new Jo(new Vo(i,l),new Vo(i-o,l),new Vo(A,e+a),new Vo(A,e))}},Yo=function(A){return[A.topLeftBorderBox,A.topRightBorderBox,A.bottomRightBorderBox,A.bottomLeftBorderBox]},Zo=function(A){return[A.topLeftPaddingBox,A.topRightPaddingBox,A.bottomRightPaddingBox,A.bottomLeftPaddingBox]},zo=function(){return function(A,e,t){this.offsetX=A,this.offsetY=e,this.matrix=t,this.type=0,this.target=6}}(),qo=function(){return function(A,e){this.path=A,this.target=e,this.type=1}}(),$o=function(){return function(A){this.opacity=A,this.type=2,this.target=6}}(),Aa=function(A){return 1===A.type},ea=function(A,e){return A.length===e.length&&A.some((function(A,t){return A===e[t]}))},ta=function(){return function(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}}(),ra=function(){function A(A,e){if(this.container=A,this.parent=e,this.effects=[],this.curves=new jo(this.container),this.container.styles.opacity<1&&this.effects.push(new $o(this.container.styles.opacity)),null!==this.container.styles.transform){var t=this.container.bounds.left+this.container.styles.transformOrigin[0].number,r=this.container.bounds.top+this.container.styles.transformOrigin[1].number,n=this.container.styles.transform;this.effects.push(new zo(t,r,n))}if(0!==this.container.styles.overflowX){var s=Yo(this.curves),o=Zo(this.curves);ea(s,o)?this.effects.push(new qo(s,6)):(this.effects.push(new qo(s,2)),this.effects.push(new qo(o,4)))}}return A.prototype.getEffects=function(A){for(var e=-1===[2,3].indexOf(this.container.styles.position),t=this.parent,r=this.effects.slice(0);t;){var n=t.effects.filter((function(A){return!Aa(A)}));if(e||0!==t.container.styles.position||!t.parent){if(r.unshift.apply(r,n),e=-1===[2,3].indexOf(t.container.styles.position),0!==t.container.styles.overflowX){var s=Yo(t.curves),o=Zo(t.curves);ea(s,o)||r.unshift(new qo(o,6))}}else r.unshift.apply(r,n);t=t.parent}return r.filter((function(e){return fn(e.target,A)}))},A}(),na=function(A,e,t,r){A.container.elements.forEach((function(n){var s=fn(n.flags,4),o=fn(n.flags,2),a=new ra(n,A);fn(n.styles.display,2048)&&r.push(a);var i=fn(n.flags,8)?[]:r;if(s||o){var l=s||n.styles.isPositioned()?t:e,c=new ta(a);if(n.styles.isPositioned()||n.styles.opacity<1||n.styles.isTransformed()){var u=n.styles.zIndex.order;if(u<0){var d=0;l.negativeZIndex.some((function(A,e){return u>A.element.container.styles.zIndex.order?(d=e,!1):d>0})),l.negativeZIndex.splice(d,0,c)}else if(u>0){var B=0;l.positiveZIndex.some((function(A,e){return u>=A.element.container.styles.zIndex.order?(B=e+1,!1):B>0})),l.positiveZIndex.splice(B,0,c)}else l.zeroOrAutoZIndexOrTransformedOrOpacity.push(c)}else n.styles.isFloating()?l.nonPositionedFloats.push(c):l.nonPositionedInlineLevel.push(c);na(a,c,s?c:t,i)}else n.styles.isInlineLevel()?e.inlineLevel.push(a):e.nonInlineLevel.push(a),na(a,e,t,i);fn(n.flags,8)&&sa(n,i)}))},sa=function(A,e){for(var t=A instanceof hs?A.start:1,r=A instanceof hs&&A.reversed,n=0;n<e.length;n++){var s=e[n];s.container instanceof ws&&"number"==typeof s.container.value&&0!==s.container.value&&(t=s.container.value),s.listValue=go(t,s.container.styles.listStyleType,!0),t+=r?-1:1}},oa=function(A,e){switch(e){case 0:return ia(A.topLeftBorderBox,A.topLeftPaddingBox,A.topRightBorderBox,A.topRightPaddingBox);case 1:return ia(A.topRightBorderBox,A.topRightPaddingBox,A.bottomRightBorderBox,A.bottomRightPaddingBox);case 2:return ia(A.bottomRightBorderBox,A.bottomRightPaddingBox,A.bottomLeftBorderBox,A.bottomLeftPaddingBox);default:return ia(A.bottomLeftBorderBox,A.bottomLeftPaddingBox,A.topLeftBorderBox,A.topLeftPaddingBox)}},aa=function(A,e){var t=[];return Xo(A)?t.push(A.subdivide(.5,!1)):t.push(A),Xo(e)?t.push(e.subdivide(.5,!0)):t.push(e),t},ia=function(A,e,t,r){var n=[];return Xo(A)?n.push(A.subdivide(.5,!1)):n.push(A),Xo(t)?n.push(t.subdivide(.5,!0)):n.push(t),Xo(r)?n.push(r.subdivide(.5,!0).reverse()):n.push(r),Xo(e)?n.push(e.subdivide(.5,!1).reverse()):n.push(e),n},la=function(A){var e=A.bounds,t=A.styles;return e.add(t.borderLeftWidth,t.borderTopWidth,-(t.borderRightWidth+t.borderLeftWidth),-(t.borderTopWidth+t.borderBottomWidth))},ca=function(A){var e=A.styles,t=A.bounds,r=it(e.paddingLeft,t.width),n=it(e.paddingRight,t.width),s=it(e.paddingTop,t.width),o=it(e.paddingBottom,t.width);return t.add(r+e.borderLeftWidth,s+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+r+n),-(e.borderTopWidth+e.borderBottomWidth+s+o))},ua=function(A,e,t){var r,n,s=(r=pa(A.styles.backgroundOrigin,e),n=A,0===r?n.bounds:2===r?ca(n):la(n)),o=function(A,e){return 0===A?e.bounds:2===A?ca(e):la(e)}(pa(A.styles.backgroundClip,e),A),a=ga(pa(A.styles.backgroundSize,e),t,s),i=a[0],l=a[1],c=at(pa(A.styles.backgroundPosition,e),s.width-i,s.height-l);return[fa(pa(A.styles.backgroundRepeat,e),c,a,s,o),Math.round(s.left+c[0]),Math.round(s.top+c[1]),i,l]},da=function(A){return We(A)&&A.value===Ct.AUTO},Ba=function(A){return"number"==typeof A},ga=function(A,e,t){var r=e[0],n=e[1],s=e[2],o=A[0],a=A[1];if(!o)return[0,0];if(tt(o)&&a&&tt(a))return[it(o,t.width),it(a,t.height)];var i=Ba(s);if(We(o)&&(o.value===Ct.CONTAIN||o.value===Ct.COVER))return Ba(s)?t.width/t.height<s!=(o.value===Ct.COVER)?[t.width,t.width/s]:[t.height*s,t.height]:[t.width,t.height];var l=Ba(r),c=Ba(n),u=l||c;if(da(o)&&(!a||da(a)))return l&&c?[r,n]:i||u?u&&i?[l?r:n*s,c?n:r/s]:[l?r:t.width,c?n:t.height]:[t.width,t.height];if(i){var d=0,B=0;return tt(o)?d=it(o,t.width):tt(a)&&(B=it(a,t.height)),da(o)?d=B*s:a&&!da(a)||(B=d/s),[d,B]}var g=null,p=null;if(tt(o)?g=it(o,t.width):a&&tt(a)&&(p=it(a,t.height)),null===g||a&&!da(a)||(p=l&&c?g/r*n:t.height),null!==p&&da(o)&&(g=l&&c?p/n*r:t.width),null!==g&&null!==p)return[g,p];throw new Error("Unable to calculate background-size for element")},pa=function(A,e){var t=A[e];return void 0===t?A[0]:t},fa=function(A,e,t,r,n){var s=e[0],o=e[1],a=t[0],i=t[1];switch(A){case 2:return[new Vo(Math.round(r.left),Math.round(r.top+o)),new Vo(Math.round(r.left+r.width),Math.round(r.top+o)),new Vo(Math.round(r.left+r.width),Math.round(i+r.top+o)),new Vo(Math.round(r.left),Math.round(i+r.top+o))];case 3:return[new Vo(Math.round(r.left+s),Math.round(r.top)),new Vo(Math.round(r.left+s+a),Math.round(r.top)),new Vo(Math.round(r.left+s+a),Math.round(r.height+r.top)),new Vo(Math.round(r.left+s),Math.round(r.height+r.top))];case 1:return[new Vo(Math.round(r.left+s),Math.round(r.top+o)),new Vo(Math.round(r.left+s+a),Math.round(r.top+o)),new Vo(Math.round(r.left+s+a),Math.round(r.top+o+i)),new Vo(Math.round(r.left+s),Math.round(r.top+o+i))];default:return[new Vo(Math.round(n.left),Math.round(n.top)),new Vo(Math.round(n.left+n.width),Math.round(n.top)),new Vo(Math.round(n.left+n.width),Math.round(n.height+n.top)),new Vo(Math.round(n.left),Math.round(n.height+n.top))]}},wa="Hidden Text",ha=function(){function A(A){this._data={},this._document=A}return A.prototype.parseMetrics=function(A,e){var t=this._document.createElement("div"),r=this._document.createElement("img"),n=this._document.createElement("span"),s=this._document.body;t.style.visibility="hidden",t.style.fontFamily=A,t.style.fontSize=e,t.style.margin="0",t.style.padding="0",t.style.whiteSpace="nowrap",s.appendChild(t),r.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",r.width=1,r.height=1,r.style.margin="0",r.style.padding="0",r.style.verticalAlign="baseline",n.style.fontFamily=A,n.style.fontSize=e,n.style.margin="0",n.style.padding="0",n.appendChild(this._document.createTextNode(wa)),t.appendChild(n),t.appendChild(r);var o=r.offsetTop-n.offsetTop+2;t.removeChild(n),t.appendChild(this._document.createTextNode(wa)),t.style.lineHeight="normal",r.style.verticalAlign="super";var a=r.offsetTop-t.offsetTop+2;return s.removeChild(t),{baseline:o,middle:a}},A.prototype.getMetrics=function(A,e){var t=A+" "+e;return void 0===this._data[t]&&(this._data[t]=this.parseMetrics(A,e)),this._data[t]},A}(),ma=function(){return function(A,e){this.context=A,this.options=e}}(),Qa=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r._activeEffects=[],r.canvas=t.canvas?t.canvas:document.createElement("canvas"),r.ctx=r.canvas.getContext("2d"),t.canvas||(r.canvas.width=Math.floor(t.width*t.scale),r.canvas.height=Math.floor(t.height*t.scale),r.canvas.style.width=t.width+"px",r.canvas.style.height=t.height+"px"),r.fontMetrics=new ha(document),r.ctx.scale(r.options.scale,r.options.scale),r.ctx.translate(-t.x,-t.y),r.ctx.textBaseline="bottom",r._activeEffects=[],r.context.logger.debug("Canvas renderer initialized ("+t.width+"x"+t.height+") with scale "+t.scale),r}return t(e,A),e.prototype.applyEffects=function(A){for(var e=this;this._activeEffects.length;)this.popEffect();A.forEach((function(A){return e.applyEffect(A)}))},e.prototype.applyEffect=function(A){this.ctx.save(),function(A){return 2===A.type}(A)&&(this.ctx.globalAlpha=A.opacity),function(A){return 0===A.type}(A)&&(this.ctx.translate(A.offsetX,A.offsetY),this.ctx.transform(A.matrix[0],A.matrix[1],A.matrix[2],A.matrix[3],A.matrix[4],A.matrix[5]),this.ctx.translate(-A.offsetX,-A.offsetY)),Aa(A)&&(this.path(A.path),this.ctx.clip()),this._activeEffects.push(A)},e.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},e.prototype.renderStack=function(A){return n(this,0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return A.element.container.styles.isVisible()?[4,this.renderStackContent(A)]:[3,2];case 1:e.sent(),e.label=2;case 2:return[2]}}))}))},e.prototype.renderNode=function(A){return n(this,0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return fn(A.container.flags,16),A.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(A)]:[3,3];case 1:return e.sent(),[4,this.renderNodeContent(A)];case 2:e.sent(),e.label=3;case 3:return[2]}}))}))},e.prototype.renderTextWithLetterSpacing=function(A,e,t){var r=this;0===e?this.ctx.fillText(A.text,A.bounds.left,A.bounds.top+t):os(A.text).reduce((function(e,n){return r.ctx.fillText(n,e,A.bounds.top+t),e+r.ctx.measureText(n).width}),A.bounds.left)},e.prototype.createFontStyle=function(A){var e=A.fontVariant.filter((function(A){return"normal"===A||"small-caps"===A})).join(""),t=Fa(A.fontFamily).join(", "),r=Xe(A.fontSize)?""+A.fontSize.number+A.fontSize.unit:A.fontSize.number+"px";return[[A.fontStyle,e,A.fontWeight,r,t].join(" "),t,r]},e.prototype.renderTextNode=function(A,e){return n(this,0,void 0,(function(){var t,r,n,o,a,i,l,c,u=this;return s(this,(function(s){return t=this.createFontStyle(e),r=t[0],n=t[1],o=t[2],this.ctx.font=r,this.ctx.direction=1===e.direction?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",a=this.fontMetrics.getMetrics(n,o),i=a.baseline,l=a.middle,c=e.paintOrder,A.textBounds.forEach((function(A){c.forEach((function(t){switch(t){case 0:u.ctx.fillStyle=wt(e.color),u.renderTextWithLetterSpacing(A,e.letterSpacing,i);var r=e.textShadow;r.length&&A.text.trim().length&&(r.slice(0).reverse().forEach((function(t){u.ctx.shadowColor=wt(t.color),u.ctx.shadowOffsetX=t.offsetX.number*u.options.scale,u.ctx.shadowOffsetY=t.offsetY.number*u.options.scale,u.ctx.shadowBlur=t.blur.number,u.renderTextWithLetterSpacing(A,e.letterSpacing,i)})),u.ctx.shadowColor="",u.ctx.shadowOffsetX=0,u.ctx.shadowOffsetY=0,u.ctx.shadowBlur=0),e.textDecorationLine.length&&(u.ctx.fillStyle=wt(e.textDecorationColor||e.color),e.textDecorationLine.forEach((function(e){switch(e){case 1:u.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top+i),A.bounds.width,1);break;case 2:u.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top),A.bounds.width,1);break;case 3:u.ctx.fillRect(A.bounds.left,Math.ceil(A.bounds.top+l),A.bounds.width,1)}})));break;case 1:e.webkitTextStrokeWidth&&A.text.trim().length&&(u.ctx.strokeStyle=wt(e.webkitTextStrokeColor),u.ctx.lineWidth=e.webkitTextStrokeWidth,u.ctx.lineJoin=window.chrome?"miter":"round",u.ctx.strokeText(A.text,A.bounds.left,A.bounds.top+i)),u.ctx.strokeStyle="",u.ctx.lineWidth=0,u.ctx.lineJoin="miter"}}))})),[2]}))}))},e.prototype.renderReplacedElement=function(A,e,t){if(t&&A.intrinsicWidth>0&&A.intrinsicHeight>0){var r=ca(A),n=Zo(e);this.path(n),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(t,0,0,A.intrinsicWidth,A.intrinsicHeight,r.left,r.top,r.width,r.height),this.ctx.restore()}},e.prototype.renderNodeContent=function(A){return n(this,0,void 0,(function(){var t,r,n,a,i,l,c,u,d,B,g,p,f,w,h,m,Q,y;return s(this,(function(s){switch(s.label){case 0:this.applyEffects(A.getEffects(4)),t=A.container,r=A.curves,n=t.styles,a=0,i=t.textNodes,s.label=1;case 1:return a<i.length?(l=i[a],[4,this.renderTextNode(l,n)]):[3,4];case 2:s.sent(),s.label=3;case 3:return a++,[3,1];case 4:if(!(t instanceof gs))return[3,8];s.label=5;case 5:return s.trys.push([5,7,,8]),[4,this.context.cache.match(t.src)];case 6:return h=s.sent(),this.renderReplacedElement(t,r,h),[3,8];case 7:return s.sent(),this.context.logger.error("Error loading image "+t.src),[3,8];case 8:if(t instanceof ps&&this.renderReplacedElement(t,r,t.canvas),!(t instanceof fs))return[3,12];s.label=9;case 9:return s.trys.push([9,11,,12]),[4,this.context.cache.match(t.svg)];case 10:return h=s.sent(),this.renderReplacedElement(t,r,h),[3,12];case 11:return s.sent(),this.context.logger.error("Error loading svg "+t.svg.substring(0,255)),[3,12];case 12:return t instanceof xs&&t.tree?[4,new e(this.context,{scale:this.options.scale,backgroundColor:t.backgroundColor,x:0,y:0,width:t.width,height:t.height}).render(t.tree)]:[3,14];case 13:c=s.sent(),t.width&&t.height&&this.ctx.drawImage(c,0,0,t.width,t.height,t.bounds.left,t.bounds.top,t.bounds.width,t.bounds.height),s.label=14;case 14:if(t instanceof Fs&&(u=Math.min(t.bounds.width,t.bounds.height),t.type===ys?t.checked&&(this.ctx.save(),this.path([new Vo(t.bounds.left+.39363*u,t.bounds.top+.79*u),new Vo(t.bounds.left+.16*u,t.bounds.top+.5549*u),new Vo(t.bounds.left+.27347*u,t.bounds.top+.44071*u),new Vo(t.bounds.left+.39694*u,t.bounds.top+.5649*u),new Vo(t.bounds.left+.72983*u,t.bounds.top+.23*u),new Vo(t.bounds.left+.84*u,t.bounds.top+.34085*u),new Vo(t.bounds.left+.39363*u,t.bounds.top+.79*u)]),this.ctx.fillStyle=wt(vs),this.ctx.fill(),this.ctx.restore()):t.type===Cs&&t.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(t.bounds.left+u/2,t.bounds.top+u/2,u/4,0,2*Math.PI,!0),this.ctx.fillStyle=wt(vs),this.ctx.fill(),this.ctx.restore())),ya(t)&&t.value.length){switch(d=this.createFontStyle(n),Q=d[0],B=d[1],g=this.fontMetrics.getMetrics(Q,B).baseline,this.ctx.font=Q,this.ctx.fillStyle=wt(n.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=Ua(t.styles.textAlign),y=ca(t),p=0,t.styles.textAlign){case 1:p+=y.width/2;break;case 2:p+=y.width}f=y.add(p,0,0,-y.height/2+1),this.ctx.save(),this.path([new Vo(y.left,y.top),new Vo(y.left+y.width,y.top),new Vo(y.left+y.width,y.top+y.height),new Vo(y.left,y.top+y.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new rs(t.value,f),n.letterSpacing,g),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!fn(t.styles.display,2048))return[3,20];if(null===t.styles.listStyleImage)return[3,19];if(0!==(w=t.styles.listStyleImage).type)return[3,18];h=void 0,m=w.url,s.label=15;case 15:return s.trys.push([15,17,,18]),[4,this.context.cache.match(m)];case 16:return h=s.sent(),this.ctx.drawImage(h,t.bounds.left-(h.width+10),t.bounds.top),[3,18];case 17:return s.sent(),this.context.logger.error("Error loading list-style-image "+m),[3,18];case 18:return[3,20];case 19:A.listValue&&-1!==t.styles.listStyleType&&(Q=this.createFontStyle(n)[0],this.ctx.font=Q,this.ctx.fillStyle=wt(n.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",y=new o(t.bounds.left,t.bounds.top+it(t.styles.paddingTop,t.bounds.width),t.bounds.width,kr(n.lineHeight,n.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new rs(A.listValue,y),n.letterSpacing,kr(n.lineHeight,n.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),s.label=20;case 20:return[2]}}))}))},e.prototype.renderStackContent=function(A){return n(this,0,void 0,(function(){var e,t,r,n,o,a,i,l,c,u,d,B,g,p,f;return s(this,(function(s){switch(s.label){case 0:return fn(A.element.container.flags,16),[4,this.renderNodeBackgroundAndBorders(A.element)];case 1:s.sent(),e=0,t=A.negativeZIndex,s.label=2;case 2:return e<t.length?(f=t[e],[4,this.renderStack(f)]):[3,5];case 3:s.sent(),s.label=4;case 4:return e++,[3,2];case 5:return[4,this.renderNodeContent(A.element)];case 6:s.sent(),r=0,n=A.nonInlineLevel,s.label=7;case 7:return r<n.length?(f=n[r],[4,this.renderNode(f)]):[3,10];case 8:s.sent(),s.label=9;case 9:return r++,[3,7];case 10:o=0,a=A.nonPositionedFloats,s.label=11;case 11:return o<a.length?(f=a[o],[4,this.renderStack(f)]):[3,14];case 12:s.sent(),s.label=13;case 13:return o++,[3,11];case 14:i=0,l=A.nonPositionedInlineLevel,s.label=15;case 15:return i<l.length?(f=l[i],[4,this.renderStack(f)]):[3,18];case 16:s.sent(),s.label=17;case 17:return i++,[3,15];case 18:c=0,u=A.inlineLevel,s.label=19;case 19:return c<u.length?(f=u[c],[4,this.renderNode(f)]):[3,22];case 20:s.sent(),s.label=21;case 21:return c++,[3,19];case 22:d=0,B=A.zeroOrAutoZIndexOrTransformedOrOpacity,s.label=23;case 23:return d<B.length?(f=B[d],[4,this.renderStack(f)]):[3,26];case 24:s.sent(),s.label=25;case 25:return d++,[3,23];case 26:g=0,p=A.positiveZIndex,s.label=27;case 27:return g<p.length?(f=p[g],[4,this.renderStack(f)]):[3,30];case 28:s.sent(),s.label=29;case 29:return g++,[3,27];case 30:return[2]}}))}))},e.prototype.mask=function(A){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(A.slice(0).reverse()),this.ctx.closePath()},e.prototype.path=function(A){this.ctx.beginPath(),this.formatPath(A),this.ctx.closePath()},e.prototype.formatPath=function(A){var e=this;A.forEach((function(A,t){var r=Xo(A)?A.start:A;0===t?e.ctx.moveTo(r.x,r.y):e.ctx.lineTo(r.x,r.y),Xo(A)&&e.ctx.bezierCurveTo(A.startControl.x,A.startControl.y,A.endControl.x,A.endControl.y,A.end.x,A.end.y)}))},e.prototype.renderRepeat=function(A,e,t,r){this.path(A),this.ctx.fillStyle=e,this.ctx.translate(t,r),this.ctx.fill(),this.ctx.translate(-t,-r)},e.prototype.resizeImage=function(A,e,t){var r;if(A.width===e&&A.height===t)return A;var n=(null!==(r=this.canvas.ownerDocument)&&void 0!==r?r:document).createElement("canvas");return n.width=Math.max(1,e),n.height=Math.max(1,t),n.getContext("2d").drawImage(A,0,0,A.width,A.height,0,0,e,t),n},e.prototype.renderBackgroundImage=function(A){return n(this,0,void 0,(function(){var e,t,r,n,o,a;return s(this,(function(i){switch(i.label){case 0:e=A.styles.backgroundImage.length-1,t=function(t){var n,o,a,i,l,c,u,d,B,g,p,f,w,h,m,Q,y,C,U,v,F,b,_,x,E,H,I,k,L,S,K;return s(this,(function(s){switch(s.label){case 0:if(0!==t.type)return[3,5];n=void 0,o=t.url,s.label=1;case 1:return s.trys.push([1,3,,4]),[4,r.context.cache.match(o)];case 2:return n=s.sent(),[3,4];case 3:return s.sent(),r.context.logger.error("Error loading background-image "+o),[3,4];case 4:return n&&(a=ua(A,e,[n.width,n.height,n.width/n.height]),Q=a[0],b=a[1],_=a[2],U=a[3],v=a[4],h=r.ctx.createPattern(r.resizeImage(n,U,v),"repeat"),r.renderRepeat(Q,h,b,_)),[3,6];case 5:1===t.type?(i=ua(A,e,[null,null,null]),Q=i[0],b=i[1],_=i[2],U=i[3],v=i[4],l=kt(t.angle,U,v),c=l[0],u=l[1],d=l[2],B=l[3],g=l[4],(p=document.createElement("canvas")).width=U,p.height=v,f=p.getContext("2d"),w=f.createLinearGradient(u,B,d,g),It(t.stops,c).forEach((function(A){return w.addColorStop(A.stop,wt(A.color))})),f.fillStyle=w,f.fillRect(0,0,U,v),U>0&&v>0&&(h=r.ctx.createPattern(p,"repeat"),r.renderRepeat(Q,h,b,_))):function(A){return 2===A.type}(t)&&(m=ua(A,e,[null,null,null]),Q=m[0],y=m[1],C=m[2],U=m[3],v=m[4],F=0===t.position.length?[st]:t.position,b=it(F[0],U),_=it(F[F.length-1],v),x=function(A,e,t,r,n){var s=0,o=0;switch(A.size){case 0:0===A.shape?s=o=Math.min(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):1===A.shape&&(s=Math.min(Math.abs(e),Math.abs(e-r)),o=Math.min(Math.abs(t),Math.abs(t-n)));break;case 2:if(0===A.shape)s=o=Math.min(Lt(e,t),Lt(e,t-n),Lt(e-r,t),Lt(e-r,t-n));else if(1===A.shape){var a=Math.min(Math.abs(t),Math.abs(t-n))/Math.min(Math.abs(e),Math.abs(e-r)),i=St(r,n,e,t,!0),l=i[0],c=i[1];o=a*(s=Lt(l-e,(c-t)/a))}break;case 1:0===A.shape?s=o=Math.max(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):1===A.shape&&(s=Math.max(Math.abs(e),Math.abs(e-r)),o=Math.max(Math.abs(t),Math.abs(t-n)));break;case 3:if(0===A.shape)s=o=Math.max(Lt(e,t),Lt(e,t-n),Lt(e-r,t),Lt(e-r,t-n));else if(1===A.shape){a=Math.max(Math.abs(t),Math.abs(t-n))/Math.max(Math.abs(e),Math.abs(e-r));var u=St(r,n,e,t,!1);l=u[0],c=u[1],o=a*(s=Lt(l-e,(c-t)/a))}}return Array.isArray(A.size)&&(s=it(A.size[0],r),o=2===A.size.length?it(A.size[1],n):s),[s,o]}(t,b,_,U,v),E=x[0],H=x[1],E>0&&H>0&&(I=r.ctx.createRadialGradient(y+b,C+_,0,y+b,C+_,E),It(t.stops,2*E).forEach((function(A){return I.addColorStop(A.stop,wt(A.color))})),r.path(Q),r.ctx.fillStyle=I,E!==H?(k=A.bounds.left+.5*A.bounds.width,L=A.bounds.top+.5*A.bounds.height,K=1/(S=H/E),r.ctx.save(),r.ctx.translate(k,L),r.ctx.transform(1,0,0,S,0,0),r.ctx.translate(-k,-L),r.ctx.fillRect(y,K*(C-L)+L,U,v*K),r.ctx.restore()):r.ctx.fill())),s.label=6;case 6:return e--,[2]}}))},r=this,n=0,o=A.styles.backgroundImage.slice(0).reverse(),i.label=1;case 1:return n<o.length?(a=o[n],[5,t(a)]):[3,4];case 2:i.sent(),i.label=3;case 3:return n++,[3,1];case 4:return[2]}}))}))},e.prototype.renderSolidBorder=function(A,e,t){return n(this,0,void 0,(function(){return s(this,(function(r){return this.path(oa(t,e)),this.ctx.fillStyle=wt(A),this.ctx.fill(),[2]}))}))},e.prototype.renderDoubleBorder=function(A,e,t,r){return n(this,0,void 0,(function(){var n,o;return s(this,(function(s){switch(s.label){case 0:return e<3?[4,this.renderSolidBorder(A,t,r)]:[3,2];case 1:return s.sent(),[2];case 2:return n=function(A,e){switch(e){case 0:return ia(A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox,A.topRightBorderBox,A.topRightBorderDoubleOuterBox);case 1:return ia(A.topRightBorderBox,A.topRightBorderDoubleOuterBox,A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox);case 2:return ia(A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox,A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox);default:return ia(A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox,A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox)}}(r,t),this.path(n),this.ctx.fillStyle=wt(A),this.ctx.fill(),o=function(A,e){switch(e){case 0:return ia(A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox,A.topRightBorderDoubleInnerBox,A.topRightPaddingBox);case 1:return ia(A.topRightBorderDoubleInnerBox,A.topRightPaddingBox,A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox);case 2:return ia(A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox,A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox);default:return ia(A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox,A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox)}}(r,t),this.path(o),this.ctx.fill(),[2]}}))}))},e.prototype.renderNodeBackgroundAndBorders=function(A){return n(this,0,void 0,(function(){var e,t,r,n,o,a,i,l,c=this;return s(this,(function(s){switch(s.label){case 0:return this.applyEffects(A.getEffects(2)),e=A.container.styles,t=!ft(e.backgroundColor)||e.backgroundImage.length,r=[{style:e.borderTopStyle,color:e.borderTopColor,width:e.borderTopWidth},{style:e.borderRightStyle,color:e.borderRightColor,width:e.borderRightWidth},{style:e.borderBottomStyle,color:e.borderBottomColor,width:e.borderBottomWidth},{style:e.borderLeftStyle,color:e.borderLeftColor,width:e.borderLeftWidth}],n=Ca(pa(e.backgroundClip,0),A.curves),t||e.boxShadow.length?(this.ctx.save(),this.path(n),this.ctx.clip(),ft(e.backgroundColor)||(this.ctx.fillStyle=wt(e.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(A.container)]):[3,2];case 1:s.sent(),this.ctx.restore(),e.boxShadow.slice(0).reverse().forEach((function(e){c.ctx.save();var t,r,n,s,o,a=Yo(A.curves),i=e.inset?0:1e4,l=(t=a,r=-i+(e.inset?1:-1)*e.spread.number,n=(e.inset?1:-1)*e.spread.number,s=e.spread.number*(e.inset?-2:2),o=e.spread.number*(e.inset?-2:2),t.map((function(A,e){switch(e){case 0:return A.add(r,n);case 1:return A.add(r+s,n);case 2:return A.add(r+s,n+o);case 3:return A.add(r,n+o)}return A})));e.inset?(c.path(a),c.ctx.clip(),c.mask(l)):(c.mask(a),c.ctx.clip(),c.path(l)),c.ctx.shadowOffsetX=e.offsetX.number+i,c.ctx.shadowOffsetY=e.offsetY.number,c.ctx.shadowColor=wt(e.color),c.ctx.shadowBlur=e.blur.number,c.ctx.fillStyle=e.inset?wt(e.color):"rgba(0,0,0,1)",c.ctx.fill(),c.ctx.restore()})),s.label=2;case 2:o=0,a=0,i=r,s.label=3;case 3:return a<i.length?0!==(l=i[a]).style&&!ft(l.color)&&l.width>0?2!==l.style?[3,5]:[4,this.renderDashedDottedBorder(l.color,l.width,o,A.curves,2)]:[3,11]:[3,13];case 4:return s.sent(),[3,11];case 5:return 3!==l.style?[3,7]:[4,this.renderDashedDottedBorder(l.color,l.width,o,A.curves,3)];case 6:return s.sent(),[3,11];case 7:return 4!==l.style?[3,9]:[4,this.renderDoubleBorder(l.color,l.width,o,A.curves)];case 8:return s.sent(),[3,11];case 9:return[4,this.renderSolidBorder(l.color,o,A.curves)];case 10:s.sent(),s.label=11;case 11:o++,s.label=12;case 12:return a++,[3,3];case 13:return[2]}}))}))},e.prototype.renderDashedDottedBorder=function(A,e,t,r,o){return n(this,0,void 0,(function(){var n,a,i,l,c,u,d,B,g,p,f,w,h,m,Q,y;return s(this,(function(s){return this.ctx.save(),n=function(A,e){switch(e){case 0:return aa(A.topLeftBorderStroke,A.topRightBorderStroke);case 1:return aa(A.topRightBorderStroke,A.bottomRightBorderStroke);case 2:return aa(A.bottomRightBorderStroke,A.bottomLeftBorderStroke);default:return aa(A.bottomLeftBorderStroke,A.topLeftBorderStroke)}}(r,t),a=oa(r,t),2===o&&(this.path(a),this.ctx.clip()),Xo(a[0])?(i=a[0].start.x,l=a[0].start.y):(i=a[0].x,l=a[0].y),Xo(a[1])?(c=a[1].end.x,u=a[1].end.y):(c=a[1].x,u=a[1].y),d=0===t||2===t?Math.abs(i-c):Math.abs(l-u),this.ctx.beginPath(),3===o?this.formatPath(n):this.formatPath(a.slice(0,2)),B=e<3?3*e:2*e,g=e<3?2*e:e,3===o&&(B=e,g=e),p=!0,d<=2*B?p=!1:d<=2*B+g?(B*=f=d/(2*B+g),g*=f):(w=Math.floor((d+g)/(B+g)),h=(d-w*B)/(w-1),g=(m=(d-(w+1)*B)/w)<=0||Math.abs(g-h)<Math.abs(g-m)?h:m),p&&(3===o?this.ctx.setLineDash([0,B+g]):this.ctx.setLineDash([B,g])),3===o?(this.ctx.lineCap="round",this.ctx.lineWidth=e):this.ctx.lineWidth=2*e+1.1,this.ctx.strokeStyle=wt(A),this.ctx.stroke(),this.ctx.setLineDash([]),2===o&&(Xo(a[0])&&(Q=a[3],y=a[0],this.ctx.beginPath(),this.formatPath([new Vo(Q.end.x,Q.end.y),new Vo(y.start.x,y.start.y)]),this.ctx.stroke()),Xo(a[1])&&(Q=a[1],y=a[2],this.ctx.beginPath(),this.formatPath([new Vo(Q.end.x,Q.end.y),new Vo(y.start.x,y.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]}))}))},e.prototype.render=function(A){return n(this,0,void 0,(function(){var e;return s(this,(function(t){switch(t.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=wt(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),r=new ra(A,null),n=new ta(r),na(r,n,n,s=[]),sa(r.container,s),e=n,[4,this.renderStack(e)];case 1:return t.sent(),this.applyEffects([]),[2,this.canvas]}var r,n,s}))}))},e}(ma),ya=function(A){return A instanceof _s||A instanceof bs||A instanceof Fs&&A.type!==Cs&&A.type!==ys},Ca=function(A,e){switch(A){case 0:return Yo(e);case 2:return function(A){return[A.topLeftContentBox,A.topRightContentBox,A.bottomRightContentBox,A.bottomLeftContentBox]}(e);default:return Zo(e)}},Ua=function(A){switch(A){case 1:return"center";case 2:return"right";default:return"left"}},va=["-apple-system","system-ui"],Fa=function(A){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?A.filter((function(A){return-1===va.indexOf(A)})):A},ba=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.canvas=t.canvas?t.canvas:document.createElement("canvas"),r.ctx=r.canvas.getContext("2d"),r.options=t,r.canvas.width=Math.floor(t.width*t.scale),r.canvas.height=Math.floor(t.height*t.scale),r.canvas.style.width=t.width+"px",r.canvas.style.height=t.height+"px",r.ctx.scale(r.options.scale,r.options.scale),r.ctx.translate(-t.x,-t.y),r.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+t.width+"x"+t.height+" at "+t.x+","+t.y+") with scale "+t.scale),r}return t(e,A),e.prototype.render=function(A){return n(this,0,void 0,(function(){var e,t;return s(this,(function(r){switch(r.label){case 0:return e=As(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,A),[4,_a(e)];case 1:return t=r.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=wt(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(t,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}}))}))},e}(ma),_a=function(A){return new Promise((function(e,t){var r=new Image;r.onload=function(){e(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},xa=function(){function A(A){var e=A.id,t=A.enabled;this.id=e,this.enabled=t,this.start=Date.now()}return A.prototype.debug=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.debug||this.info.apply(this,A))},A.prototype.getTime=function(){return Date.now()-this.start},A.prototype.info=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&"undefined"!=typeof window&&window.console&&console.info},A.prototype.warn=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.warn||this.info.apply(this,A))},A.prototype.error=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.error||this.info.apply(this,A))},A.instances={},A}(),Ea=function(){function A(e,t){var r;this.windowBounds=t,this.instanceName="#"+A.instanceCount++,this.logger=new xa({id:this.instanceName,enabled:e.logging}),this.cache=null!==(r=e.cache)&&void 0!==r?r:new So(this,e)}return A.instanceCount=1,A}();"undefined"!=typeof window&&Lo.setContext(window);var Ha=function(A,e){return n(void 0,0,void 0,(function(){var t,n,a,l,c,u,d,B,g,p,f,w,h,m,Q,y,C,U,v,F,b,_,x,E,H,I,k,L,S,K,D,T,O,M,R,P,N,V;return s(this,(function(s){switch(s.label){case 0:if(!A||"object"!=typeof A)return[2,Promise.reject("Invalid element provided as first argument")];if(!(t=A.ownerDocument))throw new Error("Element is not attached to a Document");if(!(n=t.defaultView))throw new Error("Document is not attached to a Window");return a={allowTaint:null!==(_=e.allowTaint)&&void 0!==_&&_,imageTimeout:null!==(x=e.imageTimeout)&&void 0!==x?x:15e3,proxy:e.proxy,useCORS:null!==(E=e.useCORS)&&void 0!==E&&E},l=r({logging:null===(H=e.logging)||void 0===H||H,cache:e.cache},a),c={windowWidth:null!==(I=e.windowWidth)&&void 0!==I?I:n.innerWidth,windowHeight:null!==(k=e.windowHeight)&&void 0!==k?k:n.innerHeight,scrollX:null!==(L=e.scrollX)&&void 0!==L?L:n.pageXOffset,scrollY:null!==(S=e.scrollY)&&void 0!==S?S:n.pageYOffset},u=new o(c.scrollX,c.scrollY,c.windowWidth,c.windowHeight),d=new Ea(l,u),B=null!==(K=e.foreignObjectRendering)&&void 0!==K&&K,g={allowTaint:null!==(D=e.allowTaint)&&void 0!==D&&D,onclone:e.onclone,ignoreElements:e.ignoreElements,inlineImages:B,copyStyles:B},d.logger.debug("Starting document clone with size "+u.width+"x"+u.height+" scrolled to "+-u.left+","+-u.top),p=new fo(d,A,g),(f=p.clonedReferenceElement)?[4,p.toIFrame(t,u)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return w=s.sent(),h=Vs(f)||"HTML"===f.tagName?function(A){var e=A.body,t=A.documentElement;if(!e||!t)throw new Error("Unable to get document size");var r=Math.max(Math.max(e.scrollWidth,t.scrollWidth),Math.max(e.offsetWidth,t.offsetWidth),Math.max(e.clientWidth,t.clientWidth)),n=Math.max(Math.max(e.scrollHeight,t.scrollHeight),Math.max(e.offsetHeight,t.offsetHeight),Math.max(e.clientHeight,t.clientHeight));return new o(0,0,r,n)}(f.ownerDocument):i(d,f),m=h.width,Q=h.height,y=h.left,C=h.top,U=Ia(d,f,e.backgroundColor),v={canvas:e.canvas,backgroundColor:U,scale:null!==(O=null!==(T=e.scale)&&void 0!==T?T:n.devicePixelRatio)&&void 0!==O?O:1,x:(null!==(M=e.x)&&void 0!==M?M:0)+y,y:(null!==(R=e.y)&&void 0!==R?R:0)+C,width:null!==(P=e.width)&&void 0!==P?P:Math.ceil(m),height:null!==(N=e.height)&&void 0!==N?N:Math.ceil(Q)},B?(d.logger.debug("Document cloned, using foreign object rendering"),[4,new ba(d,v).render(f)]):[3,3];case 2:return F=s.sent(),[3,5];case 3:return d.logger.debug("Document cloned, element located at "+y+","+C+" with size "+m+"x"+Q+" using computed rendering"),d.logger.debug("Starting DOM parsing"),b=ks(d,f),U===b.styles.backgroundColor&&(b.styles.backgroundColor=_t.TRANSPARENT),d.logger.debug("Starting renderer for element at "+v.x+","+v.y+" with size "+v.width+"x"+v.height),[4,new Qa(d,v).render(b)];case 4:F=s.sent(),s.label=5;case 5:return(null===(V=e.removeContainer)||void 0===V||V)&&(fo.destroy(w)||d.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),d.logger.debug("Finished rendering"),[2,F]}}))}))},Ia=function(A,e,t){var r=e.ownerDocument,n=r.documentElement?bt(A,getComputedStyle(r.documentElement).backgroundColor):_t.TRANSPARENT,s=r.body?bt(A,getComputedStyle(r.body).backgroundColor):_t.TRANSPARENT,o="string"==typeof t?bt(A,t):null===t?_t.TRANSPARENT:4294967295;return e===r.documentElement?ft(n)?ft(s)?o:s:n:o},ka={},La={},Sa={};let Ka;const Da=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];Sa.getSymbolSize=function(A){if(!A)throw new Error('"version" cannot be null or undefined');if(A<1||A>40)throw new Error('"version" should be in range from 1 to 40');return 4*A+17},Sa.getSymbolTotalCodewords=function(A){return Da[A]},Sa.getBCHDigit=function(A){let e=0;for(;0!==A;)e++,A>>>=1;return e},Sa.setToSJISFunction=function(A){if("function"!=typeof A)throw new Error('"toSJISFunc" is not a valid function.');Ka=A},Sa.isKanjiModeEnabled=function(){return void 0!==Ka},Sa.toSJIS=function(A){return Ka(A)};var Ta,Oa={};function Ma(){this.buffer=[],this.length=0}(Ta=Oa).L={bit:1},Ta.M={bit:0},Ta.Q={bit:3},Ta.H={bit:2},Ta.isValid=function(A){return A&&void 0!==A.bit&&A.bit>=0&&A.bit<4},Ta.from=function(A,e){if(Ta.isValid(A))return A;try{return function(A){if("string"!=typeof A)throw new Error("Param is not a string");switch(A.toLowerCase()){case"l":case"low":return Ta.L;case"m":case"medium":return Ta.M;case"q":case"quartile":return Ta.Q;case"h":case"high":return Ta.H;default:throw new Error("Unknown EC Level: "+A)}}(A)}catch(t){return e}},Ma.prototype={get:function(A){const e=Math.floor(A/8);return 1==(this.buffer[e]>>>7-A%8&1)},put:function(A,e){for(let t=0;t<e;t++)this.putBit(1==(A>>>e-t-1&1))},getLengthInBits:function(){return this.length},putBit:function(A){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),A&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var Ra=Ma;function Pa(A){if(!A||A<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=A,this.data=new Uint8Array(A*A),this.reservedBit=new Uint8Array(A*A)}Pa.prototype.set=function(A,e,t,r){const n=A*this.size+e;this.data[n]=t,r&&(this.reservedBit[n]=!0)},Pa.prototype.get=function(A,e){return this.data[A*this.size+e]},Pa.prototype.xor=function(A,e,t){this.data[A*this.size+e]^=t},Pa.prototype.isReserved=function(A,e){return this.reservedBit[A*this.size+e]};var Na=Pa,Va={};!function(A){const e=Sa.getSymbolSize;A.getRowColCoords=function(A){if(1===A)return[];const t=Math.floor(A/7)+2,r=e(A),n=145===r?26:2*Math.ceil((r-13)/(2*t-2)),s=[r-7];for(let e=1;e<t-1;e++)s[e]=s[e-1]-n;return s.push(6),s.reverse()},A.getPositions=function(e){const t=[],r=A.getRowColCoords(e),n=r.length;for(let A=0;A<n;A++)for(let e=0;e<n;e++)0===A&&0===e||0===A&&e===n-1||A===n-1&&0===e||t.push([r[A],r[e]]);return t}}(Va);var Ga={};const Ja=Sa.getSymbolSize;Ga.getPositions=function(A){const e=Ja(A);return[[0,0],[e-7,0],[0,e-7]]};var Xa={};!function(A){A.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const e=3,t=3,r=40,n=10;function s(e,t,r){switch(e){case A.Patterns.PATTERN000:return(t+r)%2==0;case A.Patterns.PATTERN001:return t%2==0;case A.Patterns.PATTERN010:return r%3==0;case A.Patterns.PATTERN011:return(t+r)%3==0;case A.Patterns.PATTERN100:return(Math.floor(t/2)+Math.floor(r/3))%2==0;case A.Patterns.PATTERN101:return t*r%2+t*r%3==0;case A.Patterns.PATTERN110:return(t*r%2+t*r%3)%2==0;case A.Patterns.PATTERN111:return(t*r%3+(t+r)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}}A.isValid=function(A){return null!=A&&""!==A&&!isNaN(A)&&A>=0&&A<=7},A.from=function(e){return A.isValid(e)?parseInt(e,10):void 0},A.getPenaltyN1=function(A){const t=A.size;let r=0,n=0,s=0,o=null,a=null;for(let i=0;i<t;i++){n=s=0,o=a=null;for(let l=0;l<t;l++){let t=A.get(i,l);t===o?n++:(n>=5&&(r+=e+(n-5)),o=t,n=1),t=A.get(l,i),t===a?s++:(s>=5&&(r+=e+(s-5)),a=t,s=1)}n>=5&&(r+=e+(n-5)),s>=5&&(r+=e+(s-5))}return r},A.getPenaltyN2=function(A){const e=A.size;let r=0;for(let t=0;t<e-1;t++)for(let n=0;n<e-1;n++){const e=A.get(t,n)+A.get(t,n+1)+A.get(t+1,n)+A.get(t+1,n+1);4!==e&&0!==e||r++}return r*t},A.getPenaltyN3=function(A){const e=A.size;let t=0,n=0,s=0;for(let r=0;r<e;r++){n=s=0;for(let o=0;o<e;o++)n=n<<1&2047|A.get(r,o),o>=10&&(1488===n||93===n)&&t++,s=s<<1&2047|A.get(o,r),o>=10&&(1488===s||93===s)&&t++}return t*r},A.getPenaltyN4=function(A){let e=0;const t=A.data.length;for(let r=0;r<t;r++)e+=A.data[r];return Math.abs(Math.ceil(100*e/t/5)-10)*n},A.applyMask=function(A,e){const t=e.size;for(let r=0;r<t;r++)for(let n=0;n<t;n++)e.isReserved(n,r)||e.xor(n,r,s(A,n,r))},A.getBestMask=function(e,t){const r=Object.keys(A.Patterns).length;let n=0,s=1/0;for(let o=0;o<r;o++){t(o),A.applyMask(o,e);const r=A.getPenaltyN1(e)+A.getPenaltyN2(e)+A.getPenaltyN3(e)+A.getPenaltyN4(e);A.applyMask(o,e),r<s&&(s=r,n=o)}return n}}(Xa);var ja={};const Wa=Oa,Ya=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],Za=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];ja.getBlocksCount=function(A,e){switch(e){case Wa.L:return Ya[4*(A-1)+0];case Wa.M:return Ya[4*(A-1)+1];case Wa.Q:return Ya[4*(A-1)+2];case Wa.H:return Ya[4*(A-1)+3];default:return}},ja.getTotalCodewordsCount=function(A,e){switch(e){case Wa.L:return Za[4*(A-1)+0];case Wa.M:return Za[4*(A-1)+1];case Wa.Q:return Za[4*(A-1)+2];case Wa.H:return Za[4*(A-1)+3];default:return}};var za={},qa={};const $a=new Uint8Array(512),Ai=new Uint8Array(256);!function(){let A=1;for(let e=0;e<255;e++)$a[e]=A,Ai[A]=e,A<<=1,256&A&&(A^=285);for(let e=255;e<512;e++)$a[e]=$a[e-255]}(),qa.log=function(A){if(A<1)throw new Error("log("+A+")");return Ai[A]},qa.exp=function(A){return $a[A]},qa.mul=function(A,e){return 0===A||0===e?0:$a[Ai[A]+Ai[e]]},function(A){const e=qa;A.mul=function(A,t){const r=new Uint8Array(A.length+t.length-1);for(let n=0;n<A.length;n++)for(let s=0;s<t.length;s++)r[n+s]^=e.mul(A[n],t[s]);return r},A.mod=function(A,t){let r=new Uint8Array(A);for(;r.length-t.length>=0;){const A=r[0];for(let s=0;s<t.length;s++)r[s]^=e.mul(t[s],A);let n=0;for(;n<r.length&&0===r[n];)n++;r=r.slice(n)}return r},A.generateECPolynomial=function(t){let r=new Uint8Array([1]);for(let n=0;n<t;n++)r=A.mul(r,new Uint8Array([1,e.exp(n)]));return r}}(za);const ei=za;function ti(A){this.genPoly=void 0,this.degree=A,this.degree&&this.initialize(this.degree)}ti.prototype.initialize=function(A){this.degree=A,this.genPoly=ei.generateECPolynomial(this.degree)},ti.prototype.encode=function(A){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(A.length+this.degree);e.set(A);const t=ei.mod(e,this.genPoly),r=this.degree-t.length;if(r>0){const A=new Uint8Array(this.degree);return A.set(t,r),A}return t};var ri=ti,ni={},si={},oi={isValid:function(A){return!isNaN(A)&&A>=1&&A<=40}},ai={};const ii="[0-9]+";let li="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";li=li.replace(/u/g,"\\u");const ci="(?:(?![A-Z0-9 $%*+\\-./:]|"+li+")(?:.|[\r\n]))+";ai.KANJI=new RegExp(li,"g"),ai.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),ai.BYTE=new RegExp(ci,"g"),ai.NUMERIC=new RegExp(ii,"g"),ai.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");const ui=new RegExp("^"+li+"$"),di=new RegExp("^"+ii+"$"),Bi=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");ai.testKanji=function(A){return ui.test(A)},ai.testNumeric=function(A){return di.test(A)},ai.testAlphanumeric=function(A){return Bi.test(A)},function(A){const e=oi,t=ai;A.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},A.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},A.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},A.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},A.MIXED={bit:-1},A.getCharCountIndicator=function(A,t){if(!A.ccBits)throw new Error("Invalid mode: "+A);if(!e.isValid(t))throw new Error("Invalid version: "+t);return t>=1&&t<10?A.ccBits[0]:t<27?A.ccBits[1]:A.ccBits[2]},A.getBestModeForData=function(e){return t.testNumeric(e)?A.NUMERIC:t.testAlphanumeric(e)?A.ALPHANUMERIC:t.testKanji(e)?A.KANJI:A.BYTE},A.toString=function(A){if(A&&A.id)return A.id;throw new Error("Invalid mode")},A.isValid=function(A){return A&&A.bit&&A.ccBits},A.from=function(e,t){if(A.isValid(e))return e;try{return function(e){if("string"!=typeof e)throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return A.NUMERIC;case"alphanumeric":return A.ALPHANUMERIC;case"kanji":return A.KANJI;case"byte":return A.BYTE;default:throw new Error("Unknown mode: "+e)}}(e)}catch(r){return t}}}(si),function(A){const e=Sa,t=ja,r=Oa,n=si,s=oi,o=e.getBCHDigit(7973);function a(A,e){return n.getCharCountIndicator(A,e)+4}function i(A,e){let t=0;return A.forEach((function(A){const r=a(A.mode,e);t+=r+A.getBitsLength()})),t}A.from=function(A,e){return s.isValid(A)?parseInt(A,10):e},A.getCapacity=function(A,r,o){if(!s.isValid(A))throw new Error("Invalid QR Code version");void 0===o&&(o=n.BYTE);const i=8*(e.getSymbolTotalCodewords(A)-t.getTotalCodewordsCount(A,r));if(o===n.MIXED)return i;const l=i-a(o,A);switch(o){case n.NUMERIC:return Math.floor(l/10*3);case n.ALPHANUMERIC:return Math.floor(l/11*2);case n.KANJI:return Math.floor(l/13);case n.BYTE:default:return Math.floor(l/8)}},A.getBestVersionForData=function(e,t){let s;const o=r.from(t,r.M);if(Array.isArray(e)){if(e.length>1)return function(e,t){for(let r=1;r<=40;r++)if(i(e,r)<=A.getCapacity(r,t,n.MIXED))return r}(e,o);if(0===e.length)return 1;s=e[0]}else s=e;return function(e,t,r){for(let n=1;n<=40;n++)if(t<=A.getCapacity(n,r,e))return n}(s.mode,s.getLength(),o)},A.getEncodedBits=function(A){if(!s.isValid(A)||A<7)throw new Error("Invalid QR Code version");let t=A<<12;for(;e.getBCHDigit(t)-o>=0;)t^=7973<<e.getBCHDigit(t)-o;return A<<12|t}}(ni);var gi={};const pi=Sa,fi=pi.getBCHDigit(1335);gi.getEncodedBits=function(A,e){const t=A.bit<<3|e;let r=t<<10;for(;pi.getBCHDigit(r)-fi>=0;)r^=1335<<pi.getBCHDigit(r)-fi;return 21522^(t<<10|r)};var wi={};const hi=si;function mi(A){this.mode=hi.NUMERIC,this.data=A.toString()}mi.getBitsLength=function(A){return 10*Math.floor(A/3)+(A%3?A%3*3+1:0)},mi.prototype.getLength=function(){return this.data.length},mi.prototype.getBitsLength=function(){return mi.getBitsLength(this.data.length)},mi.prototype.write=function(A){let e,t,r;for(e=0;e+3<=this.data.length;e+=3)t=this.data.substr(e,3),r=parseInt(t,10),A.put(r,10);const n=this.data.length-e;n>0&&(t=this.data.substr(e),r=parseInt(t,10),A.put(r,3*n+1))};var Qi=mi;const yi=si,Ci=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function Ui(A){this.mode=yi.ALPHANUMERIC,this.data=A}Ui.getBitsLength=function(A){return 11*Math.floor(A/2)+A%2*6},Ui.prototype.getLength=function(){return this.data.length},Ui.prototype.getBitsLength=function(){return Ui.getBitsLength(this.data.length)},Ui.prototype.write=function(A){let e;for(e=0;e+2<=this.data.length;e+=2){let t=45*Ci.indexOf(this.data[e]);t+=Ci.indexOf(this.data[e+1]),A.put(t,11)}this.data.length%2&&A.put(Ci.indexOf(this.data[e]),6)};var vi=Ui;const Fi=si;function bi(A){this.mode=Fi.BYTE,this.data="string"==typeof A?(new TextEncoder).encode(A):new Uint8Array(A)}bi.getBitsLength=function(A){return 8*A},bi.prototype.getLength=function(){return this.data.length},bi.prototype.getBitsLength=function(){return bi.getBitsLength(this.data.length)},bi.prototype.write=function(A){for(let e=0,t=this.data.length;e<t;e++)A.put(this.data[e],8)};var _i=bi;const xi=si,Ei=Sa;function Hi(A){this.mode=xi.KANJI,this.data=A}Hi.getBitsLength=function(A){return 13*A},Hi.prototype.getLength=function(){return this.data.length},Hi.prototype.getBitsLength=function(){return Hi.getBitsLength(this.data.length)},Hi.prototype.write=function(A){let e;for(e=0;e<this.data.length;e++){let t=Ei.toSJIS(this.data[e]);if(t>=33088&&t<=40956)t-=33088;else{if(!(t>=57408&&t<=60351))throw new Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");t-=49472}t=192*(t>>>8&255)+(255&t),A.put(t,13)}};var Ii,ki=Hi,Li={exports:{}},Si=Li.exports=Ii={single_source_shortest_paths:function(A,e,t){var r={},n={};n[e]=0;var s,o,a,i,l,c,u,d=Ii.PriorityQueue.make();for(d.push(e,0);!d.empty();)for(a in o=(s=d.pop()).value,i=s.cost,l=A[o]||{})l.hasOwnProperty(a)&&(c=i+l[a],u=n[a],(void 0===n[a]||u>c)&&(n[a]=c,d.push(a,c),r[a]=o));if(void 0!==t&&void 0===n[t]){var B=["Could not find a path from ",e," to ",t,"."].join("");throw new Error(B)}return r},extract_shortest_path_from_predecessor_list:function(A,e){for(var t=[],r=e;r;)t.push(r),A[r],r=A[r];return t.reverse(),t},find_path:function(A,e,t){var r=Ii.single_source_shortest_paths(A,e,t);return Ii.extract_shortest_path_from_predecessor_list(r,t)},PriorityQueue:{make:function(A){var e,t=Ii.PriorityQueue,r={};for(e in A=A||{},t)t.hasOwnProperty(e)&&(r[e]=t[e]);return r.queue=[],r.sorter=A.sorter||t.default_sorter,r},default_sorter:function(A,e){return A.cost-e.cost},push:function(A,e){var t={value:A,cost:e};this.queue.push(t),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};!function(A){const e=si,t=Qi,r=vi,n=_i,s=ki,o=ai,a=Sa,i=Si;function l(A){return unescape(encodeURIComponent(A)).length}function c(A,e,t){const r=[];let n;for(;null!==(n=A.exec(t));)r.push({data:n[0],index:n.index,mode:e,length:n[0].length});return r}function u(A){const t=c(o.NUMERIC,e.NUMERIC,A),r=c(o.ALPHANUMERIC,e.ALPHANUMERIC,A);let n,s;return a.isKanjiModeEnabled()?(n=c(o.BYTE,e.BYTE,A),s=c(o.KANJI,e.KANJI,A)):(n=c(o.BYTE_KANJI,e.BYTE,A),s=[]),t.concat(r,n,s).sort((function(A,e){return A.index-e.index})).map((function(A){return{data:A.data,mode:A.mode,length:A.length}}))}function d(A,o){switch(o){case e.NUMERIC:return t.getBitsLength(A);case e.ALPHANUMERIC:return r.getBitsLength(A);case e.KANJI:return s.getBitsLength(A);case e.BYTE:return n.getBitsLength(A)}}function B(A,o){let i;const l=e.getBestModeForData(A);if(i=e.from(o,l),i!==e.BYTE&&i.bit<l.bit)throw new Error('"'+A+'" cannot be encoded with mode '+e.toString(i)+".\n Suggested mode is: "+e.toString(l));switch(i!==e.KANJI||a.isKanjiModeEnabled()||(i=e.BYTE),i){case e.NUMERIC:return new t(A);case e.ALPHANUMERIC:return new r(A);case e.KANJI:return new s(A);case e.BYTE:return new n(A)}}A.fromArray=function(A){return A.reduce((function(A,e){return"string"==typeof e?A.push(B(e,null)):e.data&&A.push(B(e.data,e.mode)),A}),[])},A.fromString=function(t,r){const n=function(A){const t=[];for(let r=0;r<A.length;r++){const n=A[r];switch(n.mode){case e.NUMERIC:t.push([n,{data:n.data,mode:e.ALPHANUMERIC,length:n.length},{data:n.data,mode:e.BYTE,length:n.length}]);break;case e.ALPHANUMERIC:t.push([n,{data:n.data,mode:e.BYTE,length:n.length}]);break;case e.KANJI:t.push([n,{data:n.data,mode:e.BYTE,length:l(n.data)}]);break;case e.BYTE:t.push([{data:n.data,mode:e.BYTE,length:l(n.data)}])}}return t}(u(t,a.isKanjiModeEnabled())),s=function(A,t){const r={},n={start:{}};let s=["start"];for(let o=0;o<A.length;o++){const a=A[o],i=[];for(let A=0;A<a.length;A++){const l=a[A],c=""+o+A;i.push(c),r[c]={node:l,lastCount:0},n[c]={};for(let A=0;A<s.length;A++){const o=s[A];r[o]&&r[o].node.mode===l.mode?(n[o][c]=d(r[o].lastCount+l.length,l.mode)-d(r[o].lastCount,l.mode),r[o].lastCount+=l.length):(r[o]&&(r[o].lastCount=l.length),n[o][c]=d(l.length,l.mode)+4+e.getCharCountIndicator(l.mode,t))}}s=i}for(let e=0;e<s.length;e++)n[s[e]].end=0;return{map:n,table:r}}(n,r),o=i.find_path(s.map,"start","end"),c=[];for(let A=1;A<o.length-1;A++)c.push(s.table[o[A]].node);return A.fromArray(function(A){return A.reduce((function(A,e){const t=A.length-1>=0?A[A.length-1]:null;return t&&t.mode===e.mode?(A[A.length-1].data+=e.data,A):(A.push(e),A)}),[])}(c))},A.rawSplit=function(e){return A.fromArray(u(e,a.isKanjiModeEnabled()))}}(wi);const Ki=Sa,Di=Oa,Ti=Ra,Oi=Na,Mi=Va,Ri=Ga,Pi=Xa,Ni=ja,Vi=ri,Gi=ni,Ji=gi,Xi=si,ji=wi;function Wi(A,e,t){const r=A.size,n=Ji.getEncodedBits(e,t);let s,o;for(s=0;s<15;s++)o=1==(n>>s&1),s<6?A.set(s,8,o,!0):s<8?A.set(s+1,8,o,!0):A.set(r-15+s,8,o,!0),s<8?A.set(8,r-s-1,o,!0):s<9?A.set(8,15-s-1+1,o,!0):A.set(8,15-s-1,o,!0);A.set(r-8,8,1,!0)}function Yi(A,e,t){const r=new Ti;t.forEach((function(e){r.put(e.mode.bit,4),r.put(e.getLength(),Xi.getCharCountIndicator(e.mode,A)),e.write(r)}));const n=8*(Ki.getSymbolTotalCodewords(A)-Ni.getTotalCodewordsCount(A,e));for(r.getLengthInBits()+4<=n&&r.put(0,4);r.getLengthInBits()%8!=0;)r.putBit(0);const s=(n-r.getLengthInBits())/8;for(let o=0;o<s;o++)r.put(o%2?17:236,8);return function(A,e,t){const r=Ki.getSymbolTotalCodewords(e),n=Ni.getTotalCodewordsCount(e,t),s=r-n,o=Ni.getBlocksCount(e,t),a=r%o,i=o-a,l=Math.floor(r/o),c=Math.floor(s/o),u=c+1,d=l-c,B=new Vi(d);let g=0;const p=new Array(o),f=new Array(o);let w=0;const h=new Uint8Array(A.buffer);for(let U=0;U<o;U++){const A=U<i?c:u;p[U]=h.slice(g,g+A),f[U]=B.encode(p[U]),g+=A,w=Math.max(w,A)}const m=new Uint8Array(r);let Q,y,C=0;for(Q=0;Q<w;Q++)for(y=0;y<o;y++)Q<p[y].length&&(m[C++]=p[y][Q]);for(Q=0;Q<d;Q++)for(y=0;y<o;y++)m[C++]=f[y][Q];return m}(r,A,e)}function Zi(A,e,t,r){let n;if(Array.isArray(A))n=ji.fromArray(A);else{if("string"!=typeof A)throw new Error("Invalid data");{let r=e;if(!r){const e=ji.rawSplit(A);r=Gi.getBestVersionForData(e,t)}n=ji.fromString(A,r||40)}}const s=Gi.getBestVersionForData(n,t);if(!s)throw new Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<s)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+s+".\n")}else e=s;const o=Yi(e,t,n),a=Ki.getSymbolSize(e),i=new Oi(a);return function(A,e){const t=A.size,r=Ri.getPositions(e);for(let n=0;n<r.length;n++){const e=r[n][0],s=r[n][1];for(let r=-1;r<=7;r++)if(!(e+r<=-1||t<=e+r))for(let n=-1;n<=7;n++)s+n<=-1||t<=s+n||(r>=0&&r<=6&&(0===n||6===n)||n>=0&&n<=6&&(0===r||6===r)||r>=2&&r<=4&&n>=2&&n<=4?A.set(e+r,s+n,!0,!0):A.set(e+r,s+n,!1,!0))}}(i,e),function(A){const e=A.size;for(let t=8;t<e-8;t++){const e=t%2==0;A.set(t,6,e,!0),A.set(6,t,e,!0)}}(i),function(A,e){const t=Mi.getPositions(e);for(let r=0;r<t.length;r++){const e=t[r][0],n=t[r][1];for(let t=-2;t<=2;t++)for(let r=-2;r<=2;r++)-2===t||2===t||-2===r||2===r||0===t&&0===r?A.set(e+t,n+r,!0,!0):A.set(e+t,n+r,!1,!0)}}(i,e),Wi(i,t,0),e>=7&&function(A,e){const t=A.size,r=Gi.getEncodedBits(e);let n,s,o;for(let a=0;a<18;a++)n=Math.floor(a/3),s=a%3+t-8-3,o=1==(r>>a&1),A.set(n,s,o,!0),A.set(s,n,o,!0)}(i,e),function(A,e){const t=A.size;let r=-1,n=t-1,s=7,o=0;for(let a=t-1;a>0;a-=2)for(6===a&&a--;;){for(let t=0;t<2;t++)if(!A.isReserved(n,a-t)){let r=!1;o<e.length&&(r=1==(e[o]>>>s&1)),A.set(n,a-t,r),s--,-1===s&&(o++,s=7)}if(n+=r,n<0||t<=n){n-=r,r=-r;break}}}(i,o),isNaN(r)&&(r=Pi.getBestMask(i,Wi.bind(null,i,t))),Pi.applyMask(r,i),Wi(i,t,r),{modules:i,version:e,errorCorrectionLevel:t,maskPattern:r,segments:n}}La.create=function(A,e){if(void 0===A||""===A)throw new Error("No input text");let t,r,n=Di.M;return void 0!==e&&(n=Di.from(e.errorCorrectionLevel,Di.M),t=Gi.from(e.version),r=Pi.from(e.maskPattern),e.toSJISFunc&&Ki.setToSJISFunction(e.toSJISFunc)),Zi(A,t,n,r)};var zi={},qi={};!function(A){function e(A){if("number"==typeof A&&(A=A.toString()),"string"!=typeof A)throw new Error("Color should be defined as hex string");let e=A.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw new Error("Invalid hex color: "+A);3!==e.length&&4!==e.length||(e=Array.prototype.concat.apply([],e.map((function(A){return[A,A]})))),6===e.length&&e.push("F","F");const t=parseInt(e.join(""),16);return{r:t>>24&255,g:t>>16&255,b:t>>8&255,a:255&t,hex:"#"+e.slice(0,6).join("")}}A.getOptions=function(A){A||(A={}),A.color||(A.color={});const t=void 0===A.margin||null===A.margin||A.margin<0?4:A.margin,r=A.width&&A.width>=21?A.width:void 0,n=A.scale||4;return{width:r,scale:r?4:n,margin:t,color:{dark:e(A.color.dark||"#000000ff"),light:e(A.color.light||"#ffffffff")},type:A.type,rendererOpts:A.rendererOpts||{}}},A.getScale=function(A,e){return e.width&&e.width>=A+2*e.margin?e.width/(A+2*e.margin):e.scale},A.getImageWidth=function(e,t){const r=A.getScale(e,t);return Math.floor((e+2*t.margin)*r)},A.qrToImageData=function(e,t,r){const n=t.modules.size,s=t.modules.data,o=A.getScale(n,r),a=Math.floor((n+2*r.margin)*o),i=r.margin*o,l=[r.color.light,r.color.dark];for(let A=0;A<a;A++)for(let t=0;t<a;t++){let c=4*(A*a+t),u=r.color.light;A>=i&&t>=i&&A<a-i&&t<a-i&&(u=l[s[Math.floor((A-i)/o)*n+Math.floor((t-i)/o)]?1:0]),e[c++]=u.r,e[c++]=u.g,e[c++]=u.b,e[c]=u.a}}}(qi),function(A){const e=qi;A.render=function(A,t,r){let n=r,s=t;void 0!==n||t&&t.getContext||(n=t,t=void 0),t||(s=function(){try{return document.createElement("canvas")}catch(A){throw new Error("You need to specify a canvas element")}}()),n=e.getOptions(n);const o=e.getImageWidth(A.modules.size,n),a=s.getContext("2d"),i=a.createImageData(o,o);return e.qrToImageData(i.data,A,n),function(A,e,t){A.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=t,e.width=t,e.style.height=t+"px",e.style.width=t+"px"}(a,s,o),a.putImageData(i,0,0),s},A.renderToDataURL=function(e,t,r){let n=r;void 0!==n||t&&t.getContext||(n=t,t=void 0),n||(n={});const s=A.render(e,t,n),o=n.type||"image/png",a=n.rendererOpts||{};return s.toDataURL(o,a.quality)}}(zi);var $i={};const Al=qi;function el(A,e){const t=A.a/255,r=e+'="'+A.hex+'"';return t<1?r+" "+e+'-opacity="'+t.toFixed(2).slice(1)+'"':r}function tl(A,e,t){let r=A+e;return void 0!==t&&(r+=" "+t),r}$i.render=function(A,e,t){const r=Al.getOptions(e),n=A.modules.size,s=A.modules.data,o=n+2*r.margin,a=r.color.light.a?"<path "+el(r.color.light,"fill")+' d="M0 0h'+o+"v"+o+'H0z"/>':"",i="<path "+el(r.color.dark,"stroke")+' d="'+function(A,e,t){let r="",n=0,s=!1,o=0;for(let a=0;a<A.length;a++){const i=Math.floor(a%e),l=Math.floor(a/e);i||s||(s=!0),A[a]?(o++,a>0&&i>0&&A[a-1]||(r+=s?tl("M",i+t,.5+l+t):tl("m",n,0),n=0,s=!1),i+1<e&&A[a+1]||(r+=tl("h",o),o=0)):n++}return r}(s,n,r.margin)+'"/>',l='viewBox="0 0 '+o+" "+o+'"',c='<svg xmlns="http://www.w3.org/2000/svg" '+(r.width?'width="'+r.width+'" height="'+r.width+'" ':"")+l+' shape-rendering="crispEdges">'+a+i+"</svg>\n";return"function"==typeof t&&t(null,c),c};const rl=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then},nl=La,sl=zi,ol=$i;function al(A,e,t,r,n){const s=[].slice.call(arguments,1),o=s.length,a="function"==typeof s[o-1];if(!a&&!rl())throw new Error("Callback required as last argument");if(!a){if(o<1)throw new Error("Too few arguments provided");return 1===o?(t=e,e=r=void 0):2!==o||e.getContext||(r=t,t=e,e=void 0),new Promise((function(n,s){try{const s=nl.create(t,r);n(A(s,e,r))}catch(o){s(o)}}))}if(o<2)throw new Error("Too few arguments provided");2===o?(n=t,t=e,e=r=void 0):3===o&&(e.getContext&&void 0===n?(n=r,r=void 0):(n=r,r=t,t=e,e=void 0));try{const s=nl.create(t,r);n(null,A(s,e,r))}catch(i){n(i)}}ka.create=nl.create,ka.toCanvas=al.bind(null,sl.render),ka.toDataURL=al.bind(null,sl.renderToDataURL),ka.toString=al.bind(null,(function(A,e,t){return ol.render(A,t)}));
/*!
      * shared v12.0.0-alpha.2
      * (c) 2016-present kazuya kawaguchi and contributors
      * Released under the MIT License.
      */
const il="undefined"!=typeof window,ll=(A,e=!1)=>e?Symbol.for(A):Symbol(A),cl=A=>JSON.stringify(A).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),ul=A=>"number"==typeof A&&isFinite(A),dl=A=>"[object RegExp]"===xl(A),Bl=A=>El(A)&&0===Object.keys(A).length,gl=Object.assign,pl=Object.create,fl=(A=null)=>pl(A);let wl;const hl=()=>wl||(wl="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:fl());function ml(A){return A.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const Ql=Object.prototype.hasOwnProperty;function yl(A,e){return Ql.call(A,e)}const Cl=Array.isArray,Ul=A=>"function"==typeof A,vl=A=>"string"==typeof A,Fl=A=>"boolean"==typeof A,bl=A=>null!==A&&"object"==typeof A,_l=Object.prototype.toString,xl=A=>_l.call(A),El=A=>"[object Object]"===xl(A);function Hl(A,e){}const Il=A=>!bl(A)||Cl(A);function kl(A,e){if(Il(A)||Il(e))throw new Error("Invalid value");const t=[{src:A,des:e}];for(;t.length;){const{src:A,des:e}=t.pop();Object.keys(A).forEach((r=>{"__proto__"!==r&&(bl(A[r])&&!bl(e[r])&&(e[r]=Array.isArray(A[r])?[]:fl()),Il(e[r])||Il(A[r])?e[r]=A[r]:t.push({src:A[r],des:e[r]}))}))}}
/*!
      * message-compiler v12.0.0-alpha.2
      * (c) 2016-present kazuya kawaguchi and contributors
      * Released under the MIT License.
      */function Ll(A,e,t={}){const{domain:r,messages:n,args:s}=t,o=new SyntaxError(String(A));return o.code=A,o.domain=r,o}
/*!
      * core-base v12.0.0-alpha.2
      * (c) 2016-present kazuya kawaguchi and contributors
      * Released under the MIT License.
      */const Sl=["t","type"];function Kl(A){return function(A,e,t){for(let r=0;r<e.length;r++){const t=e[r];if(yl(A,t)&&null!=A[t])return A[t]}return t}(A,Sl)}function Dl(A){return bl(A)&&0===Kl(A)&&(yl(A,"b")||yl(A,"body"))}let Tl=null;const Ol=Ml("function:translate");function Ml(A){return e=>Tl&&Tl.emit(A,e)}const Rl=17,Pl=18,Nl=19,Vl=21,Gl=22,Jl=23;function Xl(A){return Ll(A,0,void 0)}function jl(A,e){return null!=e.locale?Yl(e.locale):Yl(A.locale)}let Wl;function Yl(A){if(vl(A))return A;if(Ul(A)){if(A.resolvedOnce&&null!=Wl)return Wl;if("Function"===A.constructor.name){const t=A();if(bl(e=t)&&Ul(e.then)&&Ul(e.catch))throw Xl(Vl);return Wl=t}throw Xl(Gl)}throw Xl(Jl);var e}function Zl(A,e,t){return[...new Set([t,...Cl(e)?e:bl(e)?Object.keys(e):vl(e)?[e]:[t]])]}function zl(A,e,t){let r=!0;for(let n=0;n<e.length&&Fl(r);n++){const s=e[n];vl(s)&&(r=ql(A,e[n],t))}return r}function ql(A,e,t){let r;const n=e.split("-");do{r=$l(A,n.join("-"),t),n.splice(-1,1)}while(n.length&&!0===r);return r}function $l(A,e,t){let r=!1;if(!A.includes(e)&&(r=!0,e)){r="!"!==e[e.length-1];const n=e.replace(/!/g,"");A.push(n),(Cl(t)||El(t))&&t[n]&&(r=t[n])}return r}function Ac(A,e){return bl(A)?A[e]:null}const ec="en-US",tc=A=>`${A.charAt(0).toLocaleUpperCase()}${A.substr(1)}`;let rc=null;const nc=()=>rc;let sc=null;const oc=A=>{sc=A};let ac=0;function ic(A={}){const e=Ul(A.onWarn)?A.onWarn:Hl,t=vl(A.version)?A.version:"12.0.0-alpha.2",r=vl(A.locale)||Ul(A.locale)?A.locale:ec,n=Ul(r)?ec:r,s=Cl(A.fallbackLocale)||El(A.fallbackLocale)||vl(A.fallbackLocale)||!1===A.fallbackLocale?A.fallbackLocale:n,o=El(A.messages)?A.messages:lc(n),a=El(A.datetimeFormats)?A.datetimeFormats:lc(n),i=El(A.numberFormats)?A.numberFormats:lc(n),l=gl(fl(),A.modifiers,{upper:(A,e)=>"text"===e&&vl(A)?A.toUpperCase():"vnode"===e&&bl(A)&&"__v_isVNode"in A?A.children.toUpperCase():A,lower:(A,e)=>"text"===e&&vl(A)?A.toLowerCase():"vnode"===e&&bl(A)&&"__v_isVNode"in A?A.children.toLowerCase():A,capitalize:(A,e)=>"text"===e&&vl(A)?tc(A):"vnode"===e&&bl(A)&&"__v_isVNode"in A?tc(A.children):A}),c=A.pluralRules||fl(),u=Ul(A.missing)?A.missing:null,d=!Fl(A.missingWarn)&&!dl(A.missingWarn)||A.missingWarn,B=!Fl(A.fallbackWarn)&&!dl(A.fallbackWarn)||A.fallbackWarn,g=!!A.fallbackFormat,p=!!A.unresolving,f=Ul(A.postTranslation)?A.postTranslation:null,w=El(A.processor)?A.processor:null,h=!Fl(A.warnHtmlMessage)||A.warnHtmlMessage,m=!!A.escapeParameter,Q=Ul(A.messageCompiler)?A.messageCompiler:void 0,y=Ul(A.messageResolver)?A.messageResolver:Ac,C=Ul(A.localeFallbacker)?A.localeFallbacker:Zl,U=bl(A.fallbackContext)?A.fallbackContext:void 0,v=A,F=bl(v.__datetimeFormatters)?v.__datetimeFormatters:new Map,b=bl(v.__numberFormatters)?v.__numberFormatters:new Map,_=bl(v.__meta)?v.__meta:{};ac++;const x={version:t,cid:ac,locale:r,fallbackLocale:s,messages:o,modifiers:l,pluralRules:c,missing:u,missingWarn:d,fallbackWarn:B,fallbackFormat:g,unresolving:p,postTranslation:f,processor:w,warnHtmlMessage:h,escapeParameter:m,messageCompiler:Q,messageResolver:y,localeFallbacker:C,fallbackContext:U,onWarn:e,__meta:_};return x.datetimeFormats=a,x.numberFormats=i,x.__datetimeFormatters=F,x.__numberFormatters=b,__INTLIFY_PROD_DEVTOOLS__&&function(A,e,t){Tl&&Tl.emit("i18n:init",{timestamp:Date.now(),i18n:A,version:e,meta:t})}(x,t,_),x}const lc=A=>({[A]:fl()});function cc(A,e,t,r,n){const{missing:s,onWarn:o}=A;if(null!==s){const r=s(A,t,e,n);return vl(r)?r:e}return e}function uc(A,e,t){A.__localeChainCache=new Map,A.localeFallbacker(A,t,e)}function dc(A,e){const t=e.indexOf(A);if(-1===t)return!1;for(let s=t+1;s<e.length;s++)if(r=A,n=e[s],r!==n&&r.split("-")[0]===n.split("-")[0])return!0;var r,n;return!1}function Bc(A,...e){const{datetimeFormats:t,unresolving:r,fallbackLocale:n,onWarn:s,localeFallbacker:o}=A,{__datetimeFormatters:a}=A,[i,l,c,u]=pc(...e);Fl(c.missingWarn)?c.missingWarn:A.missingWarn,Fl(c.fallbackWarn)?c.fallbackWarn:A.fallbackWarn;const d=!!c.part,B=jl(A,c),g=o(A,n,B);if(!vl(i)||""===i)return new Intl.DateTimeFormat(B,u).format(l);let p,f={},w=null;for(let Q=0;Q<g.length&&(p=g[Q],f=t[p]||{},w=f[i],!El(w));Q++)cc(A,i,p,0,"datetime format");if(!El(w)||!vl(p))return r?-1:i;let h=`${p}__${i}`;Bl(u)||(h=`${h}__${JSON.stringify(u)}`);let m=a.get(h);return m||(m=new Intl.DateTimeFormat(p,gl({},w,u)),a.set(h,m)),d?m.formatToParts(l):m.format(l)}const gc=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function pc(...A){const[e,t,r,n]=A,s=fl();let o,a=fl();if(vl(e)){const A=e.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!A)throw Xl(Nl);const t=A[3]?A[3].trim().startsWith("T")?`${A[1].trim()}${A[3].trim()}`:`${A[1].trim()}T${A[3].trim()}`:A[1].trim();o=new Date(t);try{o.toISOString()}catch(i){throw Xl(Nl)}}else if("[object Date]"===xl(e)){if(isNaN(e.getTime()))throw Xl(Pl);o=e}else{if(!ul(e))throw Xl(Rl);o=e}return vl(t)?s.key=t:El(t)&&Object.keys(t).forEach((A=>{gc.includes(A)?a[A]=t[A]:s[A]=t[A]})),vl(r)?s.locale=r:El(r)&&(a=r),El(n)&&(a=n),[s.key||"",o,s,a]}function fc(A,e,t){const r=A;for(const n in t){const A=`${e}__${n}`;r.__datetimeFormatters.has(A)&&r.__datetimeFormatters.delete(A)}}function wc(A,...e){const{numberFormats:t,unresolving:r,fallbackLocale:n,onWarn:s,localeFallbacker:o}=A,{__numberFormatters:a}=A,[i,l,c,u]=mc(...e);Fl(c.missingWarn)?c.missingWarn:A.missingWarn,Fl(c.fallbackWarn)?c.fallbackWarn:A.fallbackWarn;const d=!!c.part,B=jl(A,c),g=o(A,n,B);if(!vl(i)||""===i)return new Intl.NumberFormat(B,u).format(l);let p,f={},w=null;for(let Q=0;Q<g.length&&(p=g[Q],f=t[p]||{},w=f[i],!El(w));Q++)cc(A,i,p,0,"number format");if(!El(w)||!vl(p))return r?-1:i;let h=`${p}__${i}`;Bl(u)||(h=`${h}__${JSON.stringify(u)}`);let m=a.get(h);return m||(m=new Intl.NumberFormat(p,gl({},w,u)),a.set(h,m)),d?m.formatToParts(l):m.format(l)}const hc=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function mc(...A){const[e,t,r,n]=A,s=fl();let o=fl();if(!ul(e))throw Xl(Rl);const a=e;return vl(t)?s.key=t:El(t)&&Object.keys(t).forEach((A=>{hc.includes(A)?o[A]=t[A]:s[A]=t[A]})),vl(r)?s.locale=r:El(r)&&(o=r),El(n)&&(o=n),[s.key||"",a,s,o]}function Qc(A,e,t){const r=A;for(const n in t){const A=`${e}__${n}`;r.__numberFormatters.has(A)&&r.__numberFormatters.delete(A)}}const yc=A=>A,Cc=A=>"",Uc=A=>0===A.length?"":function(A,e=""){return A.reduce(((A,t,r)=>0===r?A+t:A+e+t),"")}(A),vc=A=>null==A?"":Cl(A)||El(A)&&A.toString===_l?JSON.stringify(A,null,2):String(A);function Fc(A,e){return A=Math.abs(A),2===e?A?A>1?1:0:1:A?Math.min(A,2):0}function bc(A={}){const e=A.locale,t=function(A){const e=ul(A.pluralIndex)?A.pluralIndex:-1;return A.named&&(ul(A.named.count)||ul(A.named.n))?ul(A.named.count)?A.named.count:ul(A.named.n)?A.named.n:e:e}(A),r=bl(A.pluralRules)&&vl(e)&&Ul(A.pluralRules[e])?A.pluralRules[e]:Fc,n=bl(A.pluralRules)&&vl(e)&&Ul(A.pluralRules[e])?Fc:void 0,s=A.list||[],o=A.named||fl();function a(e,t){const r=Ul(A.messages)?A.messages(e,!!t):!!bl(A.messages)&&A.messages[e];return r||(A.parent?A.parent.message(e):Cc)}ul(A.pluralIndex)&&function(A,e){e.count||(e.count=A),e.n||(e.n=A)}(t,o);const i=El(A.processor)&&Ul(A.processor.normalize)?A.processor.normalize:Uc,l=El(A.processor)&&Ul(A.processor.interpolate)?A.processor.interpolate:vc,c={list:A=>s[A],named:A=>o[A],plural:A=>A[r(t,A.length,n)],linked:(e,...t)=>{const[r,n]=t;let s="text",o="";1===t.length?bl(r)?(o=r.modifier||o,s=r.type||s):vl(r)&&(o=r||o):2===t.length&&(vl(r)&&(o=r||o),vl(n)&&(s=n||s));const i=a(e,!0)(c),l="vnode"===s&&Cl(i)&&o?i[0]:i;return o?(u=o,A.modifiers?A.modifiers[u]:yc)(l,s):l;var u},message:a,type:El(A.processor)&&vl(A.processor.type)?A.processor.type:"text",interpolate:l,normalize:i,values:gl(fl(),s,o)};return c}const _c=()=>"",xc=A=>Ul(A);function Ec(A,...e){const{fallbackFormat:t,postTranslation:r,unresolving:n,messageCompiler:s,fallbackLocale:o,messages:a}=A,[i,l]=kc(...e),c=(Fl(l.missingWarn)?l.missingWarn:A.missingWarn,Fl(l.fallbackWarn)?l.fallbackWarn:A.fallbackWarn,Fl(l.escapeParameter)?l.escapeParameter:A.escapeParameter),u=!!l.resolvedMessage,d=vl(l.default)||Fl(l.default)?Fl(l.default)?s?i:()=>i:l.default:t?s?i:()=>i:null,B=t||null!=d&&(vl(d)||Ul(d)),g=jl(A,l);c&&function(A){Cl(A.list)?A.list=A.list.map((A=>vl(A)?ml(A):A)):bl(A.named)&&Object.keys(A.named).forEach((e=>{vl(A.named[e])&&(A.named[e]=ml(A.named[e]))}))}(l);let[p,f,w]=u?[i,g,a[g]||fl()]:Hc(A,i,g,o),h=p,m=i;if(u||vl(h)||Dl(h)||xc(h)||B&&(h=d,m=h),!(u||(vl(h)||Dl(h)||xc(h))&&vl(f)))return n?-1:i;let Q=!1;const y=xc(h)?h:Ic(A,i,f,h,m,(()=>{Q=!0}));if(Q)return h;const C=function(A,e,t,r){const{modifiers:n,pluralRules:s,messageResolver:o,fallbackLocale:a,fallbackWarn:i,missingWarn:l,fallbackContext:c}=A,u=(r,n)=>{let s=o(t,r);if(null==s&&(c||n)){const[,,t]=Hc(c||A,r,e,a);s=o(t,r)}if(vl(s)||Dl(s)){let t=!1;const n=Ic(A,r,e,s,r,(()=>{t=!0}));return t?_c:n}return xc(s)?s:_c},d={locale:e,modifiers:n,pluralRules:s,messages:u};return A.processor&&(d.processor=A.processor),r.list&&(d.list=r.list),r.named&&(d.named=r.named),ul(r.plural)&&(d.pluralIndex=r.plural),d}(A,f,w,l),U=function(A,e,t){const r=e(t);return r}(0,y,bc(C)),v=r?r(U,i):U;if(__INTLIFY_PROD_DEVTOOLS__){const e={timestamp:Date.now(),key:vl(i)?i:xc(h)?h.key:"",locale:f||(xc(h)?h.locale:""),format:vl(h)?h:xc(h)?h.source:"",message:v};e.meta=gl({},A.__meta,nc()||{}),Ol(e)}return v}function Hc(A,e,t,r,n,s){const{messages:o,onWarn:a,messageResolver:i,localeFallbacker:l}=A,c=l(A,r,t);let u,d=fl(),B=null;for(let g=0;g<c.length&&(u=c[g],d=o[u]||fl(),null===(B=i(d,e))&&(B=d[e]),!(vl(B)||Dl(B)||xc(B)));g++)if(!dc(u,c)){const t=cc(A,e,u,0,"translate");t!==e&&(B=t)}return[B,u,d]}function Ic(A,e,t,r,n,s){const{messageCompiler:o,warnHtmlMessage:a}=A;if(xc(r)){const A=r;return A.locale=A.locale||t,A.key=A.key||e,A}if(null==o){const A=()=>r;return A.locale=t,A.key=e,A}const i=o(r,function(A,e,t,r,n,s){return{locale:e,key:t,warnHtmlMessage:n,onError:A=>{throw s&&s(A),A},onCacheKey:A=>((A,e,t)=>cl({l:A,k:e,s:t}))(e,t,A)}}(0,t,n,0,a,s));return i.locale=t,i.key=e,i.source=r,i}function kc(...A){const[e,t,r]=A,n=fl();if(!(vl(e)||ul(e)||xc(e)||Dl(e)))throw Xl(Rl);const s=ul(e)?String(e):(xc(e),e);return ul(t)?n.plural=t:vl(t)?n.default=t:El(t)&&!Bl(t)?n.named=t:Cl(t)&&(n.list=t),ul(r)?n.plural=r:vl(r)?n.default=r:El(r)&&gl(n,r),[s,n]}"boolean"!=typeof __INTLIFY_PROD_DEVTOOLS__&&(hl().__INTLIFY_PROD_DEVTOOLS__=!1),"boolean"!=typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&(hl().__INTLIFY_DROP_MESSAGE_COMPILER__=!1);const Lc=24,Sc=25,Kc=26,Dc=27,Tc=31,Oc=32;function Mc(A,...e){return Ll(A,0,void 0)}const Rc=ll("__translateVNode"),Pc=ll("__datetimeParts"),Nc=ll("__numberParts"),Vc=ll("__setPluralRules"),Gc=ll("__injectWithOption"),Jc=ll("__dispose");function Xc(A){if(!bl(A))return A;for(const e in A)if(yl(A,e))if(e.includes(".")){const t=e.split("."),r=t.length-1;let n=A,s=!1;for(let A=0;A<r;A++){if("__proto__"===t[A])throw new Error(`unsafe key: ${t[A]}`);if(t[A]in n||(n[t[A]]=fl()),!bl(n[t[A]])){s=!0;break}n=n[t[A]]}s||(n[t[r]]=A[e],delete A[e]),bl(n[t[r]])&&Xc(n[t[r]])}else bl(A[e])&&Xc(A[e]);return A}function jc(A,e){const{messages:t,__i18n:r,messageResolver:n,flatJson:s}=e,o=El(t)?t:Cl(r)?fl():{[A]:fl()};if(Cl(r)&&r.forEach((A=>{if("locale"in A&&"resource"in A){const{locale:e,resource:t}=A;e?(o[e]=o[e]||fl(),kl(t,o[e])):kl(t,o)}else vl(A)&&kl(JSON.parse(A),o)})),null==n&&s)for(const a in o)yl(o,a)&&Xc(o[a]);return o}function Wc(A){return A.type}function Yc(A){return x(E,null,A,0)}const Zc=()=>[],zc=()=>!1;let qc=0;function $c(A){return(e,t,r,n)=>A(t,r,v()||void 0,n)}function Au(A={}){const{__root:e,__injectWithOption:t}=A,r=void 0===e,n=A.flatJson,s=il?f:w;let o=!Fl(A.inheritLocale)||A.inheritLocale;const a=s(e&&o?e.locale.value:vl(A.locale)?A.locale:ec),i=s(e&&o?e.fallbackLocale.value:vl(A.fallbackLocale)||Cl(A.fallbackLocale)||El(A.fallbackLocale)||!1===A.fallbackLocale?A.fallbackLocale:a.value),l=s(jc(a.value,A)),c=s(El(A.datetimeFormats)?A.datetimeFormats:{[a.value]:{}}),u=s(El(A.numberFormats)?A.numberFormats:{[a.value]:{}});let d=e?e.missingWarn:!Fl(A.missingWarn)&&!dl(A.missingWarn)||A.missingWarn,B=e?e.fallbackWarn:!Fl(A.fallbackWarn)&&!dl(A.fallbackWarn)||A.fallbackWarn,g=e?e.fallbackRoot:!Fl(A.fallbackRoot)||A.fallbackRoot,p=!!A.fallbackFormat,Q=Ul(A.missing)?A.missing:null,y=Ul(A.missing)?$c(A.missing):null,C=Ul(A.postTranslation)?A.postTranslation:null,U=e?e.warnHtmlMessage:!Fl(A.warnHtmlMessage)||A.warnHtmlMessage,v=!!A.escapeParameter;const F=e?e.modifiers:El(A.modifiers)?A.modifiers:{};let b,_=A.pluralRules||e&&e.pluralRules;b=(()=>{r&&oc(null);const e={version:"12.0.0-alpha.2",locale:a.value,fallbackLocale:i.value,messages:l.value,modifiers:F,pluralRules:_,missing:null===y?void 0:y,missingWarn:d,fallbackWarn:B,fallbackFormat:p,unresolving:!0,postTranslation:null===C?void 0:C,warnHtmlMessage:U,escapeParameter:v,messageResolver:A.messageResolver,messageCompiler:A.messageCompiler,__meta:{framework:"vue"}};e.datetimeFormats=c.value,e.numberFormats=u.value,e.__datetimeFormatters=El(b)?b.__datetimeFormatters:void 0,e.__numberFormatters=El(b)?b.__numberFormatters:void 0;const t=ic(e);return r&&oc(t),t})(),uc(b,a.value,i.value);const x=h({get:()=>a.value,set:A=>{b.locale=A,a.value=A}}),E=h({get:()=>i.value,set:A=>{b.fallbackLocale=A,i.value=A,uc(b,a.value,A)}}),H=h((()=>l.value)),I=h((()=>Object.keys(l.value).sort())),k=h((()=>c.value)),L=h((()=>u.value)),S=(A,t,n,s,o,d)=>{let B;a.value,i.value,l.value,c.value,u.value;try{__INTLIFY_PROD_DEVTOOLS__,r||(b.fallbackContext=e?sc:void 0),B=A(b)}finally{__INTLIFY_PROD_DEVTOOLS__,r||(b.fallbackContext=void 0)}if("translate exists"!==n&&ul(B)&&-1===B||"translate exists"===n&&!B){const[A,r]=t();return e&&g?s(e):o(A)}if(d(B))return B;throw Mc(Lc)};function K(...A){return S((e=>Reflect.apply(Ec,null,[e,...A])),(()=>kc(...A)),"translate",(e=>Reflect.apply(e.t,e,[...A])),(A=>A),(A=>vl(A)))}const D={normalize:function(A){return A.map((A=>vl(A)||ul(A)||Fl(A)?Yc(String(A)):A))},interpolate:A=>A,type:"vnode"};function T(A){let e=null;const t=function(A,e,t){const r=vl(t)?t:ec,n=A;n.__localeChainCache||(n.__localeChainCache=new Map);let s=n.__localeChainCache.get(r);if(!s){s=[];let A=[t];for(;Cl(A);)A=zl(s,A,e);const o=Cl(e)||!El(e)?e:e.default?e.default:null;A=vl(o)?[o]:o,Cl(A)&&zl(s,A,!1),n.__localeChainCache.set(r,s)}return s}(b,i.value,a.value);for(let r=0;r<t.length;r++){const n=l.value[t[r]]||{},s=b.messageResolver(n,A);if(null!=s){e=s;break}}return e}function O(A){return l.value[A]||{}}qc++,e&&il&&(m(e.locale,(A=>{o&&(a.value=A,b.locale=A,uc(b,a.value,i.value))})),m(e.fallbackLocale,(A=>{o&&(i.value=A,b.fallbackLocale=A,uc(b,a.value,i.value))})));const M={id:qc,locale:x,fallbackLocale:E,get inheritLocale(){return o},set inheritLocale(A){o=A,A&&e&&(a.value=e.locale.value,i.value=e.fallbackLocale.value,uc(b,a.value,i.value))},availableLocales:I,messages:H,get modifiers(){return F},get pluralRules(){return _||{}},get isGlobal(){return r},get missingWarn(){return d},set missingWarn(A){d=A,b.missingWarn=d},get fallbackWarn(){return B},set fallbackWarn(A){B=A,b.fallbackWarn=B},get fallbackRoot(){return g},set fallbackRoot(A){g=A},get fallbackFormat(){return p},set fallbackFormat(A){p=A,b.fallbackFormat=p},get warnHtmlMessage(){return U},set warnHtmlMessage(A){U=A,b.warnHtmlMessage=A},get escapeParameter(){return v},set escapeParameter(A){v=A,b.escapeParameter=A},t:K,getLocaleMessage:O,setLocaleMessage:function(A,e){if(n){const t={[A]:e};for(const A in t)yl(t,A)&&Xc(t[A]);e=t[A]}l.value[A]=e,b.messages=l.value},mergeLocaleMessage:function(A,e){l.value[A]=l.value[A]||{};const t={[A]:e};if(n)for(const r in t)yl(t,r)&&Xc(t[r]);kl(e=t[A],l.value[A]),b.messages=l.value},getPostTranslationHandler:function(){return Ul(C)?C:null},setPostTranslationHandler:function(A){C=A,b.postTranslation=A},getMissingHandler:function(){return Q},setMissingHandler:function(A){null!==A&&(y=$c(A)),Q=A,b.missing=y},[Vc]:function(A){_=A,b.pluralRules=_}};return M.datetimeFormats=k,M.numberFormats=L,M.rt=function(...A){const[e,t,r]=A;if(r&&!bl(r))throw Mc(Sc);return K(e,t,gl({resolvedMessage:!0},r||{}))},M.te=function(A,e){return S((()=>{if(!A)return!1;const t=O(vl(e)?e:a.value),r=b.messageResolver(t,A);return Dl(r)||xc(r)||vl(r)}),(()=>[A]),"translate exists",(t=>Reflect.apply(t.te,t,[A,e])),zc,(A=>Fl(A)))},M.tm=function(A){const t=T(A);return null!=t?t:e&&e.tm(A)||{}},M.d=function(...A){return S((e=>Reflect.apply(Bc,null,[e,...A])),(()=>pc(...A)),"datetime format",(e=>Reflect.apply(e.d,e,[...A])),(()=>""),(A=>vl(A)))},M.n=function(...A){return S((e=>Reflect.apply(wc,null,[e,...A])),(()=>mc(...A)),"number format",(e=>Reflect.apply(e.n,e,[...A])),(()=>""),(A=>vl(A)))},M.getDateTimeFormat=function(A){return c.value[A]||{}},M.setDateTimeFormat=function(A,e){c.value[A]=e,b.datetimeFormats=c.value,fc(b,A,e)},M.mergeDateTimeFormat=function(A,e){c.value[A]=gl(c.value[A]||{},e),b.datetimeFormats=c.value,fc(b,A,e)},M.getNumberFormat=function(A){return u.value[A]||{}},M.setNumberFormat=function(A,e){u.value[A]=e,b.numberFormats=u.value,Qc(b,A,e)},M.mergeNumberFormat=function(A,e){u.value[A]=gl(u.value[A]||{},e),b.numberFormats=u.value,Qc(b,A,e)},M[Gc]=t,M[Rc]=function(...A){return S((e=>{let t;const r=e;try{r.processor=D,t=Reflect.apply(Ec,null,[r,...A])}finally{r.processor=null}return t}),(()=>kc(...A)),"translate",(e=>e[Rc](...A)),(A=>[Yc(A)]),(A=>Cl(A)))},M[Pc]=function(...A){return S((e=>Reflect.apply(Bc,null,[e,...A])),(()=>pc(...A)),"datetime format",(e=>e[Pc](...A)),Zc,(A=>vl(A)||Cl(A)))},M[Nc]=function(...A){return S((e=>Reflect.apply(wc,null,[e,...A])),(()=>mc(...A)),"number format",(e=>e[Nc](...A)),Zc,(A=>vl(A)||Cl(A)))},M}const eu={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:A=>"parent"===A||"global"===A,default:"parent"},i18n:{type:Object}};function tu(){return U}function ru(A,e,t,r){const{slots:n,attrs:s}=e;return()=>{const e={part:!0};let o=fl();A.locale&&(e.locale=A.locale),vl(A.format)?e.key=A.format:bl(A.format)&&(vl(A.format.key)&&(e.key=A.format.key),o=Object.keys(A.format).reduce(((e,r)=>t.includes(r)?gl(fl(),e,{[r]:A.format[r]}):e),fl()));const a=r(A.value,e,o);let i=[e.key];Cl(a)?i=a.map(((A,e)=>{const t=n[A.type],r=t?t({[A.type]:A.value,index:e,parts:a}):[A.value];var s;return Cl(s=r)&&!vl(s[0])&&(r[0].key=`${A.type}-${e}`),r})):vl(a)&&(i=[a]);const l=gl(fl(),s),c=vl(A.tag)||bl(A.tag)?A.tag:tu();return C(c,l,i)}}const nu=y({name:"i18n-d",props:gl({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},eu),setup(A,e){const t=A.i18n||iu({useScope:A.scope,__useComponent:!0});return ru(A,e,gc,((...A)=>t[Pc](...A)))}}),su=y({name:"i18n-n",props:gl({value:{type:Number,required:!0},format:{type:[String,Object]}},eu),setup(A,e){const t=A.i18n||iu({useScope:A.scope,__useComponent:!0});return ru(A,e,hc,((...A)=>t[Nc](...A)))}}),ou=y({name:"i18n-t",props:gl({},{keypath:{type:String,required:!0},plural:{type:[Number,String],validator:A=>ul(A)||!isNaN(A)}},eu),setup(A,e){const{slots:t,attrs:r}=e,n=A.i18n||iu({useScope:A.scope,__useComponent:!0});return()=>{const s=Object.keys(t).filter((A=>"_"!==A)),o=fl();A.locale&&(o.locale=A.locale),void 0!==A.plural&&(o.plural=vl(A.plural)?+A.plural:A.plural);const a=function({slots:A},e){return 1===e.length&&"default"===e[0]?(A.default?A.default():[]).reduce(((A,e)=>[...A,...e.type===U?e.children:[e]]),[]):e.reduce(((e,t)=>{const r=A[t];return r&&(e[t]=r()),e}),fl())}(e,s),i=n[Rc](A.keypath,a,o),l=gl(fl(),r),c=vl(A.tag)||bl(A.tag)?A.tag:tu();return C(c,l,i)}}}),au=ll("global-vue-i18n");function iu(A={}){const e=v();if(null==e)throw Mc(Kc);if(!e.isCE&&null!=e.appContext.app&&!e.appContext.app.__VUE_I18N_SYMBOL__)throw Mc(Dc);const t=function(A){const e=F(A.isCE?au:A.appContext.app.__VUE_I18N_SYMBOL__);if(!e)throw Mc(A.isCE?Tc:Oc);return e}(e),r=function(A){return A.global}(t),n=Wc(e),s=function(A,e){return Bl(A)?"__i18n"in e?"local":"global":A.useScope?A.useScope:"local"}(A,n);if("global"===s)return function(A,e,t){let r=bl(e.messages)?e.messages:fl();"__i18nGlobal"in t&&(r=jc(A.locale.value,{messages:r,__i18n:t.__i18nGlobal}));const n=Object.keys(r);if(n.length&&n.forEach((e=>{A.mergeLocaleMessage(e,r[e])})),bl(e.datetimeFormats)){const t=Object.keys(e.datetimeFormats);t.length&&t.forEach((t=>{A.mergeDateTimeFormat(t,e.datetimeFormats[t])}))}if(bl(e.numberFormats)){const t=Object.keys(e.numberFormats);t.length&&t.forEach((t=>{A.mergeNumberFormat(t,e.numberFormats[t])}))}}(r,A,n),r;if("parent"===s){let n=function(A,e,t=!1){let r=null;const n=e.root;let s=function(A,e=!1){return null==A?null:e&&A.vnode.ctx||A.parent}(e,t);for(;null!=s&&(r=A.__getInstance(s),null==r)&&n!==s;)s=s.parent;return r}(t,e,A.__useComponent);return null==n&&(n=r),n}const o=t;let a=o.__getInstance(e);if(null==a){const t=gl({},A);"__i18n"in n&&(t.__i18n=n.__i18n),r&&(t.__root=r),a=Au(t),o.__composerExtend&&(a[Jc]=o.__composerExtend(a)),function(A,e,t){b((()=>{}),e),_((()=>{const r=t;A.__deleteInstance(e);const n=r[Jc];n&&(n(),delete r[Jc])}),e)}(o,e,a),o.__setInstance(e,a)}return a}const lu=["locale","fallbackLocale","availableLocales"],cu=["t","rt","d","n","tm","te"];if("boolean"!=typeof __VUE_I18N_FULL_INSTALL__&&(hl().__VUE_I18N_FULL_INSTALL__=!0),"boolean"!=typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&(hl().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),"boolean"!=typeof __INTLIFY_PROD_DEVTOOLS__&&(hl().__INTLIFY_PROD_DEVTOOLS__=!1),__INTLIFY_PROD_DEVTOOLS__){const A=hl();A.__INTLIFY__=!0,uu=A.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__,Tl=uu}var uu;const du={common:{app_name:"Cooltrade",loading:"加载中...",refresh:"刷新",refreshing:"刷新中...",save:"保存",cancel:"取消",confirm:"确认",back:"返回",success:"成功",error:"错误",warning:"警告",info:"信息",yes:"是",no:"否",ok:"确定",language:"语言",sending:"发送中...",registering:"注册中...",submitting:"提交中...",retry:"重试",privacy_policy:"隐私政策",about_us:"关于我们",no_data:"暂无数据",load_data:"加载数据",popular_tokens:"热门代币",popular_stocks:"热门股票",quick_switch:"快速切换",search:"搜索",coming_soon:"开发中",my_favorites:"我的收藏"},market:{crypto:"加密货币",stock:"美股",china:"A股"},auth:{login:"登录",register:"注册",logout:"退出登录",email:"邮箱",password:"密码",confirm_password:"确认密码",verification_code:"验证码",send_code:"发送验证码",forgot_password:"忘记密码",reset_password:"重置密码",change_password:"修改密码",current_password:"当前密码",new_password:"新密码",confirm_new_password:"确认密码",invitation_code:"邀请码",login_success:"登录成功",register_success:"注册成功",logout_success:"退出成功",password_changed:"密码已修改",password_reset:"密码已重置",code_sent:"验证码已发送",no_account:"还没有账号？",register_now:"立即注册",have_account:"已有账号？",login_now:"立即登录",email_placeholder:"请输入邮箱",password_placeholder:"请输入密码",verification_code_placeholder:"请输入验证码",invitation_code_placeholder:"请输入邀请码",retry_in_seconds:"{seconds}秒后重试",password_requirements:"密码至少6位，包含字母和数字",password_changed_success:"密码修改成功",login_required_for_password_change:"请先登录后再修改密码",go_to_login:"去登录",enter_current_and_new_password:"请输入当前密码和新密码",new_password_placeholder:"请输入新密码",confirm_new_password_placeholder:"请再次输入新密码",resetting:"重置中...",reset_success:"密码重置成功",reset_success_message:"您的密码已成功重置，请使用新密码登录。",back_to_login:"返回登录"},profile:{profile:"个人资料",username:"用户名",email:"邮箱",created_at:"创建时间",updated_at:"更新时间",registration_time:"注册时间",language_preference:"语言偏好",generate_invitation:"生成邀请码",invitation_codes:"邀请码列表",language_settings:"语言设置",select_language:"选择语言",chinese:"简体中文",english:"英文",japanese:"日语",korean:"韩语"},analysis:{technical_analysis:"技术分析",market_data:"市场数据",trend_analysis:"趋势分析",indicators:"指标",trading_advice:"交易建议",risk_assessment:"风险评估",current_price:"当前价格",last_update:"最后更新",refresh_data:"刷新数据",refreshing:"正在刷新...",up_probability:"上涨概率",sideways_probability:"横盘概率",down_probability:"下跌概率",trend_summary:"趋势总结",action:"操作建议",reason:"建议原因",entry_price:"入场价格",stop_loss:"止损价格",take_profit:"止盈价格",risk_level:"风险等级",risk_score:"风险分数",risk_details:"风险详情",high_risk:"高风险",medium_risk:"中等风险",low_risk:"低风险",market_report:"{symbol}市场分析报告",snapshot_price:"快照价格",share_to_twitter:"分享到推特",save_image:"保存图片",uptrend:"上涨趋势",sideways:"横盘整理",downtrend:"下跌趋势",market_trend_analysis:"市场趋势分析",technical_indicators:"技术指标",recommended_action:"建议操作",risk_factors:"风险因素",refreshing_data:"正在刷新数据",refreshing_data_ellipsis:"正在刷新数据...",calculating_indicators:"正在获取市场数据并进行技术指标计算...",analyzing_trends:"正在进行趋势分析和概率评估...",generating_advice:"正在生成交易建议和风险评估...",finalizing_data:"最终数据整合中，即将完成...",force_refresh:"强制刷新",minute_ago:"{n}分钟前",hour_ago:"{n}小时前",day_ago:"{n}天前",preparing_analysis_report:"正在准备分析报告...",generating_new_report:"正在生成新的分析报告，请耐心等待...",please_wait:"请耐心等待，这可能需要一些时间",timeout_error:"请求超时，服务器正在处理中，请稍后重试",refresh_report:"刷新报告",refresh_report_too_soon:"报告未超过12小时，暂不可刷新"},indicators:{rsi:"RSI",macd:"MACD",bollinger_bands:"布林带",bias:"BIAS",psy:"PSY",dmi:"DMI",vwap:"VWAP",funding_rate:"资金费率",exchange_netflow:"交易所净流入",nupl:"NUPL",mayer_multiple:"梅耶倍数"},errors:{network_error:"网络错误",server_error:"服务器错误",not_found:"未找到",unauthorized:"未授权",forbidden:"禁止访问",validation_error:"验证错误",unknown_error:"未知错误",token_expired:"登录已过期，请重新登录",invalid_credentials:"邮箱或密码错误",invalid_code:"验证码无效或已过期",passwords_not_match:"两次输入的密码不一致",email_already_registered:"该邮箱已被注册",email_not_registered:"该邮箱未注册",invalid_invitation_code:"邀请码无效或已使用",weak_password:"密码强度不足",fill_all_fields:"请填写所有必填字段",login_failed_no_token:"登录失败：未获取到 token",login_failed_server_error:"登录失败：服务器响应格式错误",login_failed_check_input:"登录失败，请检查输入",too_many_attempts:"登录尝试次数过多，请稍后再试",network_timeout:"网络连接超时，请检查网络",login_failed_try_later:"登录失败，请稍后重试",email_required:"请输入邮箱",password_required:"请输入密码",verification_code_required:"请输入验证码",invitation_code_required:"请输入邀请码",invalid_email_format:"邮箱格式错误",send_code_failed:"发送验证码失败，请稍后重试",send_code_failed_log:"发送验证码失败:",registration_failed:"注册失败，请检查输入信息",registration_failed_log:"注册失败:",try_reload_or_later:"请尝试重新加载或稍后再试",password_change_failed:"修改密码失败，请稍后重试",password_change_failed_log:"修改密码失败:",password_too_short:"密码长度至少为6位",password_must_contain_letters_numbers:"密码必须包含字母和数字",server_timeout:"服务器超时，请稍后重试"},nav:{market:"行情",points:"积分",settings:"设置"},points:{my_points:"我的积分",total_points:"总积分",ranking:"排名",earn_points:"获取积分",invite_friends:"邀请好友",invite:"邀请",daily_trade:"每日交易",points:"积分",go_trade:"去交易",history:"积分记录",history_all:"全部",history_earned:"获得",history_used:"使用",no_history:"暂无积分记录",invitation_code:"邀请码",invitation_records:"邀请记录",no_invitation_records:"暂无邀请记录",total_invited:"已邀请 {count} 人",invitation_reward:"每邀请一位好友注册成功，可获得 {points} 积分奖励",copy_success:"复制成功",share_invitation_title:"邀请好友加入Cooltrade",share_invitation_text:"我正在使用Cooltrade进行加密货币分析，邀请你一起加入！使用我的邀请码 {code} 注册，我们都可以获得 {points} 积分奖励。",user:"用户",your_invitation_code:"你的邀请码",share:"分享",invitation_reward_desc:"邀请好友奖励",daily_trade_desc:"每日交易奖励",used_for_discount:"使用积分抵扣",registered_at:"注册时间"},tokenNotFound:{title:"{symbol} 数据未找到",description:"该代币尚未在我们的数据库中，点击下方按钮获取最新数据",loading:"正在获取最新市场数据...",refreshButton:"获取最新市场数据",not_supported:"该代币暂不支持，请尝试其他代币"},indicatorExplanations:{RSI:"相对强弱指数（RSI），用于衡量价格动量和超买超卖状态。",BIAS:"乖离率，衡量价格偏离均线的程度。",PSY:"心理线指标，反映市场参与者的心理变化。",VWAP:"成交量加权平均价，反映市场真实交易价值。",FundingRate:"资金费率，反映合约市场多空力量对比。",ExchangeNetflow:"交易所净流入，反映资金流向。",NUPL:"未实现净盈亏比率，反映市场整体盈亏状况。",MayerMultiple:"梅耶倍数，当前价格与200日均线的比值。",MACD:"移动平均线收敛散度，用于判断趋势强弱和转折点。",BollingerBands:"布林带，用于衡量价格波动性和支撑阻力位。",DMI:"动向指标，用于判断趋势方向和强度。"},search:{title:"搜索资产",placeholder:"搜索代码或名称...",searching:"搜索中...",no_results:"未找到结果",popular:"热门资产"},favorites:{add:"添加到收藏",remove:"取消收藏",title:"我的收藏",empty:"暂无收藏",added:"已添加到收藏",removed:"已取消收藏"}},Bu={common:{app_name:"Cooltrade",loading:"Loading...",refresh:"Refresh",refreshing:"Refreshing...",save:"Save",cancel:"Cancel",confirm:"Confirm",back:"Back",success:"Success",error:"Error",warning:"Warning",info:"Info",yes:"Yes",no:"No",ok:"OK",language:"Language",sending:"Sending...",registering:"Registering...",submitting:"Submitting...",retry:"Retry",privacy_policy:"Privacy Policy",about_us:"About Us",no_data:"No data available",load_data:"Load Data",popular_tokens:"Popular Tokens",popular_stocks:"Popular Stocks",quick_switch:"Quick Switch",search:"Search",coming_soon:"Soon",my_favorites:"My Favorites"},market:{crypto:"Crypto",stock:"US Stock",china:"A-Share"},auth:{login:"Login",register:"Register",logout:"Logout",email:"Email",password:"Password",confirm_password:"Confirm Password",verification_code:"Verification Code",send_code:"Send Code",forgot_password:"Forgot Password",reset_password:"Reset Password",change_password:"Change Password",current_password:"Current Password",new_password:"New Password",confirm_new_password:"Confirm Password",invitation_code:"Invitation Code",login_success:"Login Successful",register_success:"Registration Successful",logout_success:"Logout Successful",password_changed:"Password Changed",password_reset:"Password Reset",code_sent:"Verification Code Sent",no_account:"No account?",register_now:"Register Now",have_account:"Have an account?",login_now:"Login Now",email_placeholder:"Enter your email",password_placeholder:"Enter your password",verification_code_placeholder:"Enter verification code",invitation_code_placeholder:"Enter invitation code",retry_in_seconds:"Retry in {seconds}s",password_requirements:"Password must be at least 6 characters and contain both letters and numbers",password_changed_success:"Password changed successfully",login_required_for_password_change:"Please login to change password",go_to_login:"Go to Login",enter_current_and_new_password:"Please enter your current and new password",new_password_placeholder:"Enter new password",confirm_new_password_placeholder:"Confirm new password",resetting:"Resetting...",reset_success:"Password Reset Successful",reset_success_message:"Your password has been reset successfully. Please login with your new password.",back_to_login:"Back to Login"},profile:{profile:"Profile",username:"Username",email:"Email",created_at:"Created At",updated_at:"Updated At",registration_time:"Registration Time",language_preference:"Language Preference",generate_invitation:"Generate Invitation Code",invitation_codes:"Invitation Codes",language_settings:"Language Settings",select_language:"Select Language",chinese:"Chinese",english:"English",japanese:"Japanese",korean:"Korean"},analysis:{technical_analysis:"Technical Analysis",market_data:"Market Data",trend_analysis:"Trend Analysis",indicators:"Indicators",trading_advice:"Trading Advice",risk_assessment:"Risk Assessment",current_price:"Current Price",last_update:"Last Update",refresh_data:"Refresh Data",refreshing:"Refreshing...",up_probability:"Up Probability",sideways_probability:"Sideways Probability",down_probability:"Down Probability",trend_summary:"Trend Summary",action:"Action",reason:"Reason",entry_price:"Entry Price",stop_loss:"Stop Loss",take_profit:"Take Profit",risk_level:"Risk Level",risk_score:"Risk Score",risk_details:"Risk Details",high_risk:"High Risk",medium_risk:"Medium Risk",low_risk:"Low Risk",market_report:"{symbol} Market Analysis",snapshot_price:"Snapshot Price",share_to_twitter:"Share to Twitter",save_image:"Save Image",uptrend:"Uptrend",sideways:"Sideways",downtrend:"Downtrend",market_trend_analysis:"Market Trend Analysis",technical_indicators:"Technical Indicators",recommended_action:"Recommended Action",risk_factors:"Risk Factors",refreshing_data:"Refreshing Data",refreshing_data_ellipsis:"Refreshing data...",calculating_indicators:"Fetching market data and calculating technical indicators...",analyzing_trends:"Analyzing trends and probabilities...",generating_advice:"Generating trading advice and risk assessment...",finalizing_data:"Finalizing data integration...",force_refresh:"Force Refresh",minute_ago:"{n} min ago",hour_ago:"{n} hours ago",day_ago:"{n} days ago",preparing_analysis_report:"Preparing analysis report...",generating_new_report:"Generating new analysis report, please wait...",please_wait:"Please wait, this may take some time",timeout_error:"Request timeout. Server is processing, please try again later.",refresh_report:"Refresh Report",refresh_report_too_soon:"The report cannot be refreshed until 12 hours have passed"},tokenNotFound:{title:"{symbol} Data Not Found",description:"This token is not yet in our database. Click the button below to get the latest data.",refreshButton:"Get Latest Market Data",not_supported:"This token is not supported yet, please try other tokens"},indicators:{rsi:"RSI",macd:"MACD",bollinger_bands:"Bollinger Bands",bias:"BIAS",psy:"PSY",dmi:"DMI",vwap:"VWAP",funding_rate:"Funding Rate",exchange_netflow:"Exchange Netflow",nupl:"NUPL",mayer_multiple:"Mayer Multiple"},errors:{network_error:"Network Error",server_error:"Server Error",not_found:"Not Found",unauthorized:"Unauthorized",forbidden:"Forbidden",validation_error:"Validation Error",unknown_error:"Unknown Error",token_expired:"Login expired, please login again",invalid_credentials:"Invalid email or password",invalid_code:"Invalid or expired verification code",passwords_not_match:"Passwords do not match",email_already_registered:"Email already registered",email_not_registered:"Email not registered",invalid_invitation_code:"Invalid or used invitation code",weak_password:"Password is too weak",fill_all_fields:"Please fill in all required fields",login_failed_no_token:"Login failed: No token received",login_failed_server_error:"Login failed: Server response error",login_failed_check_input:"Login failed, please check your input",too_many_attempts:"Too many login attempts, please try again later",network_timeout:"Network connection timeout, please check your network",login_failed_try_later:"Login failed, please try again later",email_required:"Please enter your email",password_required:"Please enter your password",verification_code_required:"Please enter verification code",invitation_code_required:"Please enter invitation code",invalid_email_format:"Invalid email format",send_code_failed:"Failed to send verification code, please try again later",send_code_failed_log:"Failed to send verification code:",registration_failed:"Registration failed, please check your input",registration_failed_log:"Registration failed:",try_reload_or_later:"Please try reloading or try again later",password_change_failed:"Failed to change password, please try again later",password_change_failed_log:"Failed to change password:",password_too_short:"Password must be at least 6 characters",password_must_contain_letters_numbers:"Password must contain both letters and numbers",server_timeout:"Server timeout, please try again later",not_logged_in:"Not logged in",no_response_from_server:"No response from server",refresh_failed:"Refresh failed, please try again"},nav:{market:"Market",points:"Points",settings:"Settings"},points:{my_points:"My Points",total_points:"Total Points",ranking:"Ranking",earn_points:"Earn Points",invite_friends:"Invite Friends",invite:"Invite",daily_trade:"Daily Trade",points:"Points",go_trade:"Go Trade",history:"Points History",history_all:"All",history_earned:"Earned",history_used:"Used",no_history:"No points history",invitation_code:"Invitation Code",invitation_records:"Invitation Records",no_invitation_records:"No invitation records yet",total_invited:"{count} people invited",invitation_reward:"Earn {points} points for each friend who registers with your code",copy_success:"Copied successfully",share_invitation_title:"Join Cooltrade",share_invitation_text:"I'm using Cooltrade for crypto analysis and I invite you to join! Use my invitation code {code} to register and we'll both get {points} points.",user:"User",your_invitation_code:"Your Invitation Code",share:"Share",invitation_reward_desc:"Invitation Reward",daily_trade_desc:"Daily Trade Reward",used_for_discount:"Used for Discount",registered_at:"Registered at"},indicatorExplanations:{RSI:"Relative Strength Index (RSI), measures price momentum and overbought/oversold conditions.",BIAS:"Bias, measures the deviation of price from the moving average.",PSY:"Psychological Line, reflects market participants' psychological changes.",VWAP:"Volume Weighted Average Price, reflects the true trading value of the market.",FundingRate:"Funding Rate, reflects the balance of long and short forces in the contract market.",ExchangeNetflow:"Exchange Netflow, reflects the direction of capital flow.",NUPL:"Net Unrealized Profit/Loss, reflects the overall profit and loss status of the market.",MayerMultiple:"Mayer Multiple, the ratio of the current price to the 200-day moving average.",MACD:"Moving Average Convergence Divergence, used to judge trend strength and turning points.",BollingerBands:"Bollinger Bands, measures price volatility and support/resistance levels.",DMI:"Directional Movement Index, used to judge trend direction and strength."},search:{title:"Search Assets",placeholder:"Search symbols or names...",searching:"Searching...",no_results:"No results found",popular:"Popular Assets"},favorites:{add:"Add to Favorites",remove:"Remove from Favorites",title:"My Favorites",empty:"No favorites yet",added:"Added to favorites",removed:"Removed from favorites"}},gu={points:{my_points:"マイポイント",total_points:"合計ポイント",ranking:"ランキング",earn_points:"ポイント獲得",invite_friends:"友達招待",invite:"招待",points:"ポイント",history:"ポイント履歴",history_all:"全て",history_earned:"獲得",history_used:"使用",no_history:"ポイント履歴がありません",invitation_reward_desc:"招待報酬",daily_trade_desc:"日次取引報酬",used_for_discount:"ポイント割引に使用",your_invitation_code:"あなたの招待コード",share:"シェア",copy_success:"コピー完了",share_invitation_title:"友達を招待",share_invitation_text:"私の招待コード {code} を使って登録すると、{points} ポイントがもらえます！",daily_trade:"デイリートレード",go_trade:"トレードへ",invitation_code:"招待コード",invitation_records:"招待履歴",no_invitation_records:"招待履歴がありません",total_invited:"{count}人を招待済み",invitation_reward:"友達が招待コードで登録すると{points}ポイントを獲得できます",user:"ユーザー",registered_at:"登録日時"},common:{app_name:"Cooltrade",loading:"読み込み中...",refresh:"更新",refreshing:"更新中...",save:"保存",cancel:"キャンセル",confirm:"確認",back:"戻る",success:"成功",error:"エラー",warning:"警告",info:"情報",yes:"はい",no:"いいえ",ok:"OK",language:"言語",sending:"送信中...",registering:"登録中...",submitting:"送信中...",retry:"再試行",privacy_policy:"プライバシーポリシー",about_us:"会社概要",no_data:"データがありません",load_data:"データを読み込む",popular_tokens:"人気トークン",popular_stocks:"人気株式",quick_switch:"クイック切替",search:"検索",coming_soon:"開発中",my_favorites:"お気に入り"},auth:{login:"ログイン",register:"登録",logout:"ログアウト",email:"メールアドレス",password:"パスワード",confirm_password:"パスワード確認",verification_code:"認証コード",send_code:"コードを送信",forgot_password:"パスワードを忘れた",reset_password:"パスワードをリセット",change_password:"パスワードを変更",current_password:"現在のパスワード",new_password:"新しいパスワード",confirm_new_password:"パスワード確認",invitation_code:"招待コード",login_success:"ログイン成功",register_success:"登録成功",logout_success:"ログアウト成功",password_changed:"パスワード変更完了",password_reset:"パスワードリセット完了",code_sent:"認証コードを送信しました",no_account:"アカウントをお持ちでない方",register_now:"今すぐ登録",have_account:"アカウントをお持ちの方",login_now:"ログイン",email_placeholder:"メールアドレスを入力",password_placeholder:"パスワードを入力",verification_code_placeholder:"認証コードを入力",invitation_code_placeholder:"招待コードを入力",retry_in_seconds:"{seconds}秒後に再試行",password_requirements:"パスワードは6文字以上で、文字と数字を含める必要があります",password_changed_success:"パスワードの変更が完了しました",login_required_for_password_change:"パスワードを変更するにはログインが必要です",go_to_login:"ログインへ",enter_current_and_new_password:"現在のパスワードと新しいパスワードを入力してください",new_password_placeholder:"新しいパスワードを入力",confirm_new_password_placeholder:"新しいパスワードを確認",resetting:"リセット中...",reset_success:"パスワードのリセットが完了しました",reset_success_message:"パスワードのリセットが完了しました。新しいパスワードでログインしてください。",back_to_login:"ログインに戻る"},profile:{profile:"プロフィール",username:"ユーザー名",email:"メールアドレス",created_at:"作成日時",updated_at:"更新日時",registration_time:"登録日時",language_preference:"言語設定",generate_invitation:"招待コード生成",invitation_codes:"招待コード一覧",language_settings:"言語設定",select_language:"言語を選択",chinese:"中国語",english:"英語",japanese:"日本語",korean:"韓国語"},analysis:{technical_analysis:"テクニカル分析",market_data:"市場データ",trend_analysis:"トレンド分析",indicators:"指標",trading_advice:"取引アドバイス",risk_assessment:"リスク評価",current_price:"現在価格",last_update:"最終更新",refresh_data:"データ更新",refreshing:"更新中...",up_probability:"上昇確率",sideways_probability:"横ばい確率",down_probability:"下落確率",trend_summary:"トレンド概要",action:"アクション",reason:"理由",entry_price:"エントリー価格",stop_loss:"ストップロス",take_profit:"利益確定",risk_level:"リスクレベル",risk_score:"リスクスコア",risk_details:"リスク詳細",high_risk:"高リスク",medium_risk:"中リスク",low_risk:"低リスク",market_report:"{symbol}市場分析",snapshot_price:"スナップショット価格",share_to_twitter:"Twitterでシェア",save_image:"画像を保存",uptrend:"上昇",sideways:"横ばい",downtrend:"下落",market_trend_analysis:"市場トレンド分析",technical_indicators:"テクニカル指標",recommended_action:"推奨アクション",risk_factors:"リスク要因",refreshing_data:"データ更新中",refreshing_data_ellipsis:"データ更新中...",calculating_indicators:"市場データ取得とテクニカル指標計算中...",analyzing_trends:"トレンドと確率を分析中...",generating_advice:"取引アドバイスとリスク評価を生成中...",finalizing_data:"データ統合を完了中...",force_refresh:"強制更新",minute_ago:"{n}分前",hour_ago:"{n}時間前",day_ago:"{n}日前",preparing_analysis_report:"分析レポートを準備中...",generating_new_report:"新しい分析レポートを生成中、お待ちください...",please_wait:"お待ちください、時間がかかる場合があります",timeout_error:"リクエストタイムアウト。サーバーが処理中です。後でもう一度お試しください",refresh_report:"レポート更新",refresh_report_too_soon:"レポートは12時間経過するまで更新できません"},indicators:{rsi:"RSI",macd:"MACD",bollinger_bands:"ボリンジャーバンド",bias:"バイアス",psy:"PSY",dmi:"DMI",vwap:"VWAP",funding_rate:"資金調達率",exchange_netflow:"取引所ネットフロー",nupl:"NUPL",mayer_multiple:"メイヤー倍数"},errors:{network_error:"ネットワークエラー",server_error:"サーバーエラー",not_found:"見つかりません",unauthorized:"認証されていません",forbidden:"アクセス禁止",validation_error:"検証エラー",unknown_error:"不明なエラー",token_expired:"ログインの有効期限が切れました。再度ログインしてください",invalid_credentials:"メールアドレスまたはパスワードが無効です",invalid_code:"無効または期限切れの認証コード",passwords_not_match:"パスワードが一致しません",email_already_registered:"このメールアドレスは既に登録されています",email_not_registered:"このメールアドレスは登録されていません",invalid_invitation_code:"無効または使用済みの招待コード",weak_password:"パスワードが弱すぎます",fill_all_fields:"必須項目をすべて入力してください",login_failed_no_token:"ログイン失敗：トークンが受信されませんでした",login_failed_server_error:"ログイン失敗：サーバー応答エラー",login_failed_check_input:"ログイン失敗、入力を確認してください",too_many_attempts:"ログイン試行回数が多すぎます。後でもう一度お試しください",network_timeout:"ネットワーク接続タイムアウト、ネットワークを確認してください",login_failed_try_later:"ログイン失敗、後でもう一度お試しください",email_required:"メールアドレスを入力してください",password_required:"パスワードを入力してください",verification_code_required:"認証コードを入力してください",invitation_code_required:"招待コードを入力してください",invalid_email_format:"無効なメールアドレス形式",send_code_failed:"認証コードの送信に失敗しました。後でもう一度お試しください",send_code_failed_log:"認証コードの送信に失敗しました:",registration_failed:"登録に失敗しました。入力情報を確認してください",registration_failed_log:"登録に失敗しました:",try_reload_or_later:"再読み込みするか、後でもう一度お試しください",password_change_failed:"パスワードの変更に失敗しました。後でもう一度お試しください",password_change_failed_log:"パスワードの変更に失敗しました:",password_too_short:"パスワードは6文字以上である必要があります",password_must_contain_letters_numbers:"パスワードには文字と数字の両方を含める必要があります",server_timeout:"サーバータイムアウト、後でもう一度お試しください"},nav:{market:"マーケット",points:"ポイント",settings:"設定"},tokenNotFound:{title:"{symbol} データが見つかりません",description:"このトークンはまだデータベースにありません。下のボタンをクリックして最新データを取得してください",loading:"最新の市場データを取得中...",refreshButton:"最新の市場データを取得"},indicatorExplanations:{RSI:"相対力指数（RSI）、価格のモメンタムや買われ過ぎ・売られ過ぎ状態を測定します。",BIAS:"乖離率、価格が移動平均線からどれだけ乖離しているかを示します。",PSY:"サイコロジカルライン、投資家心理の変化を反映します。",VWAP:"出来高加重平均価格、市場の実際の取引価値を示します。",FundingRate:"資金調達率、先物市場のロング・ショートの力関係を反映します。",ExchangeNetflow:"取引所純流入、資金の流れを示します。",NUPL:"未実現損益比率、市場全体の損益状況を示します。",MayerMultiple:"メイヤー・マルチプル、現在価格と200日移動平均線の比率です。",MACD:"移動平均収束拡散法（MACD）、トレンドの強さや転換点を判断します。",BollingerBands:"ボリンジャーバンド、価格の変動性やサポート・レジスタンスを測定します。",DMI:"方向性指数（DMI）、トレンドの方向や強さを判断します。"},market:{crypto:"暗号通貨",stock:"米国株",china:"A株"},search:{title:"資産検索",placeholder:"シンボルまたは名前を検索...",searching:"検索中...",no_results:"結果が見つかりません",popular:"人気資産"},favorites:{add:"お気に入りに追加",remove:"お気に入りから削除",title:"マイお気に入り",empty:"お気に入りがありません",added:"お気に入りに追加しました",removed:"お気に入りから削除しました"}},pu={points:{my_points:"내 포인트",total_points:"총 포인트",ranking:"랭킹",earn_points:"포인트 획득",invite_friends:"친구 초대",invite:"초대",points:"포인트",history:"포인트 내역",history_all:"전체",history_earned:"획득",history_used:"사용",no_history:"포인트 내역이 없습니다",invitation_reward_desc:"초대 보상",daily_trade_desc:"일일 거래 보상",used_for_discount:"포인트 할인 사용",your_invitation_code:"내 초대 코드",share:"공유",copy_success:"복사 완료",share_invitation_title:"친구 초대하기",share_invitation_text:"내 초대 코드 {code}로 가입하고 {points} 포인트를 받으세요!",daily_trade:"일일 거래",go_trade:"거래하기",invitation_code:"초대 코드",invitation_records:"초대 내역",no_invitation_records:"초대 내역이 없습니다",total_invited:"{count}명 초대됨",invitation_reward:"친구가 초대 코드로 가입하면 {points}포인트를 획득할 수 있습니다",user:"사용자",registered_at:"가입일"},common:{app_name:"Cooltrade",loading:"로딩 중...",refresh:"새로고침",refreshing:"새로고침 중...",save:"저장",cancel:"취소",confirm:"확인",back:"뒤로",success:"성공",error:"오류",warning:"경고",info:"정보",yes:"예",no:"아니오",ok:"확인",language:"언어",sending:"전송 중...",registering:"등록 중...",submitting:"제출 중...",retry:"다시 시도",privacy_policy:"개인정보 처리방침",about_us:"회사 소개",no_data:"데이터가 없습니다",load_data:"데이터 로드",popular_tokens:"인기 토큰",popular_stocks:"인기 주식",quick_switch:"빠른 전환",search:"검색",coming_soon:"개발 중",my_favorites:"내 즐겨찾기"},auth:{login:"로그인",register:"회원가입",logout:"로그아웃",email:"이메일",password:"비밀번호",confirm_password:"비밀번호 확인",verification_code:"인증 코드",send_code:"코드 전송",forgot_password:"비밀번호 찾기",reset_password:"비밀번호 재설정",change_password:"비밀번호 변경",current_password:"현재 비밀번호",new_password:"새 비밀번호",confirm_new_password:"비밀번호 확인",invitation_code:"초대 코드",login_success:"로그인 성공",register_success:"회원가입 성공",logout_success:"로그아웃 성공",password_changed:"비밀번호가 변경되었습니다",password_reset:"비밀번호가 재설정되었습니다",code_sent:"인증 코드가 전송되었습니다",no_account:"계정이 없으신가요?",register_now:"지금 가입하기",have_account:"계정이 있으신가요?",login_now:"로그인하기",email_placeholder:"이메일을 입력하세요",password_placeholder:"비밀번호를 입력하세요",verification_code_placeholder:"인증 코드를 입력하세요",invitation_code_placeholder:"초대 코드를 입력하세요",retry_in_seconds:"{seconds}초 후 다시 시도",password_requirements:"비밀번호는 6자 이상이며 문자와 숫자를 포함해야 합니다",password_changed_success:"비밀번호가 성공적으로 변경되었습니다",login_required_for_password_change:"비밀번호를 변경하려면 로그인이 필요합니다",go_to_login:"로그인으로 이동",enter_current_and_new_password:"현재 비밀번호와 새 비밀번호를 입력하세요",new_password_placeholder:"새 비밀번호를 입력하세요",confirm_new_password_placeholder:"새 비밀번호를 확인하세요",resetting:"재설정 중...",reset_success:"비밀번호 재설정 성공",reset_success_message:"비밀번호가 성공적으로 재설정되었습니다. 새 비밀번호로 로그인하세요.",back_to_login:"로그인으로 돌아가기"},profile:{profile:"프로필",username:"사용자 이름",email:"이메일",created_at:"생성일",updated_at:"업데이트일",registration_time:"가입 시간",language_preference:"언어 설정",generate_invitation:"초대 코드 생성",invitation_codes:"초대 코드 목록",language_settings:"언어 설정",select_language:"언어 선택",chinese:"중국어",english:"영어",japanese:"일본어",korean:"한국어"},analysis:{technical_analysis:"기술적 분석",market_data:"시장 데이터",trend_analysis:"추세 분석",indicators:"지표",trading_advice:"거래 조언",risk_assessment:"위험 평가",current_price:"현재 가격",last_update:"마지막 업데이트",refresh_data:"데이터 새로고침",refreshing:"새로고침 중...",up_probability:"상승 확률",sideways_probability:"횡보 확률",down_probability:"하락 확률",trend_summary:"추세 요약",action:"행동",reason:"이유",entry_price:"진입 가격",stop_loss:"손절가",take_profit:"이익실현가",risk_level:"위험 수준",risk_score:"위험 점수",risk_details:"위험 세부 정보",high_risk:"높은 위험",medium_risk:"중간 위험",low_risk:"낮은 위험",market_report:"{symbol} 시장 분석",snapshot_price:"스냅샷 가격",share_to_twitter:"트위터에 공유",save_image:"이미지 저장",uptrend:"상승 추세",sideways:"횡보 추세",downtrend:"하락 추세",market_trend_analysis:"시장 추세 분석",technical_indicators:"기술적 지표",recommended_action:"추천 행동",risk_factors:"위험 요소",refreshing_data:"데이터 새로고침 중",refreshing_data_ellipsis:"데이터 새로고침 중...",calculating_indicators:"시장 데이터 및 기술적 지표 계산 중...",analyzing_trends:"추세 및 확률 분석 중...",generating_advice:"거래 조언 및 위험 평가 생성 중...",finalizing_data:"데이터 통합 완료 중...",force_refresh:"강제 새로고침",minute_ago:"{n}분 전",hour_ago:"{n}시간 전",day_ago:"{n}일 전",preparing_analysis_report:"분석 보고서 준비 중...",generating_new_report:"새로운 분석 보고서 생성 중, 잠시만 기다려주세요...",please_wait:"잠시 기다려 주세요, 시간이 걸릴 수 있습니다",timeout_error:"요청 시간 초과. 서버가 처리 중입니다. 나중에 다시 시도해주세요",refresh_report:"보고서 새로고침",refresh_report_too_soon:"보고서는 12시간이 지나야 새로 고침할 수 있습니다"},indicators:{rsi:"RSI",macd:"MACD",bollinger_bands:"볼린저 밴드",bias:"BIAS",psy:"PSY",dmi:"DMI",vwap:"VWAP",funding_rate:"자금 조달 비율",exchange_netflow:"거래소 순유입",nupl:"NUPL",mayer_multiple:"메이어 배수"},errors:{network_error:"네트워크 오류",server_error:"서버 오류",not_found:"찾을 수 없음",unauthorized:"인증되지 않음",forbidden:"접근 금지",validation_error:"유효성 검사 오류",unknown_error:"알 수 없는 오류",token_expired:"로그인이 만료되었습니다. 다시 로그인해주세요",invalid_credentials:"이메일 또는 비밀번호가 잘못되었습니다",invalid_code:"유효하지 않거나 만료된 인증 코드",passwords_not_match:"비밀번호가 일치하지 않습니다",email_already_registered:"이미 등록된 이메일입니다",email_not_registered:"등록되지 않은 이메일입니다",invalid_invitation_code:"유효하지 않거나 사용된 초대 코드",weak_password:"비밀번호가 너무 약합니다",fill_all_fields:"모든 필수 항목을 입력해주세요",login_failed_no_token:"로그인 실패: 토큰을 받지 못했습니다",login_failed_server_error:"로그인 실패: 서버 응답 오류",login_failed_check_input:"로그인 실패, 입력을 확인해주세요",too_many_attempts:"로그인 시도 횟수가 너무 많습니다. 나중에 다시 시도해주세요",network_timeout:"네트워크 연결 시간 초과, 네트워크를 확인해주세요",login_failed_try_later:"로그인 실패, 나중에 다시 시도해주세요",email_required:"이메일을 입력해주세요",password_required:"비밀번호를 입력해주세요",verification_code_required:"인증 코드를 입력해주세요",invitation_code_required:"초대 코드를 입력해주세요",invalid_email_format:"잘못된 이메일 형식",send_code_failed:"인증 코드 전송에 실패했습니다. 나중에 다시 시도해주세요",send_code_failed_log:"인증 코드 전송 실패:",registration_failed:"등록에 실패했습니다. 입력 정보를 확인해주세요",registration_failed_log:"등록 실패:",try_reload_or_later:"다시 로드하거나 나중에 다시 시도해주세요",password_change_failed:"비밀번호 변경에 실패했습니다. 나중에 다시 시도해주세요",password_change_failed_log:"비밀번호 변경 실패:",password_too_short:"비밀번호는 6자 이상이어야 합니다",password_must_contain_letters_numbers:"비밀번호는 문자와 숫자를 모두 포함해야 합니다",server_timeout:"서버 시간 초과, 나중에 다시 시도해주세요"},nav:{market:"마켓",points:"포인트",settings:"설정"},tokenNotFound:{title:"{symbol} 데이터를 찾을 수 없습니다",description:"이 토큰은 아직 데이터베이스에 없습니다. 아래 버튼을 클릭하여 최신 데이터를 가져오세요",loading:"최신 시장 데이터 가져오는 중...",refreshButton:"최신 시장 데이터 가져오기"},indicatorExplanations:{RSI:"상대강도지수(RSI), 가격 모멘텀과 과매수/과매도 상태를 측정합니다.",BIAS:"乖리율, 가격이 이동평균선에서 얼마나 벗어났는지 나타냅니다.",PSY:"심리선 지표, 시장 참여자의 심리 변화를 반영합니다.",VWAP:"거래량 가중 평균가, 시장의 실제 거래 가치를 반영합니다.",FundingRate:"펀딩비, 선물 시장의 롱/숏 세력 균형을 반영합니다.",ExchangeNetflow:"거래소 순유입, 자금 흐름 방향을 나타냅니다.",NUPL:"미실현 손익비율, 시장 전체의 손익 상태를 반영합니다.",MayerMultiple:"마이어 배수, 현재 가격과 200일 이동평균선의 비율입니다.",MACD:"이동평균 수렴확산(MACD), 추세의 강도와 전환점을 판단합니다.",BollingerBands:"볼린저 밴드, 가격 변동성과 지지/저항 구간을 측정합니다.",DMI:"방향성 지표(DMI), 추세 방향과 강도를 판단합니다."},market:{crypto:"암호화폐",stock:"미국 주식",china:"A주"},search:{title:"자산 검색",placeholder:"심볼 또는 이름 검색...",searching:"검색 중...",no_results:"결과를 찾을 수 없습니다",popular:"인기 자산"},favorites:{add:"즐겨찾기에 추가",remove:"즐겨찾기에서 제거",title:"내 즐겨찾기",empty:"즐겨찾기가 없습니다",added:"즐겨찾기에 추가되었습니다",removed:"즐겨찾기에서 제거되었습니다"}},fu={"zh-CN":du,"en-US":Bu,"ja-JP":gu,"ko-KR":pu};let wu="en-US";function hu(A,e){const t=localStorage.getItem("language");t&&["zh-CN","en-US","ja-JP","ko-KR"].includes(t)&&t!==wu&&(wu=t);const r=A.split(".");let n=fu[wu];for(const s of r){if(null==n)return A;n=n[s]}return null==n?A:"string"==typeof n&&e?Object.entries(e).reduce(((A,[e,t])=>A.replace(new RegExp(`{${e}}`,"g"),String(t))),n):String(n)}function mu(A){["zh-CN","en-US","ja-JP","ko-KR"].includes(A)&&(wu=A,localStorage.setItem("language",A),window.dispatchEvent(new CustomEvent("locale-changed",{detail:{locale:A}})))}function Qu(){return wu}function yu(){return Object.keys(fu)}!function(){const A=localStorage.getItem("language");A&&["zh-CN","en-US","ja-JP","ko-KR"].includes(A)?wu=A:(wu="en-US",localStorage.setItem("language","en-US"))}();const Cu={install(A){A.config.globalProperties.$td=hu,A.config.globalProperties.$locale={get:Qu,set:mu,available:yu()},A.directive("t",{mounted(A,e){A.textContent=hu(e.value)},updated(A,e){A.textContent=hu(e.value)}})}},Uu={t:hu,setLocale:mu,getLocale:Qu,getAvailableLocales:yu,plugin:Cu},vu=Object.freeze(Object.defineProperty({__proto__:null,default:Uu,getAvailableLocales:yu,getLocale:Qu,i18nDirectPlugin:Cu,setLocale:mu,t:hu},Symbol.toStringTag,{value:"Module"})),Fu=h((()=>window.location.protocol.includes("extension")||window.location.protocol.includes("chrome")||window.location.protocol.includes("moz"))),bu=()=>{const{t:A,locale:e}=iu(),t=()=>{const A=localStorage.getItem("language");A&&["zh-CN","en-US","ja-JP","ko-KR"].includes(A)&&A!==e.value&&(e.value=A)};return t(),window.addEventListener("language-changed",(A=>{var t;const r=(null==(t=A.detail)?void 0:t.language)||localStorage.getItem("language")||"en-US";e.value=r})),window.addEventListener("force-refresh-i18n",(()=>{t()})),{t:(e,r)=>{if(t(),Fu.value){const t=hu(e,r);if(t===e){const n=A(e,r||{});return n===e?t:n}return t}return A(e,r||{})},locale:e}};function _u(A,e){return function(){return A.apply(e,arguments)}}const{toString:xu}=Object.prototype,{getPrototypeOf:Eu}=Object,{iterator:Hu,toStringTag:Iu}=Symbol,ku=(A=>e=>{const t=xu.call(e);return A[t]||(A[t]=t.slice(8,-1).toLowerCase())})(Object.create(null)),Lu=A=>(A=A.toLowerCase(),e=>ku(e)===A),Su=A=>e=>typeof e===A,{isArray:Ku}=Array,Du=Su("undefined"),Tu=Lu("ArrayBuffer"),Ou=Su("string"),Mu=Su("function"),Ru=Su("number"),Pu=A=>null!==A&&"object"==typeof A,Nu=A=>{if("object"!==ku(A))return!1;const e=Eu(A);return!(null!==e&&e!==Object.prototype&&null!==Object.getPrototypeOf(e)||Iu in A||Hu in A)},Vu=Lu("Date"),Gu=Lu("File"),Ju=Lu("Blob"),Xu=Lu("FileList"),ju=Lu("URLSearchParams"),[Wu,Yu,Zu,zu]=["ReadableStream","Request","Response","Headers"].map(Lu);function qu(A,e,{allOwnKeys:t=!1}={}){if(null==A)return;let r,n;if("object"!=typeof A&&(A=[A]),Ku(A))for(r=0,n=A.length;r<n;r++)e.call(null,A[r],r,A);else{const n=t?Object.getOwnPropertyNames(A):Object.keys(A),s=n.length;let o;for(r=0;r<s;r++)o=n[r],e.call(null,A[o],o,A)}}function $u(A,e){e=e.toLowerCase();const t=Object.keys(A);let r,n=t.length;for(;n-- >0;)if(r=t[n],e===r.toLowerCase())return r;return null}const Ad="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,ed=A=>!Du(A)&&A!==Ad,td=(A=>e=>A&&e instanceof A)("undefined"!=typeof Uint8Array&&Eu(Uint8Array)),rd=Lu("HTMLFormElement"),nd=(({hasOwnProperty:A})=>(e,t)=>A.call(e,t))(Object.prototype),sd=Lu("RegExp"),od=(A,e)=>{const t=Object.getOwnPropertyDescriptors(A),r={};qu(t,((t,n)=>{let s;!1!==(s=e(t,n,A))&&(r[n]=s||t)})),Object.defineProperties(A,r)},ad=Lu("AsyncFunction"),id=(ld="function"==typeof setImmediate,cd=Mu(Ad.postMessage),ld?setImmediate:cd?(ud=`axios@${Math.random()}`,dd=[],Ad.addEventListener("message",(({source:A,data:e})=>{A===Ad&&e===ud&&dd.length&&dd.shift()()}),!1),A=>{dd.push(A),Ad.postMessage(ud,"*")}):A=>setTimeout(A));var ld,cd,ud,dd;const Bd="undefined"!=typeof queueMicrotask?queueMicrotask.bind(Ad):"undefined"!=typeof process&&process.nextTick||id,gd={isArray:Ku,isArrayBuffer:Tu,isBuffer:function(A){return null!==A&&!Du(A)&&null!==A.constructor&&!Du(A.constructor)&&Mu(A.constructor.isBuffer)&&A.constructor.isBuffer(A)},isFormData:A=>{let e;return A&&("function"==typeof FormData&&A instanceof FormData||Mu(A.append)&&("formdata"===(e=ku(A))||"object"===e&&Mu(A.toString)&&"[object FormData]"===A.toString()))},isArrayBufferView:function(A){let e;return e="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(A):A&&A.buffer&&Tu(A.buffer),e},isString:Ou,isNumber:Ru,isBoolean:A=>!0===A||!1===A,isObject:Pu,isPlainObject:Nu,isReadableStream:Wu,isRequest:Yu,isResponse:Zu,isHeaders:zu,isUndefined:Du,isDate:Vu,isFile:Gu,isBlob:Ju,isRegExp:sd,isFunction:Mu,isStream:A=>Pu(A)&&Mu(A.pipe),isURLSearchParams:ju,isTypedArray:td,isFileList:Xu,forEach:qu,merge:function A(){const{caseless:e}=ed(this)&&this||{},t={},r=(r,n)=>{const s=e&&$u(t,n)||n;Nu(t[s])&&Nu(r)?t[s]=A(t[s],r):Nu(r)?t[s]=A({},r):Ku(r)?t[s]=r.slice():t[s]=r};for(let n=0,s=arguments.length;n<s;n++)arguments[n]&&qu(arguments[n],r);return t},extend:(A,e,t,{allOwnKeys:r}={})=>(qu(e,((e,r)=>{t&&Mu(e)?A[r]=_u(e,t):A[r]=e}),{allOwnKeys:r}),A),trim:A=>A.trim?A.trim():A.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:A=>(65279===A.charCodeAt(0)&&(A=A.slice(1)),A),inherits:(A,e,t,r)=>{A.prototype=Object.create(e.prototype,r),A.prototype.constructor=A,Object.defineProperty(A,"super",{value:e.prototype}),t&&Object.assign(A.prototype,t)},toFlatObject:(A,e,t,r)=>{let n,s,o;const a={};if(e=e||{},null==A)return e;do{for(n=Object.getOwnPropertyNames(A),s=n.length;s-- >0;)o=n[s],r&&!r(o,A,e)||a[o]||(e[o]=A[o],a[o]=!0);A=!1!==t&&Eu(A)}while(A&&(!t||t(A,e))&&A!==Object.prototype);return e},kindOf:ku,kindOfTest:Lu,endsWith:(A,e,t)=>{A=String(A),(void 0===t||t>A.length)&&(t=A.length),t-=e.length;const r=A.indexOf(e,t);return-1!==r&&r===t},toArray:A=>{if(!A)return null;if(Ku(A))return A;let e=A.length;if(!Ru(e))return null;const t=new Array(e);for(;e-- >0;)t[e]=A[e];return t},forEachEntry:(A,e)=>{const t=(A&&A[Hu]).call(A);let r;for(;(r=t.next())&&!r.done;){const t=r.value;e.call(A,t[0],t[1])}},matchAll:(A,e)=>{let t;const r=[];for(;null!==(t=A.exec(e));)r.push(t);return r},isHTMLForm:rd,hasOwnProperty:nd,hasOwnProp:nd,reduceDescriptors:od,freezeMethods:A=>{od(A,((e,t)=>{if(Mu(A)&&-1!==["arguments","caller","callee"].indexOf(t))return!1;const r=A[t];Mu(r)&&(e.enumerable=!1,"writable"in e?e.writable=!1:e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+t+"'")}))}))},toObjectSet:(A,e)=>{const t={},r=A=>{A.forEach((A=>{t[A]=!0}))};return Ku(A)?r(A):r(String(A).split(e)),t},toCamelCase:A=>A.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(A,e,t){return e.toUpperCase()+t})),noop:()=>{},toFiniteNumber:(A,e)=>null!=A&&Number.isFinite(A=+A)?A:e,findKey:$u,global:Ad,isContextDefined:ed,isSpecCompliantForm:function(A){return!!(A&&Mu(A.append)&&"FormData"===A[Iu]&&A[Hu])},toJSONObject:A=>{const e=new Array(10),t=(A,r)=>{if(Pu(A)){if(e.indexOf(A)>=0)return;if(!("toJSON"in A)){e[r]=A;const n=Ku(A)?[]:{};return qu(A,((A,e)=>{const s=t(A,r+1);!Du(s)&&(n[e]=s)})),e[r]=void 0,n}}return A};return t(A,0)},isAsyncFn:ad,isThenable:A=>A&&(Pu(A)||Mu(A))&&Mu(A.then)&&Mu(A.catch),setImmediate:id,asap:Bd,isIterable:A=>null!=A&&Mu(A[Hu])};function pd(A,e,t,r,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=A,this.name="AxiosError",e&&(this.code=e),t&&(this.config=t),r&&(this.request=r),n&&(this.response=n,this.status=n.status?n.status:null)}gd.inherits(pd,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:gd.toJSONObject(this.config),code:this.code,status:this.status}}});const fd=pd.prototype,wd={};function hd(A){return gd.isPlainObject(A)||gd.isArray(A)}function md(A){return gd.endsWith(A,"[]")?A.slice(0,-2):A}function Qd(A,e,t){return A?A.concat(e).map((function(A,e){return A=md(A),!t&&e?"["+A+"]":A})).join(t?".":""):e}["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((A=>{wd[A]={value:A}})),Object.defineProperties(pd,wd),Object.defineProperty(fd,"isAxiosError",{value:!0}),pd.from=(A,e,t,r,n,s)=>{const o=Object.create(fd);return gd.toFlatObject(A,o,(function(A){return A!==Error.prototype}),(A=>"isAxiosError"!==A)),pd.call(o,A.message,e,t,r,n),o.cause=A,o.name=A.name,s&&Object.assign(o,s),o};const yd=gd.toFlatObject(gd,{},null,(function(A){return/^is[A-Z]/.test(A)}));function Cd(A,e,t){if(!gd.isObject(A))throw new TypeError("target must be an object");e=e||new FormData;const r=(t=gd.toFlatObject(t,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(A,e){return!gd.isUndefined(e[A])}))).metaTokens,n=t.visitor||l,s=t.dots,o=t.indexes,a=(t.Blob||"undefined"!=typeof Blob&&Blob)&&gd.isSpecCompliantForm(e);if(!gd.isFunction(n))throw new TypeError("visitor must be a function");function i(A){if(null===A)return"";if(gd.isDate(A))return A.toISOString();if(!a&&gd.isBlob(A))throw new pd("Blob is not supported. Use a Buffer instead.");return gd.isArrayBuffer(A)||gd.isTypedArray(A)?a&&"function"==typeof Blob?new Blob([A]):Buffer.from(A):A}function l(A,t,n){let a=A;if(A&&!n&&"object"==typeof A)if(gd.endsWith(t,"{}"))t=r?t:t.slice(0,-2),A=JSON.stringify(A);else if(gd.isArray(A)&&function(A){return gd.isArray(A)&&!A.some(hd)}(A)||(gd.isFileList(A)||gd.endsWith(t,"[]"))&&(a=gd.toArray(A)))return t=md(t),a.forEach((function(A,r){!gd.isUndefined(A)&&null!==A&&e.append(!0===o?Qd([t],r,s):null===o?t:t+"[]",i(A))})),!1;return!!hd(A)||(e.append(Qd(n,t,s),i(A)),!1)}const c=[],u=Object.assign(yd,{defaultVisitor:l,convertValue:i,isVisitable:hd});if(!gd.isObject(A))throw new TypeError("data must be an object");return function A(t,r){if(!gd.isUndefined(t)){if(-1!==c.indexOf(t))throw Error("Circular reference detected in "+r.join("."));c.push(t),gd.forEach(t,(function(t,s){!0===(!(gd.isUndefined(t)||null===t)&&n.call(e,t,gd.isString(s)?s.trim():s,r,u))&&A(t,r?r.concat(s):[s])})),c.pop()}}(A),e}function Ud(A){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(A).replace(/[!'()~]|%20|%00/g,(function(A){return e[A]}))}function vd(A,e){this._pairs=[],A&&Cd(A,this,e)}const Fd=vd.prototype;function bd(A){return encodeURIComponent(A).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function _d(A,e,t){if(!e)return A;const r=t&&t.encode||bd;gd.isFunction(t)&&(t={serialize:t});const n=t&&t.serialize;let s;if(s=n?n(e,t):gd.isURLSearchParams(e)?e.toString():new vd(e,t).toString(r),s){const e=A.indexOf("#");-1!==e&&(A=A.slice(0,e)),A+=(-1===A.indexOf("?")?"?":"&")+s}return A}Fd.append=function(A,e){this._pairs.push([A,e])},Fd.toString=function(A){const e=A?function(e){return A.call(this,e,Ud)}:Ud;return this._pairs.map((function(A){return e(A[0])+"="+e(A[1])}),"").join("&")};class xd{constructor(){this.handlers=[]}use(A,e,t){return this.handlers.push({fulfilled:A,rejected:e,synchronous:!!t&&t.synchronous,runWhen:t?t.runWhen:null}),this.handlers.length-1}eject(A){this.handlers[A]&&(this.handlers[A]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(A){gd.forEach(this.handlers,(function(e){null!==e&&A(e)}))}}const Ed={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Hd={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:vd,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Id="undefined"!=typeof window&&"undefined"!=typeof document,kd="object"==typeof navigator&&navigator||void 0,Ld=Id&&(!kd||["ReactNative","NativeScript","NS"].indexOf(kd.product)<0),Sd="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,Kd=Id&&window.location.href||"http://localhost",Dd=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Id,hasStandardBrowserEnv:Ld,hasStandardBrowserWebWorkerEnv:Sd,navigator:kd,origin:Kd},Symbol.toStringTag,{value:"Module"})),Td=l(l({},Dd),Hd);function Od(A){function e(A,t,r,n){let s=A[n++];if("__proto__"===s)return!0;const o=Number.isFinite(+s),a=n>=A.length;return s=!s&&gd.isArray(r)?r.length:s,a?(gd.hasOwnProp(r,s)?r[s]=[r[s],t]:r[s]=t,!o):(r[s]&&gd.isObject(r[s])||(r[s]=[]),e(A,t,r[s],n)&&gd.isArray(r[s])&&(r[s]=function(A){const e={},t=Object.keys(A);let r;const n=t.length;let s;for(r=0;r<n;r++)s=t[r],e[s]=A[s];return e}(r[s])),!o)}if(gd.isFormData(A)&&gd.isFunction(A.entries)){const t={};return gd.forEachEntry(A,((A,r)=>{e(function(A){return gd.matchAll(/\w+|\[(\w*)]/g,A).map((A=>"[]"===A[0]?"":A[1]||A[0]))}(A),r,t,0)})),t}return null}const Md={transitional:Ed,adapter:["xhr","http","fetch"],transformRequest:[function(A,e){const t=e.getContentType()||"",r=t.indexOf("application/json")>-1,n=gd.isObject(A);if(n&&gd.isHTMLForm(A)&&(A=new FormData(A)),gd.isFormData(A))return r?JSON.stringify(Od(A)):A;if(gd.isArrayBuffer(A)||gd.isBuffer(A)||gd.isStream(A)||gd.isFile(A)||gd.isBlob(A)||gd.isReadableStream(A))return A;if(gd.isArrayBufferView(A))return A.buffer;if(gd.isURLSearchParams(A))return e.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),A.toString();let s;if(n){if(t.indexOf("application/x-www-form-urlencoded")>-1)return function(A,e){return Cd(A,new Td.classes.URLSearchParams,Object.assign({visitor:function(A,e,t,r){return Td.isNode&&gd.isBuffer(A)?(this.append(e,A.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},e))}(A,this.formSerializer).toString();if((s=gd.isFileList(A))||t.indexOf("multipart/form-data")>-1){const e=this.env&&this.env.FormData;return Cd(s?{"files[]":A}:A,e&&new e,this.formSerializer)}}return n||r?(e.setContentType("application/json",!1),function(A,e,t){if(gd.isString(A))try{return(e||JSON.parse)(A),gd.trim(A)}catch(r){if("SyntaxError"!==r.name)throw r}return(t||JSON.stringify)(A)}(A)):A}],transformResponse:[function(A){const e=this.transitional||Md.transitional,t=e&&e.forcedJSONParsing,r="json"===this.responseType;if(gd.isResponse(A)||gd.isReadableStream(A))return A;if(A&&gd.isString(A)&&(t&&!this.responseType||r)){const t=!(e&&e.silentJSONParsing)&&r;try{return JSON.parse(A)}catch(n){if(t){if("SyntaxError"===n.name)throw pd.from(n,pd.ERR_BAD_RESPONSE,this,null,this.response);throw n}}}return A}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Td.classes.FormData,Blob:Td.classes.Blob},validateStatus:function(A){return A>=200&&A<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};gd.forEach(["delete","get","head","post","put","patch"],(A=>{Md.headers[A]={}}));const Rd=gd.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Pd=Symbol("internals");function Nd(A){return A&&String(A).trim().toLowerCase()}function Vd(A){return!1===A||null==A?A:gd.isArray(A)?A.map(Vd):String(A)}function Gd(A,e,t,r,n){return gd.isFunction(r)?r.call(this,e,t):(n&&(e=t),gd.isString(e)?gd.isString(r)?-1!==e.indexOf(r):gd.isRegExp(r)?r.test(e):void 0:void 0)}let Jd=class{constructor(A){A&&this.set(A)}set(A,e,t){const r=this;function n(A,e,t){const n=Nd(e);if(!n)throw new Error("header name must be a non-empty string");const s=gd.findKey(r,n);(!s||void 0===r[s]||!0===t||void 0===t&&!1!==r[s])&&(r[s||e]=Vd(A))}const s=(A,e)=>gd.forEach(A,((A,t)=>n(A,t,e)));if(gd.isPlainObject(A)||A instanceof this.constructor)s(A,e);else if(gd.isString(A)&&(A=A.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(A.trim()))s((A=>{const e={};let t,r,n;return A&&A.split("\n").forEach((function(A){n=A.indexOf(":"),t=A.substring(0,n).trim().toLowerCase(),r=A.substring(n+1).trim(),!t||e[t]&&Rd[t]||("set-cookie"===t?e[t]?e[t].push(r):e[t]=[r]:e[t]=e[t]?e[t]+", "+r:r)})),e})(A),e);else if(gd.isObject(A)&&gd.isIterable(A)){let t,r,n={};for(const e of A){if(!gd.isArray(e))throw TypeError("Object iterator must return a key-value pair");n[r=e[0]]=(t=n[r])?gd.isArray(t)?[...t,e[1]]:[t,e[1]]:e[1]}s(n,e)}else null!=A&&n(e,A,t);return this}get(A,e){if(A=Nd(A)){const t=gd.findKey(this,A);if(t){const A=this[t];if(!e)return A;if(!0===e)return function(A){const e=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=t.exec(A);)e[r[1]]=r[2];return e}(A);if(gd.isFunction(e))return e.call(this,A,t);if(gd.isRegExp(e))return e.exec(A);throw new TypeError("parser must be boolean|regexp|function")}}}has(A,e){if(A=Nd(A)){const t=gd.findKey(this,A);return!(!t||void 0===this[t]||e&&!Gd(0,this[t],t,e))}return!1}delete(A,e){const t=this;let r=!1;function n(A){if(A=Nd(A)){const n=gd.findKey(t,A);!n||e&&!Gd(0,t[n],n,e)||(delete t[n],r=!0)}}return gd.isArray(A)?A.forEach(n):n(A),r}clear(A){const e=Object.keys(this);let t=e.length,r=!1;for(;t--;){const n=e[t];A&&!Gd(0,this[n],n,A,!0)||(delete this[n],r=!0)}return r}normalize(A){const e=this,t={};return gd.forEach(this,((r,n)=>{const s=gd.findKey(t,n);if(s)return e[s]=Vd(r),void delete e[n];const o=A?function(A){return A.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((A,e,t)=>e.toUpperCase()+t))}(n):String(n).trim();o!==n&&delete e[n],e[o]=Vd(r),t[o]=!0})),this}concat(...A){return this.constructor.concat(this,...A)}toJSON(A){const e=Object.create(null);return gd.forEach(this,((t,r)=>{null!=t&&!1!==t&&(e[r]=A&&gd.isArray(t)?t.join(", "):t)})),e}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([A,e])=>A+": "+e)).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(A){return A instanceof this?A:new this(A)}static concat(A,...e){const t=new this(A);return e.forEach((A=>t.set(A))),t}static accessor(A){const e=(this[Pd]=this[Pd]={accessors:{}}).accessors,t=this.prototype;function r(A){const r=Nd(A);e[r]||(function(A,e){const t=gd.toCamelCase(" "+e);["get","set","has"].forEach((r=>{Object.defineProperty(A,r+t,{value:function(A,t,n){return this[r].call(this,e,A,t,n)},configurable:!0})}))}(t,A),e[r]=!0)}return gd.isArray(A)?A.forEach(r):r(A),this}};function Xd(A,e){const t=this||Md,r=e||t,n=Jd.from(r.headers);let s=r.data;return gd.forEach(A,(function(A){s=A.call(t,s,n.normalize(),e?e.status:void 0)})),n.normalize(),s}function jd(A){return!(!A||!A.__CANCEL__)}function Wd(A,e,t){pd.call(this,null==A?"canceled":A,pd.ERR_CANCELED,e,t),this.name="CanceledError"}function Yd(A,e,t){const r=t.config.validateStatus;t.status&&r&&!r(t.status)?e(new pd("Request failed with status code "+t.status,[pd.ERR_BAD_REQUEST,pd.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t)):A(t)}Jd.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),gd.reduceDescriptors(Jd.prototype,(({value:A},e)=>{let t=e[0].toUpperCase()+e.slice(1);return{get:()=>A,set(A){this[t]=A}}})),gd.freezeMethods(Jd),gd.inherits(Wd,pd,{__CANCEL__:!0});const Zd=(A,e,t=3)=>{let r=0;const n=function(A,e){A=A||10;const t=new Array(A),r=new Array(A);let n,s=0,o=0;return e=void 0!==e?e:1e3,function(a){const i=Date.now(),l=r[o];n||(n=i),t[s]=a,r[s]=i;let c=o,u=0;for(;c!==s;)u+=t[c++],c%=A;if(s=(s+1)%A,s===o&&(o=(o+1)%A),i-n<e)return;const d=l&&i-l;return d?Math.round(1e3*u/d):void 0}}(50,250);return function(A,e){let t,r,n=0,s=1e3/e;const o=(e,s=Date.now())=>{n=s,t=null,r&&(clearTimeout(r),r=null),A.apply(null,e)};return[(...A)=>{const e=Date.now(),a=e-n;a>=s?o(A,e):(t=A,r||(r=setTimeout((()=>{r=null,o(t)}),s-a)))},()=>t&&o(t)]}((t=>{const s=t.loaded,o=t.lengthComputable?t.total:void 0,a=s-r,i=n(a);r=s,A({loaded:s,total:o,progress:o?s/o:void 0,bytes:a,rate:i||void 0,estimated:i&&o&&s<=o?(o-s)/i:void 0,event:t,lengthComputable:null!=o,[e?"download":"upload"]:!0})}),t)},zd=(A,e)=>{const t=null!=A;return[r=>e[0]({lengthComputable:t,total:A,loaded:r}),e[1]]},qd=A=>(...e)=>gd.asap((()=>A(...e))),$d=Td.hasStandardBrowserEnv?((A,e)=>t=>(t=new URL(t,Td.origin),A.protocol===t.protocol&&A.host===t.host&&(e||A.port===t.port)))(new URL(Td.origin),Td.navigator&&/(msie|trident)/i.test(Td.navigator.userAgent)):()=>!0,AB=Td.hasStandardBrowserEnv?{write(A,e,t,r,n,s){const o=[A+"="+encodeURIComponent(e)];gd.isNumber(t)&&o.push("expires="+new Date(t).toGMTString()),gd.isString(r)&&o.push("path="+r),gd.isString(n)&&o.push("domain="+n),!0===s&&o.push("secure"),document.cookie=o.join("; ")},read(A){const e=document.cookie.match(new RegExp("(^|;\\s*)("+A+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(A){this.write(A,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function eB(A,e,t){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e);return A&&(r||0==t)?function(A,e){return e?A.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):A}(A,e):e}const tB=A=>A instanceof Jd?l({},A):A;function rB(A,e){e=e||{};const t={};function r(A,e,t,r){return gd.isPlainObject(A)&&gd.isPlainObject(e)?gd.merge.call({caseless:r},A,e):gd.isPlainObject(e)?gd.merge({},e):gd.isArray(e)?e.slice():e}function n(A,e,t,n){return gd.isUndefined(e)?gd.isUndefined(A)?void 0:r(void 0,A,0,n):r(A,e,0,n)}function s(A,e){if(!gd.isUndefined(e))return r(void 0,e)}function o(A,e){return gd.isUndefined(e)?gd.isUndefined(A)?void 0:r(void 0,A):r(void 0,e)}function a(t,n,s){return s in e?r(t,n):s in A?r(void 0,t):void 0}const i={url:s,method:s,data:s,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:a,headers:(A,e,t)=>n(tB(A),tB(e),0,!0)};return gd.forEach(Object.keys(Object.assign({},A,e)),(function(r){const s=i[r]||n,o=s(A[r],e[r],r);gd.isUndefined(o)&&s!==a||(t[r]=o)})),t}const nB=A=>{const e=rB({},A);let t,{data:r,withXSRFToken:n,xsrfHeaderName:s,xsrfCookieName:o,headers:a,auth:i}=e;if(e.headers=a=Jd.from(a),e.url=_d(eB(e.baseURL,e.url,e.allowAbsoluteUrls),A.params,A.paramsSerializer),i&&a.set("Authorization","Basic "+btoa((i.username||"")+":"+(i.password?unescape(encodeURIComponent(i.password)):""))),gd.isFormData(r))if(Td.hasStandardBrowserEnv||Td.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(t=a.getContentType())){const[A,...e]=t?t.split(";").map((A=>A.trim())).filter(Boolean):[];a.setContentType([A||"multipart/form-data",...e].join("; "))}if(Td.hasStandardBrowserEnv&&(n&&gd.isFunction(n)&&(n=n(e)),n||!1!==n&&$d(e.url))){const A=s&&o&&AB.read(o);A&&a.set(s,A)}return e},sB="undefined"!=typeof XMLHttpRequest&&function(A){return new Promise((function(e,t){const r=nB(A);let n=r.data;const s=Jd.from(r.headers).normalize();let o,a,i,l,c,{responseType:u,onUploadProgress:d,onDownloadProgress:B}=r;function g(){l&&l(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(o),r.signal&&r.signal.removeEventListener("abort",o)}let p=new XMLHttpRequest;function f(){if(!p)return;const r=Jd.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders());Yd((function(A){e(A),g()}),(function(A){t(A),g()}),{data:u&&"text"!==u&&"json"!==u?p.response:p.responseText,status:p.status,statusText:p.statusText,headers:r,config:A,request:p}),p=null}p.open(r.method.toUpperCase(),r.url,!0),p.timeout=r.timeout,"onloadend"in p?p.onloadend=f:p.onreadystatechange=function(){p&&4===p.readyState&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))&&setTimeout(f)},p.onabort=function(){p&&(t(new pd("Request aborted",pd.ECONNABORTED,A,p)),p=null)},p.onerror=function(){t(new pd("Network Error",pd.ERR_NETWORK,A,p)),p=null},p.ontimeout=function(){let e=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const n=r.transitional||Ed;r.timeoutErrorMessage&&(e=r.timeoutErrorMessage),t(new pd(e,n.clarifyTimeoutError?pd.ETIMEDOUT:pd.ECONNABORTED,A,p)),p=null},void 0===n&&s.setContentType(null),"setRequestHeader"in p&&gd.forEach(s.toJSON(),(function(A,e){p.setRequestHeader(e,A)})),gd.isUndefined(r.withCredentials)||(p.withCredentials=!!r.withCredentials),u&&"json"!==u&&(p.responseType=r.responseType),B&&([i,c]=Zd(B,!0),p.addEventListener("progress",i)),d&&p.upload&&([a,l]=Zd(d),p.upload.addEventListener("progress",a),p.upload.addEventListener("loadend",l)),(r.cancelToken||r.signal)&&(o=e=>{p&&(t(!e||e.type?new Wd(null,A,p):e),p.abort(),p=null)},r.cancelToken&&r.cancelToken.subscribe(o),r.signal&&(r.signal.aborted?o():r.signal.addEventListener("abort",o)));const w=function(A){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(A);return e&&e[1]||""}(r.url);w&&-1===Td.protocols.indexOf(w)?t(new pd("Unsupported protocol "+w+":",pd.ERR_BAD_REQUEST,A)):p.send(n||null)}))},oB=(A,e)=>{const{length:t}=A=A?A.filter(Boolean):[];if(e||t){let t,r=new AbortController;const n=function(A){if(!t){t=!0,o();const e=A instanceof Error?A:this.reason;r.abort(e instanceof pd?e:new Wd(e instanceof Error?e.message:e))}};let s=e&&setTimeout((()=>{s=null,n(new pd(`timeout ${e} of ms exceeded`,pd.ETIMEDOUT))}),e);const o=()=>{A&&(s&&clearTimeout(s),s=null,A.forEach((A=>{A.unsubscribe?A.unsubscribe(n):A.removeEventListener("abort",n)})),A=null)};A.forEach((A=>A.addEventListener("abort",n)));const{signal:a}=r;return a.unsubscribe=()=>gd.asap(o),a}},aB=function*(A,e){let t=A.byteLength;if(t<e)return void(yield A);let r,n=0;for(;n<t;)r=n+e,yield A.slice(n,r),n=r},iB=function(A,e){return B(this,null,(function*(){try{for(var t,r,n,s=((A,e,t)=>(e=A[a("asyncIterator")])?e.call(A):(A=A[a("iterator")](),e={},(t=(t,r)=>(r=A[t])&&(e[t]=e=>new Promise(((t,n,s)=>(e=r.call(A,e),s=e.done,Promise.resolve(e.value).then((A=>t({value:A,done:s})),n))))))("next"),t("return"),e))(lB(A));t=!(r=yield new d(s.next())).done;t=!1){const A=r.value;yield*g(aB(A,e))}}catch(r){n=[r]}finally{try{t&&(r=s.return)&&(yield new d(r.call(s)))}finally{if(n)throw n[0]}}}))},lB=function(A){return B(this,null,(function*(){if(A[Symbol.asyncIterator])return void(yield*g(A));const e=A.getReader();try{for(;;){const{done:A,value:t}=yield new d(e.read());if(A)break;yield t}}finally{yield new d(e.cancel())}}))},cB=(A,e,t,r)=>{const n=iB(A,e);let s,o=0,a=A=>{s||(s=!0,r&&r(A))};return new ReadableStream({pull(A){return u(this,null,(function*(){try{const{done:e,value:r}=yield n.next();if(e)return a(),void A.close();let s=r.byteLength;if(t){let A=o+=s;t(A)}A.enqueue(new Uint8Array(r))}catch(e){throw a(e),e}}))},cancel:A=>(a(A),n.return())},{highWaterMark:2})},uB="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,dB=uB&&"function"==typeof ReadableStream,BB=uB&&("function"==typeof TextEncoder?(A=>e=>A.encode(e))(new TextEncoder):e=>u(A,null,(function*(){return new Uint8Array(yield new Response(e).arrayBuffer())}))),gB=(A,...e)=>{try{return!!A(...e)}catch(t){return!1}},pB=dB&&gB((()=>{let A=!1;const e=new Request(Td.origin,{body:new ReadableStream,method:"POST",get duplex(){return A=!0,"half"}}).headers.has("Content-Type");return A&&!e})),fB=dB&&gB((()=>gd.isReadableStream(new Response("").body))),wB={stream:fB&&(A=>A.body)};var hB;uB&&(hB=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((A=>{!wB[A]&&(wB[A]=gd.isFunction(hB[A])?e=>e[A]():(e,t)=>{throw new pd(`Response type '${A}' is not supported`,pd.ERR_NOT_SUPPORT,t)})})));const mB=(e,t)=>u(A,null,(function*(){const r=gd.toFiniteNumber(e.getContentLength());return null==r?(e=>u(A,null,(function*(){if(null==e)return 0;if(gd.isBlob(e))return e.size;if(gd.isSpecCompliantForm(e)){const A=new Request(Td.origin,{method:"POST",body:e});return(yield A.arrayBuffer()).byteLength}return gd.isArrayBufferView(e)||gd.isArrayBuffer(e)?e.byteLength:(gd.isURLSearchParams(e)&&(e+=""),gd.isString(e)?(yield BB(e)).byteLength:void 0)})))(t):r})),QB=uB&&(e=>u(A,null,(function*(){let{url:A,method:t,data:r,signal:n,cancelToken:s,timeout:o,onDownloadProgress:a,onUploadProgress:i,responseType:u,headers:d,withCredentials:B="same-origin",fetchOptions:g}=nB(e);u=u?(u+"").toLowerCase():"text";let p,f=oB([n,s&&s.toAbortSignal()],o);const w=f&&f.unsubscribe&&(()=>{f.unsubscribe()});let h;try{if(i&&pB&&"get"!==t&&"head"!==t&&0!==(h=yield mB(d,r))){let e,t=new Request(A,{method:"POST",body:r,duplex:"half"});if(gd.isFormData(r)&&(e=t.headers.get("content-type"))&&d.setContentType(e),t.body){const[A,e]=zd(h,Zd(qd(i)));r=cB(t.body,65536,A,e)}}gd.isString(B)||(B=B?"include":"omit");const n="credentials"in Request.prototype;p=new Request(A,c(l({},g),{signal:f,method:t.toUpperCase(),headers:d.normalize().toJSON(),body:r,duplex:"half",credentials:n?B:void 0}));let s=yield fetch(p);const o=fB&&("stream"===u||"response"===u);if(fB&&(a||o&&w)){const A={};["status","statusText","headers"].forEach((e=>{A[e]=s[e]}));const e=gd.toFiniteNumber(s.headers.get("content-length")),[t,r]=a&&zd(e,Zd(qd(a),!0))||[];s=new Response(cB(s.body,65536,t,(()=>{r&&r(),w&&w()})),A)}u=u||"text";let m=yield wB[gd.findKey(wB,u)||"text"](s,e);return!o&&w&&w(),yield new Promise(((A,t)=>{Yd(A,t,{data:m,headers:Jd.from(s.headers),status:s.status,statusText:s.statusText,config:e,request:p})}))}catch(m){if(w&&w(),m&&"TypeError"===m.name&&/Load failed|fetch/i.test(m.message))throw Object.assign(new pd("Network Error",pd.ERR_NETWORK,e,p),{cause:m.cause||m});throw pd.from(m,m&&m.code,e,p)}}))),yB={http:null,xhr:sB,fetch:QB};gd.forEach(yB,((A,e)=>{if(A){try{Object.defineProperty(A,"name",{value:e})}catch(t){}Object.defineProperty(A,"adapterName",{value:e})}}));const CB=A=>`- ${A}`,UB=A=>gd.isFunction(A)||null===A||!1===A,vB=A=>{A=gd.isArray(A)?A:[A];const{length:e}=A;let t,r;const n={};for(let s=0;s<e;s++){let e;if(t=A[s],r=t,!UB(t)&&(r=yB[(e=String(t)).toLowerCase()],void 0===r))throw new pd(`Unknown adapter '${e}'`);if(r)break;n[e||"#"+s]=r}if(!r){const A=Object.entries(n).map((([A,e])=>`adapter ${A} `+(!1===e?"is not supported by the environment":"is not available in the build")));throw new pd("There is no suitable adapter to dispatch the request "+(e?A.length>1?"since :\n"+A.map(CB).join("\n"):" "+CB(A[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function FB(A){if(A.cancelToken&&A.cancelToken.throwIfRequested(),A.signal&&A.signal.aborted)throw new Wd(null,A)}function bB(A){return FB(A),A.headers=Jd.from(A.headers),A.data=Xd.call(A,A.transformRequest),-1!==["post","put","patch"].indexOf(A.method)&&A.headers.setContentType("application/x-www-form-urlencoded",!1),vB(A.adapter||Md.adapter)(A).then((function(e){return FB(A),e.data=Xd.call(A,A.transformResponse,e),e.headers=Jd.from(e.headers),e}),(function(e){return jd(e)||(FB(A),e&&e.response&&(e.response.data=Xd.call(A,A.transformResponse,e.response),e.response.headers=Jd.from(e.response.headers))),Promise.reject(e)}))}const _B="1.9.0",xB={};["object","boolean","number","function","string","symbol"].forEach(((A,e)=>{xB[A]=function(t){return typeof t===A||"a"+(e<1?"n ":" ")+A}}));const EB={};xB.transitional=function(A,e,t){return(r,n,s)=>{if(!1===A)throw new pd(function(A,e){return"[Axios v1.9.0] Transitional option '"+A+"'"+e+(t?". "+t:"")}(n," has been removed"+(e?" in "+e:"")),pd.ERR_DEPRECATED);return e&&!EB[n]&&(EB[n]=!0),!A||A(r,n,s)}},xB.spelling=function(A){return(A,e)=>!0};const HB={assertOptions:function(A,e,t){if("object"!=typeof A)throw new pd("options must be an object",pd.ERR_BAD_OPTION_VALUE);const r=Object.keys(A);let n=r.length;for(;n-- >0;){const s=r[n],o=e[s];if(o){const e=A[s],t=void 0===e||o(e,s,A);if(!0!==t)throw new pd("option "+s+" must be "+t,pd.ERR_BAD_OPTION_VALUE)}else if(!0!==t)throw new pd("Unknown option "+s,pd.ERR_BAD_OPTION)}},validators:xB},IB=HB.validators;let kB=class{constructor(A){this.defaults=A||{},this.interceptors={request:new xd,response:new xd}}request(A,e){return u(this,null,(function*(){try{return yield this._request(A,e)}catch(t){if(t instanceof Error){let A={};Error.captureStackTrace?Error.captureStackTrace(A):A=new Error;const e=A.stack?A.stack.replace(/^.+\n/,""):"";try{t.stack?e&&!String(t.stack).endsWith(e.replace(/^.+\n.+\n/,""))&&(t.stack+="\n"+e):t.stack=e}catch(r){}}throw t}}))}_request(A,e){"string"==typeof A?(e=e||{}).url=A:e=A||{},e=rB(this.defaults,e);const{transitional:t,paramsSerializer:r,headers:n}=e;void 0!==t&&HB.assertOptions(t,{silentJSONParsing:IB.transitional(IB.boolean),forcedJSONParsing:IB.transitional(IB.boolean),clarifyTimeoutError:IB.transitional(IB.boolean)},!1),null!=r&&(gd.isFunction(r)?e.paramsSerializer={serialize:r}:HB.assertOptions(r,{encode:IB.function,serialize:IB.function},!0)),void 0!==e.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?e.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:e.allowAbsoluteUrls=!0),HB.assertOptions(e,{baseUrl:IB.spelling("baseURL"),withXsrfToken:IB.spelling("withXSRFToken")},!0),e.method=(e.method||this.defaults.method||"get").toLowerCase();let s=n&&gd.merge(n.common,n[e.method]);n&&gd.forEach(["delete","get","head","post","put","patch","common"],(A=>{delete n[A]})),e.headers=Jd.concat(s,n);const o=[];let a=!0;this.interceptors.request.forEach((function(A){"function"==typeof A.runWhen&&!1===A.runWhen(e)||(a=a&&A.synchronous,o.unshift(A.fulfilled,A.rejected))}));const i=[];let l;this.interceptors.response.forEach((function(A){i.push(A.fulfilled,A.rejected)}));let c,u=0;if(!a){const A=[bB.bind(this),void 0];for(A.unshift.apply(A,o),A.push.apply(A,i),c=A.length,l=Promise.resolve(e);u<c;)l=l.then(A[u++],A[u++]);return l}c=o.length;let d=e;for(u=0;u<c;){const A=o[u++],e=o[u++];try{d=A(d)}catch(B){e.call(this,B);break}}try{l=bB.call(this,d)}catch(B){return Promise.reject(B)}for(u=0,c=i.length;u<c;)l=l.then(i[u++],i[u++]);return l}getUri(A){return _d(eB((A=rB(this.defaults,A)).baseURL,A.url,A.allowAbsoluteUrls),A.params,A.paramsSerializer)}};gd.forEach(["delete","get","head","options"],(function(A){kB.prototype[A]=function(e,t){return this.request(rB(t||{},{method:A,url:e,data:(t||{}).data}))}})),gd.forEach(["post","put","patch"],(function(A){function e(e){return function(t,r,n){return this.request(rB(n||{},{method:A,headers:e?{"Content-Type":"multipart/form-data"}:{},url:t,data:r}))}}kB.prototype[A]=e(),kB.prototype[A+"Form"]=e(!0)}));const LB={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(LB).forEach((([A,e])=>{LB[e]=A}));const SB=function A(e){const t=new kB(e),r=_u(kB.prototype.request,t);return gd.extend(r,kB.prototype,t,{allOwnKeys:!0}),gd.extend(r,t,null,{allOwnKeys:!0}),r.create=function(t){return A(rB(e,t))},r}(Md);SB.Axios=kB,SB.CanceledError=Wd,SB.CancelToken=class A{constructor(A){if("function"!=typeof A)throw new TypeError("executor must be a function.");let e;this.promise=new Promise((function(A){e=A}));const t=this;this.promise.then((A=>{if(!t._listeners)return;let e=t._listeners.length;for(;e-- >0;)t._listeners[e](A);t._listeners=null})),this.promise.then=A=>{let e;const r=new Promise((A=>{t.subscribe(A),e=A})).then(A);return r.cancel=function(){t.unsubscribe(e)},r},A((function(A,r,n){t.reason||(t.reason=new Wd(A,r,n),e(t.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(A){this.reason?A(this.reason):this._listeners?this._listeners.push(A):this._listeners=[A]}unsubscribe(A){if(!this._listeners)return;const e=this._listeners.indexOf(A);-1!==e&&this._listeners.splice(e,1)}toAbortSignal(){const A=new AbortController,e=e=>{A.abort(e)};return this.subscribe(e),A.signal.unsubscribe=()=>this.unsubscribe(e),A.signal}static source(){let e;return{token:new A((function(A){e=A})),cancel:e}}},SB.isCancel=jd,SB.VERSION=_B,SB.toFormData=Cd,SB.AxiosError=pd,SB.Cancel=SB.CanceledError,SB.all=function(A){return Promise.all(A)},SB.spread=function(A){return function(e){return A.apply(null,e)}},SB.isAxiosError=function(A){return gd.isObject(A)&&!0===A.isAxiosError},SB.mergeConfig=rB,SB.AxiosHeaders=Jd,SB.formToJSON=A=>Od(gd.isHTMLForm(A)?new FormData(A):A),SB.getAdapter=vB,SB.HttpStatusCode=LB,SB.default=SB;const{Axios:KB,AxiosError:DB,CanceledError:TB,isCancel:OB,CancelToken:MB,VERSION:RB,all:PB,Cancel:NB,isAxiosError:VB,spread:GB,toFormData:JB,AxiosHeaders:XB,HttpStatusCode:jB,formToJSON:WB,getAdapter:YB,mergeConfig:ZB}=SB;function zB(A,e){var t,r,n,s,o,a,i,l,c,u,d,B,g,p;try{if(!A)throw new Error("技术分析数据为空");if(function(A){return"object"==typeof A&&null!==A&&"status"in A&&"string"==typeof A.status}(A)&&"data"in A){if("success"!==A.status)throw new Error(`API响应错误: ${A.status}`);if(!A.data)throw new Error("API响应中data为空");A=A.data}if(function(A){return"object"==typeof A&&null!==A&&"trend_up_probability"in A&&"trend_sideways_probability"in A&&"trend_down_probability"in A}(A))try{return{current_price:"number"==typeof A.current_price?A.current_price:0,snapshot_price:"number"==typeof A.snapshot_price?A.snapshot_price:0,trend_analysis:{probabilities:{up:"number"==typeof A.trend_up_probability?A.trend_up_probability:0,sideways:"number"==typeof A.trend_sideways_probability?A.trend_sideways_probability:0,down:"number"==typeof A.trend_down_probability?A.trend_down_probability:0},summary:"string"==typeof A.trend_summary?A.trend_summary:"无数据"},indicators_analysis:A.indicators_analysis||{},trading_advice:{action:"string"==typeof A.trading_action?A.trading_action:"无建议",reason:"string"==typeof A.trading_reason?A.trading_reason:"无数据",entry_price:"number"==typeof A.entry_price?A.entry_price:0,stop_loss:"number"==typeof A.stop_loss?A.stop_loss:0,take_profit:"number"==typeof A.take_profit?A.take_profit:0},risk_assessment:{level:"string"==typeof A.risk_level?A.risk_level:"中",score:"number"==typeof A.risk_score?A.risk_score:50,details:Array.isArray(A.risk_details)?A.risk_details:[]},last_update_time:"string"==typeof A.last_update_time?A.last_update_time:(new Date).toISOString()}}catch(f){throw new Error(`格式化强制刷新数据失败: ${f instanceof Error?f.message:String(f)}`)}if(function(A){return"object"==typeof A&&null!==A&&"trend_analysis"in A&&"indicators_analysis"in A&&"trading_advice"in A}(A))try{localStorage.getItem("i18n_debug");const e="number"==typeof A.price?A.price:0;let f=A.risk_assessment;return!f&&A.trading_advice&&(f={level:null!=(t=A.trading_advice.risk_level)?t:"中",score:null!=(r=A.trading_advice.risk_score)?r:50,details:Array.isArray(A.trading_advice.risk_details)?A.trading_advice.risk_details:[]}),{current_price:"number"==typeof A.current_price?A.current_price:e,snapshot_price:"number"==typeof A.snapshot_price?A.snapshot_price:e,trend_analysis:{probabilities:{up:"number"==typeof(null==(s=null==(n=A.trend_analysis)?void 0:n.probabilities)?void 0:s.up)?A.trend_analysis.probabilities.up:0,sideways:"number"==typeof(null==(a=null==(o=A.trend_analysis)?void 0:o.probabilities)?void 0:a.sideways)?A.trend_analysis.probabilities.sideways:0,down:"number"==typeof(null==(l=null==(i=A.trend_analysis)?void 0:i.probabilities)?void 0:l.down)?A.trend_analysis.probabilities.down:0},summary:"string"==typeof(null==(c=A.trend_analysis)?void 0:c.summary)?A.trend_analysis.summary:"无数据"},indicators_analysis:A.indicators_analysis||{},trading_advice:{action:"string"==typeof(null==(u=A.trading_advice)?void 0:u.action)?A.trading_advice.action:"无建议",reason:"string"==typeof(null==(d=A.trading_advice)?void 0:d.reason)?A.trading_advice.reason:"无数据",entry_price:"number"==typeof(null==(B=A.trading_advice)?void 0:B.entry_price)?A.trading_advice.entry_price:0,stop_loss:"number"==typeof(null==(g=A.trading_advice)?void 0:g.stop_loss)?A.trading_advice.stop_loss:0,take_profit:"number"==typeof(null==(p=A.trading_advice)?void 0:p.take_profit)?A.trading_advice.take_profit:0},risk_assessment:{level:"string"==typeof(null==f?void 0:f.level)?f.level:"中",score:"number"==typeof(null==f?void 0:f.score)?f.score:50,details:Array.isArray(null==f?void 0:f.details)?f.details:[]},last_update_time:"string"==typeof A.last_update_time?A.last_update_time:"string"==typeof A.timestamp?A.timestamp:(new Date).toISOString()}}catch(f){throw new Error(`格式化技术分析数据失败: ${f instanceof Error?f.message:String(f)}`)}throw new Error("无法识别的数据格式")}catch(f){return{current_price:0,snapshot_price:0,trend_analysis:{probabilities:{up:.33,sideways:.34,down:.33},summary:"Data loading failed, please refresh and try again"},indicators_analysis:{RSI:{value:0,analysis:"Data loading failed",support_trend:"neutral"},MACD:{value:{line:0,signal:0,histogram:0},analysis:"Data loading failed",support_trend:"neutral"},BollingerBands:{value:{upper:0,middle:0,lower:0},analysis:"Data loading failed",support_trend:"neutral"},BIAS:{value:0,analysis:"Data loading failed",support_trend:"neutral"},PSY:{value:0,analysis:"Data loading failed",support_trend:"neutral"},DMI:{value:{plus_di:0,minus_di:0,adx:0},analysis:"Data loading failed",support_trend:"neutral"},VWAP:{value:0,analysis:"Data loading failed",support_trend:"neutral"},FundingRate:{value:0,analysis:"Data loading failed",support_trend:"neutral"},ExchangeNetflow:{value:0,analysis:"Data loading failed",support_trend:"neutral"},NUPL:{value:0,analysis:"Data loading failed",support_trend:"neutral"},MayerMultiple:{value:0,analysis:"Data loading failed",support_trend:"neutral"}},trading_advice:{action:"No advice",reason:"Data loading failed",entry_price:0,stop_loss:0,take_profit:0},risk_assessment:{level:"medium",score:50,details:["Data loading failed, unable to assess risk"]},last_update_time:(new Date).toISOString()}}}const qB=()=>"undefined"!=typeof chrome&&void 0!==chrome.runtime&&"function"==typeof chrome.runtime.sendMessage,$B=e=>u(A,null,(function*(){if(!qB())throw new Error("Not running in extension environment, cannot use proxy");return new Promise(((A,t)=>{const{url:r,method:n,headers:s,data:o,params:a}=e;let i=r||"";if(a&&Object.keys(a).length>0){const A=new URLSearchParams;Object.entries(a).forEach((([e,t])=>{null!=t&&A.append(e,String(t))}));const e=A.toString();e&&(i+=(i.includes("?")?"&":"?")+e)}const u=i.includes("force_refresh=true");let d=null;const B=u?12e4:3e4;d=window.setTimeout((()=>{t(new Error(`Request timed out (${B/1e3}s)`))}),B);let g=s||{};if(!g.Authorization){const A=localStorage.getItem("token");A&&(g=c(l({},g),{Authorization:A}))}const p="string"==typeof n?n.toUpperCase():"GET";"undefined"!=typeof chrome&&chrome.runtime&&chrome.runtime.sendMessage&&chrome.runtime.id?chrome.runtime.sendMessage({type:"PROXY_API_REQUEST",data:{url:i,method:p,headers:g,body:o}},(e=>{if(null!==d&&clearTimeout(d),chrome.runtime.lastError)t(new Error(chrome.runtime.lastError.message));else{if(!e||!e.success){const A=new Error((null==e?void 0:e.error)||"Proxy request failed");return(null==e?void 0:e.errorDetail)&&(A.detail=e.errorDetail),void t(A)}try{A({data:e.data,status:e.status,statusText:e.statusText,headers:{},config:{}})}catch(r){A({data:e.data,status:200,statusText:"OK",headers:{},config:{}})}}})):t(new Error("Chrome extension API not available. Please run in Chrome extension environment."))}))})),Ag=SB.create({baseURL:"http://127.0.0.1:8000/api",timeout:6e4,headers:{"Content-Type":"application/json"}});let eg=[],tg=0;const rg=()=>{const A=localStorage.getItem("token");if(!A)return!1;if(!A.startsWith("Token ")&&!A.startsWith("Bearer "))try{return localStorage.setItem("token",`Token ${A}`),!0}catch(e){return!1}return!(A.startsWith("Token ")&&A.replace("Token ","").length<5)},ng=A=>!!A&&(A.includes("/auth/login")||A.includes("/auth/register")||A.includes("/auth/send-code")||A.includes("/auth/request-password-reset")||A.includes("/auth/reset-password-with-code")),sg=()=>u(A,null,(function*(){const A=Date.now(),e=A-6e4;if(eg=eg.filter((A=>A.timestamp>e)),eg.length>=30){const e=6e4-(A-eg[0].timestamp);if(e>0)return yield new Promise((A=>setTimeout(A,e))),void(yield sg())}const t=A-tg;if(t<2e3){const A=2e3-t;yield new Promise((e=>setTimeout(e,A)))}eg.push({timestamp:A,count:1}),tg=A})),og=(e,t=0)=>u(A,null,(function*(){var A,r,n,s,o;try{if(!rg())throw new Error("Token validation failed");yield sg();const t=localStorage.getItem("token");return t&&(t.startsWith("Token ")||t.startsWith("Bearer ")?e.headers.Authorization=t:e.headers.Authorization=`Token ${t}`),e.headers["Cache-Control"]="no-cache",e.headers.Pragma="no-cache",(null==(A=e.params)?void 0:A.force_refresh)&&(e.timeout=12e4),qB()?$B(e):yield Ag(e)}catch(a){if("ERR_FILE_NOT_FOUND"===a.code&&qB()&&(null==(n=null==(r=a.config)?void 0:r.url)?void 0:n.includes(chrome.runtime.getURL(""))))return chrome.runtime.sendMessage({type:"RELOAD_RESOURCES"}),yield new Promise((A=>setTimeout(A,1e3))),og(e,t+1);if("Token validation failed"===a.message)return localStorage.removeItem("token"),localStorage.removeItem("userInfo"),window.location.href="/login",Promise.reject(a);const A=(null==(s=e.params)?void 0:s.force_refresh)?6:3;if(t<A&&("ERR_NETWORK"===a.code||"ERR_FILE_NOT_FOUND"===a.code||"ECONNABORTED"===a.code||500===(null==(o=a.response)?void 0:o.status))){const A=2e3*Math.pow(2,t);return yield new Promise((e=>setTimeout(e,A))),og(e,t+1)}throw a}}));Ag.interceptors.request.use((e=>u(A,null,(function*(){try{if(ng(e.url)||e.headers.Authorization)e.headers.Authorization;else{const A=localStorage.getItem("token");A&&(e.headers.Authorization=A)}if(!ng(e.url)&&!rg())return Promise.reject(new Error("Token validation failed"));if(!ng(e.url)){const A=localStorage.getItem("token");A&&(A.startsWith("Token ")||A.startsWith("Bearer ")?e.headers.Authorization=A:e.headers.Authorization=`Token ${A}`)}return qB()?$B(e):(yield sg(),e)}catch(A){return Promise.reject(A)}}))),(A=>Promise.reject(A))),Ag.interceptors.response.use((A=>A.data&&"object"==typeof A.data?"success"===A.data.status||"error"===A.data.status?A.data:{status:"success",data:A.data}:Promise.reject(new Error("Invalid data format"))),(e=>u(A,null,(function*(){var A,t;const r=e.config;if("Token validation failed"===e.message)return localStorage.removeItem("token"),localStorage.removeItem("userInfo"),window.location.href="/login",Promise.reject(e);if("ERR_NETWORK"===e.code&&!r._retry){r._retry=!0;try{return yield og(r)}catch(n){return Promise.reject(n)}}if(("ERR_FILE_NOT_FOUND"===e.code||500===(null==(A=e.response)?void 0:A.status))&&!r._retry){r._retry=!0;try{return yield og(r)}catch(n){return Promise.reject(n)}}return 401===(null==(t=e.response)?void 0:t.status)&&(localStorage.removeItem("token"),localStorage.removeItem("userInfo"),window.location.href="/login"),Promise.reject(e)}))));const ag=e=>u(A,null,(function*(){try{const A="http://127.0.0.1:8000/api/auth/send-code/";return(yield SB.post(A,{email:e.email.trim()},{headers:{"Content-Type":"application/json"}})).data}catch(A){throw A}})),ig=e=>u(A,null,(function*(){try{const A="http://127.0.0.1:8000/api/auth/register/";return(yield SB.post(A,{email:e.email.trim(),password:e.password.trim(),code:e.code.trim(),invitation_code:e.invitation_code.trim()},{headers:{"Content-Type":"application/json"}})).data}catch(A){throw A}})),lg=e=>u(A,null,(function*(){try{const A="http://127.0.0.1:8000/api/auth/login/";return(yield SB.post(A,{email:e.email.trim(),password:e.password.trim()},{headers:{"Content-Type":"application/json"}})).data}catch(A){throw A}})),cg=e=>u(A,null,(function*(){try{return(yield Ag.post("/auth/reset-password-with-code/",{email:e.email.trim(),code:e.code.trim(),new_password:e.new_password.trim(),confirm_password:e.confirm_password.trim()})).data}catch(A){throw A}})),ug=e=>u(A,null,(function*(){try{const A="http://127.0.0.1:8000/api/auth/change-password/",t=localStorage.getItem("token");if(!t)throw new Error("Not logged in, unable to change password");return(yield SB.post(A,{current_password:e.current_password.trim(),new_password:e.new_password.trim(),confirm_password:e.confirm_password.trim()},{headers:{"Content-Type":"application/json",Authorization:t}})).data}catch(A){throw A}})),dg=(e,t=!1)=>u(A,null,(function*(){var A,r,n;if(!e||"string"!=typeof e||!e.trim())throw new Error("Invalid symbol provided");try{const A=e.toUpperCase(),r=`/crypto/technical-indicators/${A.endsWith("USDT")?A:`${A}USDT`}/`,n={};t&&(n._t=Date.now());const s=`http://127.0.0.1:8000/api${r}`,o=localStorage.getItem("token"),a=o?o.startsWith("Token ")?o:`Token ${o}`:"",i=(yield SB.get(s,{params:n,headers:{"Content-Type":"application/json",Authorization:a}})).data;if("object"==typeof i&&"status"in i){if("not_found"===i.status)return i;if("success"===i.status&&"data"in i)return zB(i.data)}return zB(i)}catch(s){if(404===(null==(A=s.response)?void 0:A.status))return{status:"not_found",message:(null==(n=null==(r=s.response)?void 0:r.data)?void 0:n.message)||"Token data not found",needs_refresh:!0};if("ERR_NETWORK"===s.code)throw new Error("Network connection error, please check your network");throw s}}));let Bg={};const gg=e=>u(A,null,(function*(){var A,t,r;let n="";if(!e||"string"!=typeof e||!e.trim())throw new Error("交易对无效，无法刷新报告");try{const o=e.toUpperCase();n=`/crypto/get_report/${o.endsWith("USDT")?o:`${o}USDT`}/`;const a={};a._t=Date.now();const i=`${n}`;if(Bg[i])throw new Error("请求正在进行中，请稍后再试");Bg[i]=!0;try{const A=(yield og({url:n,method:"GET",params:a,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})).data;let e=A;if(A&&Array.isArray(A.reports)&&A.reports.length>0&&(e=A.reports[0]),"object"==typeof A&&"status"in A){if("not_found"===A.status)return A;if("success"===A.status&&"data"in A)return zB(e)}return zB(e)}catch(s){if(404===(null==(A=s.response)?void 0:A.status))return{status:"not_found",message:(null==(r=null==(t=s.response)?void 0:t.data)?void 0:r.message)||"未找到交易对数据",needs_refresh:!0};if("ERR_NETWORK"===s.code)throw new Error("网络连接错误，请检查您的网络连接");if("ECONNABORTED"===s.code)throw new Error("请求超时，服务器响应时间过长，请稍后重试");throw s}}finally{n&&(Bg[`${n}`]=!1)}})),pg=()=>u(A,null,(function*(){try{if(qB()){const A=yield new Promise(((A,e)=>{chrome.runtime.sendMessage({type:"PROXY_API_REQUEST",data:{url:"/auth/invitation-info/",method:"GET",headers:{Authorization:localStorage.getItem("token"),Accept:"application/json"}}},(t=>{chrome.runtime.lastError?e(new Error(chrome.runtime.lastError.message)):t.success?A(t):e(new Error(t.error||"请求失败"))}))}));if(A&&A.success&&A.data)return A.data;throw new Error("Invalid response format")}return yield Ag.get("/auth/invitation-info/")}catch(A){throw A}})),fg=()=>u(A,null,(function*(){try{return qB()?yield new Promise(((A,e)=>{chrome.runtime.sendMessage({type:"PROXY_API_REQUEST",data:{url:"/auth/invitation-info/ranking/",method:"GET",headers:{Authorization:localStorage.getItem("token"),Accept:"application/json"}}},(t=>{chrome.runtime.lastError?e(new Error(chrome.runtime.lastError.message)):t.success?A(t.data):e(new Error(t.error||"请求失败"))}))})):yield Ag.get("/auth/invitation-info/ranking/")}catch(A){throw A}})),wg=e=>u(A,null,(function*(){try{if(qB()){const A=yield new Promise(((A,t)=>{chrome.runtime.sendMessage({type:"PROXY_API_REQUEST",data:{url:"/auth/claim-temporary-invitation/",method:"POST",headers:{Authorization:localStorage.getItem("token"),Accept:"application/json","Content-Type":"application/json"},body:{temporary_invitation_uuid:e}}},(e=>{chrome.runtime.lastError?t(new Error(chrome.runtime.lastError.message)):e.success?A(e):t(new Error(e.error||"请求失败"))}))}));if(A&&A.success&&A.data)return A.data;throw new Error("Invalid response format")}return yield Ag.post("/auth/claim-temporary-invitation/",{temporary_invitation_uuid:e})}catch(A){throw A}})),hg=(e,t,r=20)=>u(A,null,(function*(){try{const A={q:e,limit:r};return t&&(A.market_type=t),yield Ag.get("/crypto/search/",{params:A})}catch(A){throw A}})),mg=()=>u(A,null,(function*(){try{return yield Ag.get("/crypto/favorites/")}catch(A){throw A}})),Qg=e=>u(A,null,(function*(){try{return yield Ag.post("/crypto/favorites/",{symbol:e.symbol,market_type:e.market_type,name:e.name,exchange:e.exchange,sector:e.sector})}catch(A){throw A}})),yg=(e,t)=>u(A,null,(function*(){try{return yield Ag.delete("/crypto/favorites/",{data:{symbol:e,market_type:t}})}catch(A){throw A}})),Cg=(e,t="crypto")=>u(A,null,(function*(){try{return yield Ag.get(`/crypto/favorites/status/${e}/`,{params:{market_type:t}})}catch(A){throw A}})),Ug=[{name:"Gate.io",baseUrl:"gate.io",tradeUrlPatterns:["/zh/trade/","/en/trade/","/trade/"],symbolRegexes:[/\/trade\/([A-Z0-9]+)_USDT/i,/\/[a-z]{2}\/trade\/([A-Z0-9]+)_USDT/i,/\/([A-Z0-9]+)_USDT$/i]},{name:"Binance",baseUrl:"binance.com",tradeUrlPatterns:["/*/trade/","/trade/"],symbolRegexes:[/\/([A-Z0-9]+)USDT$/i,/\/trade\/([A-Z0-9]+)_USDT/i,/\/trading\/([A-Z0-9]+)USDT/i]},{name:"OKX",baseUrl:"okx.com",tradeUrlPatterns:["/*/trade-spot/","/*/trade/","/trade-spot/","/trade/"],symbolRegexes:[/\/([A-Z0-9]+)-USDT$/i,/\/trade\/([A-Z0-9]+)-USDT/i,/\/spot\/([A-Z0-9]+)-USDT/i]},{name:"HTX",baseUrl:"htx.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/([a-zA-Z0-9]+)_([a-zA-Z0-9]+)/i]},{name:"Bybit",baseUrl:"bybit.com",tradeUrlPatterns:["/trade/usdt/","/trade/spot/","/en/trade/","/zh/trade/"],symbolRegexes:[/\/trade\/usdt\/([A-Z0-9]+)/i,/\/trade\/spot\/([A-Z0-9]+)\/USDT/i,/\/[a-z]{2}\/trade\/spot\/([A-Z0-9]+)\/USDT/i,/\/([A-Z0-9]+)\/USDT$/i]},{name:"Bitmart",baseUrl:"bitmart.com",tradeUrlPatterns:["/trade/en"],symbolRegexes:[/\/trade\/en\?symbol=([A-Z0-9_]+)/i]},{name:"Coinbase",baseUrl:"coinbase.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/([A-Z0-9-]+)/i]},{name:"Bitstamp",baseUrl:"bitstamp.net",tradeUrlPatterns:["/markets/"],symbolRegexes:[/\/markets\/([a-z0-9]+)\/([a-z0-9]+)/i]},{name:"Kucoin",baseUrl:"kucoin.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/(\w+)-(\w+)/i]},{name:"Poloniex",baseUrl:"poloniex.com",tradeUrlPatterns:["/spot/"],symbolRegexes:[/\/spot\/([A-Z0-9_]+)/i]},{name:"Bithumb",baseUrl:"bithumb.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/([A-Z0-9_]+)/i]},{name:"Upbit",baseUrl:"upbit.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\?code=CRIX.UPBIT.([A-Z0-9]+)-([A-Z0-9]+)/i]},{name:"Bitflyer",baseUrl:"bitflyer.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/([A-Z0-9_]+)/i]},{name:"Gemini",baseUrl:"gemini.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/([A-Z0-9]+)[-_/]([A-Z0-9]+)/i]},{name:"LBank",baseUrl:"lbank.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/([A-Z0-9_]+)/i]},{name:"Phemex",baseUrl:"phemex.com",tradeUrlPatterns:["/spot/trade"],symbolRegexes:[/\/spot\/trade\?symbol=([A-Z0-9_]+)/i]},{name:"MEXC",baseUrl:"mexc.com",tradeUrlPatterns:["/exchange/","/futures/"],symbolRegexes:[/\/exchange\/([A-Z0-9_]+)/i,/\/futures\/([A-Z0-9_]+)/i]},{name:"Bitget",baseUrl:"bitget.com",tradeUrlPatterns:["/spot/"],symbolRegexes:[/\/spot\/([A-Z0-9_]+)/i]},{name:"Bitfinex",baseUrl:"bitfinex.com",tradeUrlPatterns:["/t/"],symbolRegexes:[/\/t\/([A-Z0-9:]+)/i]},{name:"Kraken",baseUrl:"kraken.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/([A-Z0-9]+)-([A-Z0-9]+)/i]},{name:"Huobi",baseUrl:"huobi.com",tradeUrlPatterns:["/en-us/exchange/"],symbolRegexes:[/\/exchange\/([a-z0-9]+)\/\?type=spot/i]},{name:"Gate.com",baseUrl:"gate.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/([A-Z0-9]+)_([A-Z0-9]+)/i]}],vg=A=>{try{if(A.includes("mexc.com")&&A.includes("/futures/")){const e=A.match(/\/futures\/([A-Z0-9_]+)/i);if(e){const A=e[1];if(A.includes("_"))return A.split("_")[0].toUpperCase()+"USDT"}}for(const e of Ug)if(A.includes(e.baseUrl))for(const t of e.symbolRegexes){const r=A.match(t);if(r)return"Gate.io"===e.name&&t.source.includes("_USDT")?r[1].toUpperCase()+"USDT":r.slice(1).map((A=>A.toUpperCase())).join("")}return null}catch(e){return null}};Ug.map((A=>({name:A.name,baseUrl:A.baseUrl})));const Fg={key:0,class:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60"},bg={class:"bg-[#232a36] rounded-xl shadow-lg px-6 py-8 flex flex-col items-center w-80 border-2 border-blue-500"},_g={key:0,class:"w-full text-center"},xg={class:"text-white text-lg font-semibold mb-1"},Eg={class:"text-gray-400 text-sm"},Hg=y({__name:"LoadingModal",props:{visible:{type:Boolean,default:!1},type:{default:"refresh"},customStages:{}},setup(A){const{t:e}=bu(),t=A,r=f(""),n=f("");let s=null;const o=()=>localStorage.getItem("language")||"en-US",a=(A,t)=>{const r=e(A);return r===A?t:r},i=[{title:()=>a("analysis.calculating_indicators","zh-CN"===o()?"正在获取市场数据":"en-US"===o()?"Fetching market data...":"ja-JP"===o()?"市場データ取得中...":"ko-KR"===o()?"시장 데이터 가져오는 중...":"Fetching market data..."),sub:()=>a("analysis.calculating_indicators","zh-CN"===o()?"获取最新价格和交易量数据":"en-US"===o()?"Getting latest price and volume data":"ja-JP"===o()?"最新の価格と出来高データを取得":"ko-KR"===o()?"최신 가격 및 거래량 데이터 가져오기":"Getting latest price and volume data")},{title:()=>a("analysis.analyzing_trends","zh-CN"===o()?"正在计算技术指标":"en-US"===o()?"Calculating technical indicators...":"ja-JP"===o()?"テクニカル指標計算中...":"ko-KR"===o()?"기술 지표 계산 중...":"Calculating technical indicators..."),sub:()=>a("analysis.analyzing_trends","zh-CN"===o()?"分析RSI、MACD、布林带等指标":"en-US"===o()?"Analyzing RSI, MACD, Bollinger Bands, etc.":"ja-JP"===o()?"RSI、MACD、ボリンジャーバンドなどを分析":"ko-KR"===o()?"RSI, MACD, 볼린저 밴드 등 분석":"Analyzing RSI, MACD, Bollinger Bands, etc.")},{title:()=>a("analysis.generating_advice","zh-CN"===o()?"正在分析市场趋势":"en-US"===o()?"Analyzing market trends...":"ja-JP"===o()?"市場トレンド分析中...":"ko-KR"===o()?"시장 트렌드 분석 중...":"Analyzing market trends..."),sub:()=>a("analysis.generating_advice","zh-CN"===o()?"评估上涨、下跌和盘整概率":"en-US"===o()?"Evaluating up, down, and sideways probabilities":"ja-JP"===o()?"上昇、下降、横ばいの確率を評価":"ko-KR"===o()?"상승, 하락, 횡보 확률 평가":"Evaluating up, down, and sideways probabilities")},{title:()=>a("analysis.finalizing_data","zh-CN"===o()?"正在生成交易建议":"en-US"===o()?"Generating trading advice...":"ja-JP"===o()?"取引アドバイス生成中...":"ko-KR"===o()?"거래 조언 생성 중...":"Generating trading advice..."),sub:()=>a("analysis.finalizing_data","zh-CN"===o()?"制定入场价、止损价和目标价":"en-US"===o()?"Setting entry, stop loss, and target prices":"ja-JP"===o()?"エントリー、ストップロス、ターゲット価格を設定":"ko-KR"===o()?"진입, 손절, 목표 가격 설정":"Setting entry, stop loss, and target prices")},{title:()=>a("analysis.preparing_analysis_report","zh-CN"===o()?"正在完成分析报告":"en-US"===o()?"Finalizing analysis report...":"ja-JP"===o()?"分析レポート完成中...":"ko-KR"===o()?"분석 보고서 완성 중...":"Finalizing analysis report..."),sub:()=>""}],l=[{title:()=>a("analysis.generating_new_report","zh-CN"===o()?"正在生成新的分析报告":"en-US"===o()?"Generating new analysis report...":"ja-JP"===o()?"新しい分析レポートを生成中...":"ko-KR"===o()?"새로운 분석 보고서 생성 중...":"Generating new analysis report..."),sub:()=>a("analysis.please_wait","zh-CN"===o()?"请耐心等待，这可能需要一些时间":"en-US"===o()?"Please wait, this may take some time":"ja-JP"===o()?"お待ちください、時間がかかる場合があります":"ko-KR"===o()?"잠시 기다려 주세요, 시간이 걸릴 수 있습니다":"Please wait, this may take some time")}],c=()=>{s&&(clearInterval(s),s=null)};return m((()=>t.visible),(A=>{A?(()=>{let A=i;"generate"===t.type?A=l:"custom"===t.type&&t.customStages&&(A=t.customStages.map((A=>({title:()=>A.title,sub:()=>A.sub||""}))));let e=0;r.value=A[0].title(),n.value=A[0].sub(),s&&clearInterval(s),A.length>1&&"refresh"===t.type&&(s=setInterval((()=>{e=(e+1)%A.length,r.value=A[e].title(),n.value=A[e].sub()}),5e3))})():c()})),_((()=>{c()})),(A,e)=>A.visible?(K(),H("div",Fg,[k("div",bg,[e[0]||(e[0]=k("div",{class:"flex items-center justify-center mb-4"},[k("svg",{class:"animate-spin h-10 w-10 text-blue-400",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[k("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),k("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"})])],-1)),"generate"===A.type?(K(),H("div",_g,[k("div",xg,S(l[0].title()),1),k("div",Eg,S(l[0].sub()),1)])):L(A.$slots,"default",{key:1})])])):I("",!0)}}),Ig={class:"flex flex-col items-center justify-center h-full text-center px-4"},kg={class:"text-xl font-semibold text-white mb-2"},Lg={class:"text-gray-300 mb-6"},Sg=["disabled"],Kg={class:"flex items-center justify-center"},Dg=y({__name:"TokenNotFoundView",props:{symbol:{},isRefreshing:{type:Boolean}},emits:["refresh-success","refresh-error"],setup(A,{emit:e}){const{t:t}=bu(),{locale:r}=iu(),n=f(localStorage.getItem("language")||"en-US"),s=(A,e,r)=>{const n=t(A,r);return n===A?e:n};m((()=>r.value),(A=>{n.value=A})),b((()=>{window.addEventListener("language-changed",(A=>{var e;const t=(null==(e=A.detail)?void 0:e.language)||localStorage.getItem("language")||"en-US";n.value=t})),window.addEventListener("force-refresh-i18n",(()=>{const A=localStorage.getItem("language")||"en-US";n.value=A}))}));const o=A,a=e,i=f(!1),l=f(!1),c=h((()=>o.symbol&&"string"==typeof o.symbol?o.symbol.toUpperCase().endsWith("USDT")?o.symbol:`${o.symbol}/USDT`:"Unknown/USDT")),d=()=>u(this,null,(function*(){if(!i.value){i.value=!0,l.value=!0;try{const A=yield gg(o.symbol);if(!A||"not_found"===A.status)throw new Error("Failed to generate report, please try again later");a("refresh-success"),setTimeout((()=>{l.value=!1}),1e3)}catch(A){a("refresh-error",A),l.value=!1}finally{i.value=!1}}}));return _((()=>{l.value=!1,i.value=!1})),(A,e)=>(K(),H("div",Ig,[e[0]||(e[0]=k("div",{class:"mb-6"},[k("i",{class:"ri-database-2-line text-5xl text-yellow-500"})],-1)),k("h2",kg,S(s("tokenNotFound.title","zh-CN"===n.value?`${c.value} 数据未找到`:"en-US"===n.value?`${c.value} Data Not Found`:"ja-JP"===n.value?`${c.value} データが見つかりません`:"ko-KR"===n.value?`${c.value} 데이터를 찾을 수 없습니다`:`${c.value} Data Not Found`,{symbol:c.value})),1),k("p",Lg,S(s("tokenNotFound.description","zh-CN"===n.value?"该代币尚未在我们的数据库中，点击下方按钮获取最新数据":"en-US"===n.value?"This token is not yet in our database. Click the button below to get the latest data.":"ja-JP"===n.value?"このトークンはまだデータベースにありません。下のボタンをクリックして最新データを取得してください。":"ko-KR"===n.value?"이 토큰은 아직 데이터베이스에 없습니다. 아래 버튼을 클릭하여 최신 데이터를 가져오세요.":"This token is not yet in our database. Click the button below to get the latest data.")),1),k("button",{class:"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium mb-4 w-full max-w-xs transition-colors duration-200 flex items-center justify-center",onClick:d,disabled:l.value},[k("span",Kg,[k("i",{class:O(["ri-refresh-line mr-2",{"animate-spin":l.value}])},null,2),T(" "+S(s("tokenNotFound.refreshButton","zh-CN"===n.value?"获取最新市场数据":"en-US"===n.value?"Get Latest Market Data":"ja-JP"===n.value?"最新の市場データを取得":"ko-KR"===n.value?"최신 시장 데이터 가져오기":"Get Latest Market Data")),1)])],8,Sg),l.value?(K(),D(Hg,{key:0,visible:l.value,type:"generate",text:M(t)("tokenNotFound.loading")},null,8,["visible","text"])):I("",!0)]))}}),Tg={class:"w-full"},Og={class:"mt-4 text-center"},Mg={class:"text-gray-400 text-sm mt-2"},Rg=(A,e)=>{const t=A.__vccOpts||A;for(const[r,n]of e)t[r]=n;return t},Pg=Rg(y({__name:"ChartSkeleton",props:{loadingText:{type:String,default:"Loading analysis data..."}},setup:A=>(e,t)=>(K(),H("div",Tg,[t[1]||(t[1]=R('<div class="mt-6 p-5 rounded-lg bg-gradient-to-b from-gray-800/60 to-gray-900/60 border border-gray-700/50 shadow-lg" data-v-180bba7d><div class="text-center text-gray-400 mb-1 h-4 w-24 bg-gray-700/50 rounded animate-pulse mx-auto" data-v-180bba7d></div><div class="text-center text-3xl font-bold mb-2 h-10 w-32 bg-gray-700/50 rounded animate-pulse mx-auto" data-v-180bba7d></div><div class="flex justify-center gap-3 mt-4 mb-2" data-v-180bba7d><div class="h-8 w-24 bg-gray-700/50 rounded-full animate-pulse" data-v-180bba7d></div><div class="h-8 w-24 bg-gray-700/50 rounded-full animate-pulse" data-v-180bba7d></div></div></div><div class="mt-4 flex items-center justify-between pl-2 rounded-lg bg-gradient-to-b from-gray-800/60 to-gray-900/60 border border-gray-700/50 shadow" data-v-180bba7d><div class="flex items-center text-xs text-gray-400" data-v-180bba7d><div class="w-4 h-4 bg-gray-700/50 rounded-full animate-pulse mr-2" data-v-180bba7d></div><div class="h-4 w-20 bg-gray-700/50 rounded animate-pulse mr-2" data-v-180bba7d></div><div class="h-4 w-12 bg-gray-700/50 rounded animate-pulse" data-v-180bba7d></div></div><div class="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700/30 animate-pulse" data-v-180bba7d></div></div><div class="mt-6 grid grid-cols-3 gap-3" data-v-180bba7d><div class="p-3 rounded-lg bg-gradient-to-br from-green-600/20 to-green-800/20 border border-green-500/30 text-center" data-v-180bba7d><div class="h-6 w-16 bg-gray-700/50 rounded animate-pulse mx-auto mb-1" data-v-180bba7d></div><div class="h-4 w-20 bg-gray-700/50 rounded animate-pulse mx-auto" data-v-180bba7d></div></div><div class="p-3 rounded-lg bg-gradient-to-br from-gray-700/20 to-gray-800/20 border border-gray-600/30 text-center" data-v-180bba7d><div class="h-6 w-16 bg-gray-700/50 rounded animate-pulse mx-auto mb-1" data-v-180bba7d></div><div class="h-4 w-20 bg-gray-700/50 rounded animate-pulse mx-auto" data-v-180bba7d></div></div><div class="p-3 rounded-lg bg-[rgba(239,68,68,0.12)] border border-red-500/30 text-center" data-v-180bba7d><div class="h-6 w-16 bg-gray-700/50 rounded animate-pulse mx-auto mb-1" data-v-180bba7d></div><div class="h-4 w-20 bg-gray-700/50 rounded animate-pulse mx-auto" data-v-180bba7d></div></div></div><div class="mt-6" data-v-180bba7d><div class="h-6 w-32 bg-gray-700/50 rounded animate-pulse mb-3" data-v-180bba7d></div><div class="p-4 rounded-lg bg-gray-800/30 border border-gray-700/50" data-v-180bba7d><div class="space-y-2" data-v-180bba7d><div class="h-4 w-full bg-gray-700/50 rounded animate-pulse" data-v-180bba7d></div><div class="h-4 w-3/4 bg-gray-700/50 rounded animate-pulse" data-v-180bba7d></div><div class="h-4 w-5/6 bg-gray-700/50 rounded animate-pulse" data-v-180bba7d></div></div></div></div>',4)),k("div",Og,[t[0]||(t[0]=k("div",{class:"loading-dots flex space-x-2 justify-center"},[k("div",{class:"w-2 h-2 bg-blue-400 rounded-full animate-bounce",style:{"animation-delay":"0ms"}}),k("div",{class:"w-2 h-2 bg-blue-400 rounded-full animate-bounce",style:{"animation-delay":"150ms"}}),k("div",{class:"w-2 h-2 bg-blue-400 rounded-full animate-bounce",style:{"animation-delay":"300ms"}})],-1)),k("p",Mg,S(A.loadingText),1)])]))}),[["__scopeId","data-v-180bba7d"]]),Ng={class:"market-tab-selector"},Vg={class:"flex bg-gray-800/30 rounded-lg p-1 border border-gray-700/50"},Gg=["onClick","disabled"],Jg={key:0,class:"absolute -top-1 -right-1 bg-orange-500 text-white text-xs px-1 rounded-full",style:{"font-size":"10px","line-height":"1.2"}},Xg=Rg(y({__name:"MarketTabSelector",props:{modelValue:{}},emits:["update:modelValue","change"],setup(A,{emit:e}){const{t:t}=bu(),r=e,n=[{value:"crypto",label:"market.crypto",icon:"ri-currency-line"},{value:"stock",label:"market.stock",icon:"ri-line-chart-line"},{value:"china",label:"market.china",icon:"ri-bank-line",disabled:!0,comingSoon:!0}];return(A,e)=>(K(),H("div",Ng,[k("div",Vg,[(K(),H(U,null,P(n,(e=>k("button",{key:e.value,onClick:A=>(A=>{A.disabled||(r("update:modelValue",A.value),r("change",A.value))})(e),disabled:e.disabled,class:O(["flex-1 flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 relative",{"bg-blue-600 text-white shadow-sm":A.modelValue===e.value&&!e.disabled,"text-gray-300 hover:text-white hover:bg-gray-700/50":A.modelValue!==e.value&&!e.disabled,"text-gray-500 cursor-not-allowed":e.disabled}])},[k("i",{class:O([e.icon,"mr-1.5 text-sm"])},null,2),k("span",null,S(M(t)(e.label)),1),e.comingSoon?(K(),H("div",Jg,S(M(t)("common.coming_soon")),1)):I("",!0)],10,Gg))),64))])]))}}),[["__scopeId","data-v-64afbdca"]]),jg={class:"market-type-selector"},Wg={class:"flex items-center space-x-1 bg-gray-800/50 rounded-lg p-1"},Yg=["disabled","onClick"],Zg={class:"flex items-center justify-center"},zg={key:0,class:"absolute -top-1 -right-1 bg-orange-500 text-white text-[10px] px-1 rounded-full leading-none",style:{"font-size":"8px",padding:"1px 3px"}},qg=Rg(y({__name:"MarketTypeSelector",props:{modelValue:{}},emits:["update:modelValue","change"],setup(A,{emit:e}){const{t:t}=bu(),r=A,n=e,s=f(r.modelValue),o=[{value:"crypto",label:"market.crypto",icon:"ri-currency-line"},{value:"stock",label:"market.stock",icon:"ri-line-chart-line"},{value:"china",label:"market.china",icon:"ri-bank-line",disabled:!0,comingSoon:!0}];return m((()=>r.modelValue),(A=>{s.value=A})),(A,e)=>(K(),H("div",jg,[k("div",Wg,[(K(),H(U,null,P(o,(A=>k("button",{key:A.value,class:O(["flex-1 px-2 py-2 rounded-md text-xs font-medium transition-all duration-200 relative",s.value!==A.value||A.disabled?A.disabled?"text-gray-500 cursor-not-allowed bg-gray-800/30":"text-gray-300 hover:text-white hover:bg-gray-700/50":"bg-blue-600 text-white shadow-lg"]),disabled:A.disabled,onClick:e=>(A=>{"china"!==A&&s.value!==A&&(s.value=A,n("update:modelValue",A),n("change",A))})(A.value)},[k("div",Zg,[k("i",{class:O([A.icon,"mr-1"])},null,2),T(" "+S(M(t)(A.label)),1)]),A.comingSoon?(K(),H("div",zg,S(M(t)("common.coming_soon")),1)):I("",!0)],10,Yg))),64))])]))}}),[["__scopeId","data-v-fc104f8f"]]),$g={class:"bg-gray-900 rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[80vh] flex flex-col"},Ap={class:"flex items-center justify-between p-4 border-b border-gray-700"},ep={class:"text-lg font-semibold text-white"},tp={class:"p-4 border-b border-gray-700"},rp={class:"relative"},np=["placeholder"],sp={class:"p-4 border-b border-gray-700"},op={class:"flex-1 overflow-y-auto"},ap={key:0,class:"p-4 text-center"},ip={class:"text-gray-400 mt-2"},lp={key:1,class:"p-4 text-center"},cp={class:"text-gray-400"},up={key:2,class:"divide-y divide-gray-700"},dp=["onClick"],Bp={class:"flex items-center space-x-3"},gp={class:"w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center"},pp={class:"font-medium text-white"},fp={class:"text-sm text-gray-400"},wp={class:"text-xs text-gray-500 capitalize"},hp={key:3,class:"p-4"},mp={class:"text-sm font-medium text-gray-300 mb-3"},Qp={class:"space-y-2"},yp=["onClick"],Cp={class:"flex items-center space-x-3"},Up={class:"w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center"},vp={class:"font-medium text-white text-sm"},Fp={class:"text-xs text-gray-400"},bp=Rg(y({__name:"AssetSearchModal",props:{visible:{type:Boolean}},emits:["close","select"],setup(A,{emit:e}){const{t:t}=bu(),r=A,n=e,s=f(""),o=f("crypto"),a=f([]),i=f(!1),l=f(),c=[{symbol:"BTCUSDT",name:"Bitcoin",market_type:"crypto"},{symbol:"ETHUSDT",name:"Ethereum",market_type:"crypto"},{symbol:"SOLUSDT",name:"Solana",market_type:"crypto"},{symbol:"BNBUSDT",name:"BNB",market_type:"crypto"},{symbol:"ADAUSDT",name:"Cardano",market_type:"crypto"},{symbol:"XRPUSDT",name:"XRP",market_type:"crypto"},{symbol:"DOGEUSDT",name:"Dogecoin",market_type:"crypto"},{symbol:"AVAXUSDT",name:"Avalanche",market_type:"crypto"}],d=[{symbol:"AAPL",name:"Apple Inc.",market_type:"stock",sector:"Technology"},{symbol:"MSFT",name:"Microsoft Corporation",market_type:"stock",sector:"Technology"},{symbol:"GOOGL",name:"Alphabet Inc.",market_type:"stock",sector:"Technology"},{symbol:"AMZN",name:"Amazon.com Inc.",market_type:"stock",sector:"Consumer Discretionary"},{symbol:"TSLA",name:"Tesla Inc.",market_type:"stock",sector:"Consumer Discretionary"},{symbol:"META",name:"Meta Platforms Inc.",market_type:"stock",sector:"Technology"},{symbol:"NVDA",name:"NVIDIA Corporation",market_type:"stock",sector:"Technology"},{symbol:"NFLX",name:"Netflix Inc.",market_type:"stock",sector:"Communication Services"}],B=h((()=>"crypto"===o.value?c:d)),g=()=>{n("close"),s.value="",a.value=[]},p=A=>{n("select",A),g()},w=A=>{o.value=A,s.value&&y()};let Q;const y=()=>{clearTimeout(Q),Q=setTimeout((()=>u(this,null,(function*(){if(s.value.trim()){i.value=!0;try{yield C(s.value)}catch(A){a.value=[]}finally{i.value=!1}}else a.value=[]}))),300)},C=A=>u(this,null,(function*(){try{const e="all"===o.value?void 0:o.value,t=yield hg(A,e,10);"success"===t.status?a.value=t.data.map((A=>({symbol:A.symbol,name:A.name,market_type:A.market_type,exchange:A.exchange,sector:A.sector}))):a.value=[]}catch(e){a.value=[]}}));return m((()=>r.visible),(A=>{A?(document.body.style.overflow="hidden",J((()=>{var A;null==(A=l.value)||A.focus()}))):document.body.style.overflow=""})),(A,e)=>A.visible?(K(),H("div",{key:0,class:"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm overflow-hidden",onClick:N(g,["self"])},[k("div",$g,[k("div",Ap,[k("h3",ep,S(M(t)("search.title")),1),k("button",{onClick:g,class:"text-gray-400 hover:text-white transition-colors"},e[2]||(e[2]=[k("i",{class:"ri-close-line text-xl"},null,-1)]))]),k("div",tp,[k("div",rp,[e[3]||(e[3]=k("i",{class:"ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"},null,-1)),V(k("input",{"onUpdate:modelValue":e[0]||(e[0]=A=>s.value=A),type:"text",placeholder:M(t)("search.placeholder"),class:"w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500",onInput:y,ref_key:"searchInput",ref:l},null,40,np),[[G,s.value]])])]),k("div",sp,[x(qg,{modelValue:o.value,"onUpdate:modelValue":e[1]||(e[1]=A=>o.value=A),onChange:w},null,8,["modelValue"])]),k("div",op,[i.value?(K(),H("div",ap,[e[4]||(e[4]=k("i",{class:"ri-loader-4-line animate-spin text-2xl text-blue-500"},null,-1)),k("p",ip,S(M(t)("search.searching")),1)])):s.value&&0===a.value.length?(K(),H("div",lp,[e[5]||(e[5]=k("i",{class:"ri-search-line text-3xl text-gray-500 mb-2"},null,-1)),k("p",cp,S(M(t)("search.no_results")),1)])):a.value.length>0?(K(),H("div",up,[(K(!0),H(U,null,P(a.value,(A=>(K(),H("button",{key:`${A.symbol}-${A.market_type}`,onClick:e=>p(A),class:"w-full p-4 text-left hover:bg-gray-800 transition-colors flex items-center justify-between"},[k("div",Bp,[k("div",gp,[k("i",{class:O(["crypto"===A.market_type?"ri-currency-line":"ri-line-chart-line","text-white text-sm"])},null,2)]),k("div",null,[k("div",pp,S(A.symbol),1),k("div",fp,S(A.name),1)])]),k("div",wp,S(A.market_type),1)],8,dp)))),128))])):(K(),H("div",hp,[k("h4",mp,S(M(t)("search.popular")),1),k("div",Qp,[(K(!0),H(U,null,P(B.value,(A=>(K(),H("button",{key:`${A.symbol}-${A.market_type}`,onClick:e=>p(A),class:"w-full p-3 text-left hover:bg-gray-800 rounded-lg transition-colors flex items-center justify-between"},[k("div",Cp,[k("div",Up,[k("i",{class:O(["crypto"===A.market_type?"ri-currency-line":"ri-line-chart-line","text-white text-xs"])},null,2)]),k("div",null,[k("div",vp,S(A.symbol),1),k("div",Fp,S(A.name),1)])])],8,yp)))),128))])]))])])])):I("",!0)}}),[["__scopeId","data-v-f2672b4a"]]),_p=["disabled","title"],xp=y({__name:"FavoriteButton",props:{symbol:{},marketType:{},name:{}},emits:["favoriteChanged"],setup(A,{emit:e}){const{t:t}=bu(),r=A,n=e,s=f(!1),o=f(!1),a=h((()=>o.value)),i=()=>u(this,null,(function*(){if(!s.value){if(s.value=!0,c())try{const A={symbol:r.symbol,name:r.name||r.symbol,market_type:r.marketType,exchange:r.exchange,sector:r.sector};if(o.value)"success"===(yield yg(r.symbol,r.marketType)).status&&(o.value=!1,n("favoriteChanged",!1));else{const e=yield Qg(A);"success"!==e.status&&"info"!==e.status||(o.value=!0,n("favoriteChanged",!0))}}catch(A){yield l(r.symbol,r.marketType,!o.value),o.value=!o.value,n("favoriteChanged",o.value)}else yield l(r.symbol,r.marketType,!o.value),o.value=!o.value,n("favoriteChanged",o.value);s.value=!1}})),l=(A,e,t)=>u(this,null,(function*(){yield new Promise((A=>setTimeout(A,300)));const r=JSON.parse(localStorage.getItem("favorites")||"[]"),n=`${A}-${e}`;if(t)r.includes(n)||r.push(n);else{const A=r.indexOf(n);A>-1&&r.splice(A,1)}localStorage.setItem("favorites",JSON.stringify(r))})),c=()=>"undefined"!=typeof chrome&&chrome.runtime&&chrome.runtime.sendMessage&&chrome.runtime.id,d=()=>u(this,null,(function*(){if(c())try{const A=yield Cg(r.symbol,r.marketType);if("success"===A.status)return void(o.value=A.data.is_favorite)}catch(t){}const A=JSON.parse(localStorage.getItem("favorites")||"[]"),e=`${r.symbol}-${r.marketType}`;o.value=A.includes(e)}));return d(),m((()=>[r.symbol,r.marketType]),(()=>{d()})),(A,e)=>(K(),H("button",{onClick:i,disabled:s.value,class:O(["flex items-center justify-center w-8 h-8 rounded-full transition-all duration-200",a.value?"bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30":"bg-gray-700/50 text-gray-400 hover:bg-gray-600/50 hover:text-gray-300",s.value?"opacity-50 cursor-not-allowed":"cursor-pointer"]),title:a.value?M(t)("favorites.remove"):M(t)("favorites.add")},[k("i",{class:O([s.value?"ri-loader-4-line animate-spin":a.value?"ri-star-fill":"ri-star-line","text-sm"])},null,2)],10,_p))}}),Ep=Rg(xp,[["__scopeId","data-v-d38971cd"]]),Hp={key:0,class:"fixed inset-0 z-50 flex items-center justify-center overflow-hidden"},Ip={class:"relative w-[90vw] max-w-md mx-4 bg-gray-900 rounded-xl border border-gray-700 shadow-2xl"},kp={class:"flex items-center justify-between p-4 border-b border-gray-700"},Lp={class:"text-lg font-semibold text-white flex items-center"},Sp={class:"p-4"},Kp={key:0,class:"text-center py-8"},Dp={class:"text-gray-400 mt-2"},Tp={key:1,class:"text-center py-8"},Op={class:"text-gray-400"},Mp={key:2,class:"space-y-2 max-h-80 overflow-y-auto"},Rp=["onClick"],Pp={class:"flex items-center space-x-2"},Np={class:"font-medium text-white"},Vp={class:"text-xs text-gray-500 px-2 py-1 rounded bg-gray-700/50"},Gp=["onClick","title"],Jp=Rg(y({__name:"FavoritesModal",props:{visible:{type:Boolean}},emits:["close","select","favorite-removed"],setup(A,{emit:e}){const{t:t}=iu(),r=A,n=e,s=f(!1),o=f([]),a=A=>{switch(A){case"crypto":return"ri-currency-line";case"stock":return"ri-line-chart-line";case"china":return"ri-bank-line";default:return"ri-question-line"}},i=A=>{switch(A){case"crypto":return t("market.crypto");case"stock":return t("market.stock");case"china":return t("market.china");default:return A}},l=()=>u(this,null,(function*(){s.value=!0;try{const A=yield mg();o.value=A.data||[]}catch(A){o.value=[]}finally{s.value=!1}})),c=A=>u(this,null,(function*(){try{yield yg(A.symbol,A.market_type),o.value=o.value.filter((e=>e.id!==A.id)),n("favorite-removed",A)}catch(e){}}));return m((()=>r.visible),(A=>{if(A){const A=window.scrollY;document.body.style.position="fixed",document.body.style.top=`-${A}px`,document.body.style.width="100%",document.body.style.overflow="hidden",l()}else{const A=document.body.style.top;document.body.style.position="",document.body.style.top="",document.body.style.width="",document.body.style.overflow="",A&&window.scrollTo(0,-1*parseInt(A||"0"))}}),{immediate:!0}),(A,e)=>A.visible?(K(),H("div",Hp,[k("div",{class:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:e[0]||(e[0]=e=>A.$emit("close"))}),k("div",Ip,[k("div",kp,[k("h3",Lp,[e[2]||(e[2]=k("i",{class:"ri-bookmark-line mr-2 text-yellow-400"},null,-1)),T(" "+S(M(t)("common.my_favorites")),1)]),k("button",{onClick:e[1]||(e[1]=e=>A.$emit("close")),class:"text-gray-400 hover:text-white transition-colors"},e[3]||(e[3]=[k("i",{class:"ri-close-line text-xl"},null,-1)]))]),k("div",Sp,[s.value?(K(),H("div",Kp,[e[4]||(e[4]=k("i",{class:"ri-loader-4-line text-2xl text-blue-400 animate-spin"},null,-1)),k("p",Dp,S(M(t)("common.loading")),1)])):0===o.value.length?(K(),H("div",Tp,[e[5]||(e[5]=k("i",{class:"ri-bookmark-line text-4xl text-gray-600 mb-3"},null,-1)),k("p",Op,S(M(t)("favorites.empty")),1)])):(K(),H("div",Mp,[(K(!0),H(U,null,P(o.value,(A=>(K(),H("div",{key:`${A.symbol}-${A.market_type}`,class:"flex items-center justify-between p-3 rounded-lg bg-gray-800/50 border border-gray-700/50 hover:bg-gray-800/70 transition-colors"},[k("div",{class:"flex items-center flex-1 cursor-pointer",onClick:e=>(A=>{n("select",A),n("close")})(A)},[k("div",Pp,[k("i",{class:O([a(A.market_type),"text-gray-400"])},null,2),k("span",Np,S(A.symbol),1),k("span",Vp,S(i(A.market_type)),1)])],8,Rp),k("button",{onClick:e=>c(A),class:"ml-3 p-1 text-gray-400 hover:text-red-400 transition-colors",title:M(t)("favorites.remove")},e[6]||(e[6]=[k("i",{class:"ri-bookmark-fill text-yellow-400 hover:text-gray-400"},null,-1)]),8,Gp)])))),128))]))])])])):I("",!0)}}),[["__scopeId","data-v-a5d49fca"]]);function Xp(A,e){return u(this,null,(function*(){const t={q:A,target:e,format:"text"},r=yield fetch("https://translation.googleapis.com/language/translate/v2?key=AIzaSyCJod07SIklxTIawm8qqm1dbaYVtgr9VjM",{method:"POST",body:JSON.stringify(t),headers:{"Content-Type":"application/json"}}),n=yield r.json();return n&&n.data&&n.data.translations&&n.data.translations[0]?n.data.translations[0].translatedText:A}))}const jp={class:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900"},Wp={class:"max-w-md mx-auto bg-slate-900/95 backdrop-blur-sm min-h-screen shadow-2xl flex flex-col"},Yp={class:"flex-shrink-0 bg-slate-900/98 backdrop-blur-md border-b border-slate-700/50"},Zp={class:"p-4 pb-3"},zp={key:0,class:"px-4 pb-4"},qp={class:"flex items-center justify-between"},$p={class:"flex items-center space-x-3"},Af={class:"text-2xl font-bold text-white tracking-tight"},ef={class:"flex items-center space-x-2"},tf=["title"],rf=["title"],nf={key:0,class:"relative"},sf=["title"],of={class:"p-4"},af={class:"text-sm font-semibold text-green-400 mb-3 flex items-center"},lf={class:"grid grid-cols-4 gap-2"},cf=["onClick","disabled"],uf={class:"text-xs font-bold text-center"},df={key:0,class:"absolute -top-1 -right-1 w-2.5 h-2.5 bg-blue-400 rounded-full border border-slate-800"},Bf={key:0,class:"flex-1 flex items-center justify-center px-4"},gf={class:"text-center space-y-6"},pf={class:"inline-flex items-center px-4 py-2 rounded-full bg-orange-500/20 border border-orange-500/40"},ff={class:"text-orange-400 text-sm font-medium"},wf={key:1,class:"flex-1 overflow-y-auto"},hf={key:0,class:"p-6"},mf={key:1,class:"p-4 space-y-6 pb-24"},Qf={class:"p-6 rounded-2xl bg-gradient-to-br from-slate-800/80 to-slate-900/80 border border-slate-700/50 shadow-xl"},yf={class:"text-center space-y-4"},Cf={class:"space-y-1"},Uf={class:"text-sm font-medium text-slate-400 uppercase tracking-wider"},vf={class:"text-4xl font-bold text-white"},Ff={class:"flex justify-center gap-3 pt-2"},bf={class:"text-sm font-medium"},_f={class:"text-sm font-medium"},xf={class:"flex items-center justify-between p-4 rounded-xl bg-slate-800/40 border border-slate-700/50"},Ef={class:"flex items-center space-x-2 text-sm text-slate-400"},Hf=["disabled"],If={key:0},kf={class:"text-lg font-bold text-white mb-4"},Lf={class:"grid grid-cols-3 gap-4"},Sf={class:"p-4 rounded-xl bg-gradient-to-br from-emerald-500/15 to-emerald-600/10 border border-emerald-500/30 text-center space-y-2"},Kf={class:"text-2xl font-bold text-emerald-400"},Df={class:"text-xs text-emerald-300 font-medium"},Tf={class:"p-4 rounded-xl bg-gradient-to-br from-slate-500/15 to-slate-600/10 border border-slate-500/30 text-center space-y-2"},Of={class:"text-2xl font-bold text-slate-300"},Mf={class:"text-xs text-slate-400 font-medium"},Rf={class:"p-4 rounded-xl bg-gradient-to-br from-red-500/15 to-red-600/10 border border-red-500/30 text-center space-y-2"},Pf={class:"text-2xl font-bold text-red-400"},Nf={class:"text-xs text-red-300 font-medium"},Vf={key:1},Gf={class:"text-lg font-bold text-white mb-4"},Jf={class:"p-5 rounded-xl bg-gradient-to-br from-slate-800/60 to-slate-900/60 border border-slate-700/50"},Xf={class:"flex items-start space-x-3"},jf={class:"flex-1"},Wf={class:"text-slate-200 leading-relaxed"},Yf={key:0,class:"text-slate-400"},Zf={key:1},zf={key:2},qf={class:"text-lg font-bold text-white mb-4"},$f={class:"grid grid-cols-2 gap-3 mb-4"},Aw={key:0,class:"p-4 rounded-xl bg-slate-800/40 border border-slate-700/50"},ew={class:"flex items-center justify-between mb-2"},tw={class:"flex items-center space-x-2"},rw={class:"text-sm font-medium text-slate-300"},nw={class:"text-lg font-bold text-white"},sw={class:"space-y-4"},ow={key:0,class:"p-5 rounded-xl bg-slate-800/40 border border-slate-700/50"},aw={class:"flex items-center justify-between mb-4"},iw={class:"flex items-center space-x-2"},lw={class:"text-lg font-bold text-white"},cw={class:"grid grid-cols-3 gap-3"},uw={class:"text-center p-3 rounded-lg bg-blue-500/10 border border-blue-500/30"},dw={class:"text-sm font-bold text-white"},Bw={class:"text-center p-3 rounded-lg bg-blue-500/10 border border-blue-500/30"},gw={class:"text-sm font-bold text-white"},pw={class:"text-center p-3 rounded-lg bg-blue-500/10 border border-blue-500/30"},fw={class:"text-sm font-bold text-white"},ww={class:"text-center p-3 rounded-lg bg-red-500/10 border border-red-500/30"},hw={class:"text-sm font-bold text-white"},mw={class:"text-center p-3 rounded-lg bg-slate-500/10 border border-slate-500/30"},Qw={class:"text-sm font-bold text-white"},yw={class:"text-center p-3 rounded-lg bg-emerald-500/10 border border-emerald-500/30"},Cw={class:"text-sm font-bold text-white"},Uw={class:"text-center p-3 rounded-lg bg-emerald-500/10 border border-emerald-500/30"},vw={class:"text-sm font-bold text-white"},Fw={class:"text-center p-3 rounded-lg bg-red-500/10 border border-red-500/30"},bw={class:"text-sm font-bold text-white"},_w={class:"text-center p-3 rounded-lg bg-blue-500/10 border border-blue-500/30"},xw={class:"text-sm font-bold text-white"},Ew={key:3},Hw={class:"text-lg font-bold text-white mb-4"},Iw={class:"p-5 rounded-xl bg-gradient-to-br from-slate-800/60 to-slate-900/60 border border-slate-700/50 space-y-4"},kw={class:"flex items-center justify-between p-3 rounded-lg bg-slate-700/30"},Lw={class:"text-sm font-medium text-slate-300"},Sw={class:"grid grid-cols-3 gap-3"},Kw={class:"text-center p-3 rounded-lg bg-blue-500/10 border border-blue-500/30"},Dw={class:"text-xs text-blue-300 font-medium mb-1"},Tw={class:"text-sm font-bold text-white"},Ow={class:"text-center p-3 rounded-lg bg-red-500/10 border border-red-500/30"},Mw={class:"text-xs text-red-300 font-medium mb-1"},Rw={class:"text-sm font-bold text-red-400"},Pw={class:"text-center p-3 rounded-lg bg-emerald-500/10 border border-emerald-500/30"},Nw={class:"text-xs text-emerald-300 font-medium mb-1"},Vw={class:"text-sm font-bold text-emerald-400"},Gw={class:"p-4 rounded-lg bg-slate-700/20 border border-slate-600/30"},Jw={class:"flex items-start space-x-3"},Xw={class:"flex-1"},jw={class:"text-sm font-medium text-slate-300 mb-2"},Ww={class:"text-sm text-slate-200 leading-relaxed"},Yw={key:0,class:"text-slate-400"},Zw={key:1},zw={key:4},qw={class:"text-lg font-bold text-white mb-4"},$w={class:"p-5 rounded-xl bg-gradient-to-br from-slate-800/60 to-slate-900/60 border border-slate-700/50 space-y-4"},Ah={class:"flex items-center justify-between p-3 rounded-lg bg-slate-700/30"},eh={class:"text-sm font-medium text-slate-300"},th={class:"space-y-2"},rh={class:"flex items-center justify-between"},nh={class:"text-sm font-medium text-slate-300"},sh={class:"text-lg font-bold text-white"},oh={class:"w-full bg-slate-700/50 rounded-full h-3 overflow-hidden"},ah={key:0,class:"space-y-3"},ih={class:"flex items-center space-x-2"},lh={class:"text-sm font-medium text-slate-300"},ch={class:"pl-8 space-y-2"},uh={key:0,class:"text-slate-400 text-sm"},dh={key:1,class:"space-y-2"},Bh={key:2,class:"flex items-center justify-center min-h-[400px] p-4"},gh={key:3,class:"flex items-center justify-center min-h-[400px] p-4"},ph={class:"text-center px-6 py-8"},fh={class:"text-slate-300 mb-2"},wh={class:"text-slate-400 text-sm mb-6"},hh={key:4,class:"flex items-center justify-center min-h-[400px] p-4"},mh={class:"text-center px-6 py-8"},Qh={class:"text-slate-300 mb-6"},yh={class:"sticky bottom-0 bg-slate-900/95 backdrop-blur-md border-t border-slate-700/50"},Ch={class:"grid grid-cols-3 h-16"},Uh={class:"text-xs mt-1 font-medium"},vh={class:"text-xs mt-1"},Fh={class:"text-xs mt-1"},bh=y({__name:"HomeView",setup(A){const{t:e}=bu(),{t:t}=iu(),r=f(null),n=f(!1),s=f(!1),o=f(null),a=f("BTCUSDT"),i=f(!1),l=f("crypto"),c=f(!1),d=f(!1),B=f(!1),g=f([{symbol:"BTCUSDT",display:"BTC"},{symbol:"ETHUSDT",display:"ETH"},{symbol:"SOLUSDT",display:"SOL"},{symbol:"BNBUSDT",display:"BNB"},{symbol:"ADAUSDT",display:"ADA"},{symbol:"XRPUSDT",display:"XRP"},{symbol:"DOGEUSDT",display:"DOGE"},{symbol:"AVAXUSDT",display:"AVAX"}]),p=f([{symbol:"AAPL",display:"AAPL"},{symbol:"MSFT",display:"MSFT"},{symbol:"GOOGL",display:"GOOGL"},{symbol:"AMZN",display:"AMZN"},{symbol:"TSLA",display:"TSLA"},{symbol:"META",display:"META"},{symbol:"NVDA",display:"NVDA"},{symbol:"NFLX",display:"NFLX"}]),w=h((()=>"crypto"===l.value?g.value:"stock"===l.value?p.value:[])),Q=f(!0);m(r,(A=>{A&&(Q.value=!1)})),m([i,o],(([A,e])=>{(A||e)&&(Q.value=!1)}));const y=A=>{"china"!==A&&(l.value=A,v("crypto"===A?"BTCUSDT":"AAPL",A))},C=A=>{v(A.symbol,A.market_type)},v=(A,e)=>u(this,null,(function*(){A===a.value&&e===l.value||(r.value=null,o.value=null,i.value=!1,Q.value=!0,l.value=e,a.value=A,yield oA(!0,!1))})),F=A=>{},E=A=>{v(A.symbol,A.market_type)},L=A=>{},V=()=>u(this,null,(function*(){try{if("undefined"!=typeof chrome&&chrome.tabs&&chrome.tabs.query&&chrome.tabs.sendMessage)try{const A=(yield chrome.tabs.query({active:!0,currentWindow:!0}))[0];if(A&&"number"==typeof A.id){const e=yield new Promise((e=>{chrome.tabs.sendMessage(A.id,{type:"GET_SYMBOL_FROM_CONTENT"},(A=>{chrome.runtime.lastError?e({}):e(A||{})}))}));if(e&&e.symbol&&"string"==typeof e.symbol)return e.symbol;if(A.url){const e=vg(A.url);if(e&&"string"==typeof e)return e}}}catch(A){}if("undefined"!=typeof chrome&&chrome.runtime)try{const A=yield new Promise((A=>{chrome.runtime.sendMessage({type:"GET_CURRENT_SYMBOL"},(e=>{chrome.runtime.lastError?A({}):A(e||{})}))}));if(A&&A.symbol&&"string"==typeof A.symbol)return A.symbol}catch(A){}const e=vg(window.location.href);return e&&"string"==typeof e?e:"BTCUSDT"}catch(e){return"BTCUSDT"}})),G=A=>{if(null==A)return"--";if("string"==typeof A&&(A=parseFloat(A),isNaN(A)))return A||"--";const e=Number(A);return isNaN(e)?"--":e<1e-4?e<1e-8?e.toExponential(8):e.toFixed(8):e<1?e.toFixed(6):e.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})},Y=A=>{if(!A)return"--";try{const t=new Date(A);if(isNaN(t.getTime()))return e("common.error");const r=new Date,n=Math.floor((r.getTime()-t.getTime())/1e3/60);return n<60?e("analysis.minute_ago",{n:n}):n<1440?e("analysis.hour_ago",{n:Math.floor(n/60)}):e("analysis.day_ago",{n:Math.floor(n/1440)})}catch(t){return e("common.error")}},Z=A=>A&&"string"==typeof A?A.replace("USDT",""):"BTC",z=()=>a.value?"crypto"===l.value?Z(a.value):(l.value,a.value):e("common.loading"),q=A=>u(this,null,(function*(){B.value=!1,yield v(A,l.value)})),$=A=>{const e=A.target.closest(".relative");e&&e.querySelector(".ri-fire-line")||(B.value=!1)};let tA=null,rA=null,nA=null;const sA=(A=!0,e=!1)=>u(this,null,(function*(){return a.value&&"string"==typeof a.value&&a.value.trim()?(tA&&(nA&&nA.abort(),tA=null),nA=new AbortController,o.value=null,i.value=!1,A&&(n.value=!0,s.value=!0),tA=dg(a.value,e).then((A=>{if(A&&"not_found"!==A.status){const e=zB(A);r.value=e,i.value=!1,o.value=null}else i.value=!0,r.value=null,o.value=null;return A})).catch((A=>(o.value=A instanceof Error?A.message:"加载数据失败",r.value=null,i.value=!1,null))).finally((()=>{n.value=!1,s.value=!1,tA=null})),tA):(o.value="无法获取当前交易对信息",void eA.error("交易对无效，无法加载数据"))})),oA=(A=!0,e=!0,t=!1)=>u(this,null,(function*(){try{return e?(rA&&clearTimeout(rA),new Promise(((e,r)=>{rA=setTimeout((()=>u(this,null,(function*(){try{const r=yield sA(A,t);e(r)}catch(n){r(n)}}))),300)}))):yield sA(A,t)}catch(r){o.value=r instanceof Error?r.message:"加载数据失败",n.value=!1,s.value=!1,tA=null}}));_((()=>{tA=null,rA&&(clearTimeout(rA),rA=null),nA&&(nA.abort(),nA=null)}));const aA=()=>{window.addEventListener("language-changed",(A=>u(this,null,(function*(){var e;const t=(null==(e=A.detail)?void 0:e.language)||localStorage.getItem("language")||"en-US";mA.value=t,a.value&&(yield oA(!1),yield J())})))),window.addEventListener("force-refresh-i18n",(()=>u(this,null,(function*(){if(r.value&&a.value){const A=localStorage.getItem("language")||"en-US";mA.value=A,yield oA(!1),yield J()}}))))},iA=()=>u(this,null,(function*(){try{if("undefined"!=typeof chrome&&chrome.tabs&&chrome.tabs.query&&chrome.tabs.sendMessage){const A=(yield chrome.tabs.query({active:!0,currentWindow:!0}))[0];A&&"number"==typeof A.id&&(chrome.tabs.sendMessage(A.id,{type:"PAGE_UPDATED"},(A=>{chrome.runtime.lastError})),yield new Promise((A=>setTimeout(A,500))))}}catch(A){}}));b((()=>u(this,null,(function*(){var A,e;if(!(()=>{const A=localStorage.getItem("token"),e=localStorage.getItem("userInfo");return A&&e})())return void(window.location.href="/login");n.value=!0,aA(),"undefined"!=typeof chrome&&chrome.runtime&&chrome.runtime.onMessage&&chrome.runtime.onMessage.addListener(((A,e,t)=>{try{if("SYMBOL_UPDATED"===A.type&&A.data&&A.data.symbol){const e=A.data.symbol;e&&"string"==typeof e&&e!==a.value&&(a.value=e)}t({status:"success"})}catch(r){t({status:"error",error:r.message})}return!0})),yield iA();try{const A=yield V();a.value=A&&"string"==typeof A?A:"BTCUSDT",yield oA(!0,!1)}catch(i){o.value=i instanceof Error?i.message:"加载数据失败",n.value=!1}const t=null==(e=null==(A=r.value)?void 0:A.trend_analysis)?void 0:e.summary,s=mA.value;if(t)if("en-US"===s)_A.value=t;else{HA.value=!0;try{_A.value=yield Xp(t,LA[s]||"zh-CN")}catch(i){_A.value=t}HA.value=!1}document.addEventListener("click",$)})))),_((()=>{document.removeEventListener("click",$)})),m(a,((A,e)=>u(this,null,(function*(){A&&"string"==typeof A?A&&"string"==typeof A&&A!==e&&(yield oA(!0,!0)):a.value="BTCUSDT"}))));const lA=A=>{if(null==A)return"--";if("string"==typeof A){const e=A.replace(/[^\d.]/g,"");if(A=parseFloat(e),isNaN(A))return e?`${e}%`:"--"}const e=Number(A);return isNaN(e)?"--":e>1?`${Math.round(e)}%`:`${Math.round(100*e)}%`},cA=A=>A?"bullish"===A||"看涨"===A||"支持当前趋势"===A||"up"===A?"text-green-400":"bearish"===A||"看跌"===A||"不支持当前趋势"===A||"down"===A?"text-red-400":"neutral"===A||"中性"===A?"text-yellow-400":"text-gray-400":"text-gray-400",uA=A=>"bullish"===A||"看涨"===A||"支持当前趋势"===A||"up"===A?"ri-arrow-up-line":"bearish"===A||"看跌"===A||"反对当前趋势"===A||"down"===A?"ri-arrow-down-line":"ri-subtract-line",dA=()=>{var A,e,t,n,s,o,i,l,c,u,d,B,g,p,f,w,h,m,Q,y,C,U,v,F,b;try{const _=(localStorage.getItem("language")||"en-US").toLowerCase(),x=a.value||"CRYPTO",E=G((null==(A=r.value)?void 0:A.current_price)||0);let H=null==(n=null==(t=null==(e=r.value)?void 0:e.trend_analysis)?void 0:t.probabilities)?void 0:n.up,I=null==(i=null==(o=null==(s=r.value)?void 0:s.trend_analysis)?void 0:o.probabilities)?void 0:i.down;H="number"==typeof H&&H>=0&&H<=1?H:.33,I="number"==typeof I&&I>=0&&I<=1?I:.33;const k=(null==(c=null==(l=r.value)?void 0:l.trend_analysis)?void 0:c.summary)||"",L=(null==(d=null==(u=r.value)?void 0:u.trading_advice)?void 0:d.action)||"",S=(null==(g=null==(B=r.value)?void 0:B.trading_advice)||g.reason,G(null==(f=null==(p=r.value)?void 0:p.trading_advice)?void 0:f.entry_price)),K=G(null==(h=null==(w=r.value)?void 0:w.trading_advice)?void 0:h.stop_loss),D=G(null==(Q=null==(m=r.value)?void 0:m.trading_advice)?void 0:Q.take_profit),T=(null==(C=null==(y=r.value)?void 0:y.risk_assessment)?void 0:C.level)||"",O=(null==(v=null==(U=r.value)?void 0:U.risk_assessment)?void 0:v.score)||50,M=(null==(b=null==(F=r.value)?void 0:F.risk_assessment)?void 0:b.details)||[];let R="";R="zh-cn"===_?`${x}市场分析报告 - 当前价格: ${E} USD\n\n市场趋势:\n${k.substring(0,50)}${k.length>50?"...":""}\n\n交易建议:\n操作: ${L}\n入场价: ${S}\n止损价: ${K}\n目标价: ${D}\n\n风险评估:\n风险等级: ${T}\n风险评分: ${O}/100\n${M.length>0?"主要风险因素:\n"+M.slice(0,2).map((A=>`- ${A}`)).join("\n"):""}\n\n#加密货币 #技术分析 #交易建议`:"ja-jp"===_?`${x}市場分析レポート - 現在価格: ${E} USD\n\n市場トレンド:\n${k.substring(0,50)}${k.length>50?"...":""}\n\n取引アドバイス:\nアクション: ${L}\nエントリー価格: ${S}\nストップロス: ${K}\n利益確定: ${D}\n\nリスク評価:\nリスクレベル: ${T}\nリスクスコア: ${O}/100\n${M.length>0?"主なリスク要因:\n"+M.slice(0,2).map((A=>`- ${A}`)).join("\n"):""}\n\n#暗号資産 #テクニカル分析 #取引アドバイス`:"ko-kr"===_?`${x} 시장 분석 리포트 - 현재 가격: ${E} USD\n\n시장 트렌드:\n${k.substring(0,50)}${k.length>50?"...":""}\n\n거래 조언:\n행동: ${L}\n진입 가격: ${S}\n손절가: ${K}\n이익실현가: ${D}\n\n위험 평가:\n위험 수준: ${T}\n위험 점수: ${O}/100\n${M.length>0?"주요 위험 요소:\n"+M.slice(0,2).map((A=>`- ${A}`)).join("\n"):""}\n\n#암호화폐 #기술분석 #거래조언`:`${x} Market Analysis Report - Current Price: ${E} USD\n\nMarket Trend:\n${k.substring(0,50)}${k.length>50?"...":""}\n\nTrading Advice:\nAction: ${L}\nEntry Price: ${S}\nStop Loss: ${K}\nTake Profit: ${D}\n\nRisk Assessment:\nRisk Level: ${T}\nRisk Score: ${O}/100\n${M.length>0?"Main Risk Factors:\n"+M.slice(0,2).map((A=>`- ${A}`)).join("\n"):""}\n\n#crypto #technicalanalysis #tradingadvice`,R.length>270&&(R=R.substring(0,260)+"...");const P=`https://twitter.com/intent/tweet?text=${encodeURIComponent(R)}`;window.open(P,"_blank")}catch(_){}},BA=()=>u(this,null,(function*(){var A,t,n,s,o,i,l,c;try{const u=document.createElement("div");u.style.width="375px",u.style.padding="20px",u.style.backgroundColor="#0F172A",u.style.color="#fff",u.style.fontFamily="system-ui, -apple-system, sans-serif",u.style.position="fixed",u.style.left="-9999px",u.style.top="0",u.style.zIndex="-1";const d=document.createElement("div");d.style.textAlign="center",d.style.marginBottom="20px",d.style.padding="20px 0 10px 0",d.style.background="linear-gradient(to bottom, #1e293b99 60%, #0f172a99 100%)",d.style.borderRadius="16px",d.style.boxShadow="0 2px 8px 0 #0002";const B=a.value||"BTCUSDT",g=Z(B);if(d.innerHTML=`\n      <h2 style="font-size: 22px; margin-bottom: 10px; font-weight: 600; letter-spacing: 1px;">${B} ${e("analysis.market_report",{symbol:g}).replace("analysis.market_report","市场分析报告")}</h2>\n      <div style="font-size: 32px; font-weight: bold; margin-bottom: 4px;">\n        ${G(null==(A=r.value)?void 0:A.current_price)} <span style='font-size:16px;color:#9ca3af'>USD</span>\n      </div>\n    `,u.appendChild(d),null==(n=null==(t=r.value)?void 0:t.trend_analysis)?void 0:n.summary){const A=document.createElement("div");A.style.margin="20px 0 0 0",A.style.padding="16px",A.style.background="rgba(31,41,55,0.3)",A.style.border="1px solid #374151",A.style.borderRadius="12px",A.style.boxShadow="0 1px 4px 0 #0001",A.innerHTML=`\n        <div style="font-size: 16px; font-weight: 500; margin-bottom: 8px;">${e("analysis.market_trend_analysis").replace("analysis.market_trend_analysis","市场趋势分析")}</div>\n        <div style="font-size: 14px; color: #d1d5db; line-height: 1.6; margin-bottom: 12px;">${r.value.trend_analysis.summary}</div>\n        <div style="display: flex; justify-content: center; gap: 8px;">\n          <div style="flex:1; text-align:center; background:rgba(16,185,129,0.12); border-radius:8px; padding:8px 0; border:1px solid #10b98133;">\n            <div style="color:#4ade80; font-size:18px; font-weight:600;">${lA(r.value.trend_analysis.probabilities.up)}</div>\n            <div style="color:#4ade80; font-size:12px;">${e("analysis.uptrend").replace("analysis.uptrend","上涨")}</div>\n          </div>\n          <div style="flex:1; text-align:center; background:rgba(156,163,175,0.12); border-radius:8px; padding:8px 0; border:1px solid #9ca3af33;">\n            <div style="color:#9ca3af; font-size:18px; font-weight:600;">${lA(r.value.trend_analysis.probabilities.sideways)}</div>\n            <div style="color:#9ca3af; font-size:12px;">${e("analysis.sideways").replace("analysis.sideways","盘整")}</div>\n          </div>\n          <div style="flex:1; text-align:center; background:rgba(239,68,68,0.12); border-radius:8px; padding:8px 0; border:1px solid #ef444433;">\n            <div style="color:#ef4444; font-size:18px; font-weight:600;">${lA(r.value.trend_analysis.probabilities.down)}</div>\n            <div style="color:#ef4444; font-size:12px;">${e("analysis.downtrend").replace("analysis.downtrend","下跌")}</div>\n          </div>\n        </div>\n      `,u.appendChild(A)}if(null==(s=r.value)?void 0:s.indicators_analysis){const A=document.createElement("div");A.style.margin="20px 0 0 0",A.style.padding="16px",A.style.background="rgba(31,41,55,0.3)",A.style.border="1px solid #374151",A.style.borderRadius="12px",A.style.boxShadow="0 1px 4px 0 #0001",A.innerHTML=`\n        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">${e("analysis.technical_indicators").replace("analysis.technical_indicators","技术指标")}</div>\n        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">\n          ${Object.entries(r.value.indicators_analysis).filter((([A])=>!["MACD","BollingerBands","DMI"].includes(A))).map((([A,e])=>`\n              <div style="padding: 10px; background: rgba(17,24,39,0.5); border: 1px solid #334155; border-radius: 8px;">\n                <div style="font-size: 12px; color: #9ca3af; margin-bottom: 5px;">${A}</div>\n                <div style="display: flex; justify-content: space-between; align-items: center;">\n                  <span style="font-size: 14px;">${"number"==typeof e.value?e.value.toFixed(2):e.value}</span>\n                  <span style="font-size: 12px;">${(A=>{const e="display:inline-flex;align-items:center;justify-content:center;width:20px;height:20px;border-radius:50%;",t="font-size:14px;line-height:1;height:14px;vertical-align:middle;display:block;margin-top:-14px;";return"bullish"===A||"看涨"===A||"支持当前趋势"===A?`<span style="${e}background:rgba(16,185,129,0.12);">\n      <i class='ri-arrow-up-line' style='${t}color:#22c55e;'></i>\n    </span>`:"bearish"===A||"看跌"===A||"反对当前趋势"===A?`<span style="${e}background:rgba(239,68,68,0.12);">\n      <i class='ri-arrow-down-line' style='${t}color:#ef4444;'></i>\n    </span>`:`<span style="${e}background:rgba(156,163,175,0.12);">\n    <i class='ri-subtract-line' style='${t}color:#9ca3af;'></i>\n  </span>`})(e.support_trend)}</span>\n                </div>\n              </div>\n            `)).join("")}\n        </div>\n      `,u.appendChild(A)}if(null==(o=r.value)?void 0:o.trading_advice){const A=r.value.trading_advice,e=document.createElement("div");e.style.margin="20px 0 0 0",e.style.padding="16px",e.style.background="rgba(31,41,55,0.3)",e.style.border="1px solid #374151",e.style.borderRadius="12px",e.style.boxShadow="0 1px 4px 0 #0001",e.innerHTML=`\n        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Trading Advice</div>\n        <div style="display: flex; flex-direction: column; gap: 6px; font-size: 14px;">\n          <div><span style='color:#9ca3af'>Action:</span> <span style='font-weight:500;'>${A.action}</span></div>\n          <div><span style='color:#9ca3af'>Entry Price:</span> ${G(A.entry_price)}</div>\n          <div><span style='color:#9ca3af'>Stop Loss:</span> <span style='color:#ef4444'>${G(A.stop_loss)}</span></div>\n          <div><span style='color:#9ca3af'>Take Profit:</span> <span style='color:#4ade80'>${G(A.take_profit)}</span></div>\n          <div><span style='color:#9ca3af'>Reason:</span> ${A.reason}</div>\n        </div>\n      `,u.appendChild(e)}if(null==(i=r.value)?void 0:i.risk_assessment){const A=r.value.risk_assessment,t=document.createElement("div");t.style.margin="20px 0 0 0",t.style.padding="16px",t.style.background="rgba(31,41,55,0.3)",t.style.border="1px solid #374151",t.style.borderRadius="12px",t.style.boxShadow="0 1px 4px 0 #0001",t.innerHTML=`\n        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">${e("analysis.risk_assessment").replace("analysis.risk_assessment","风险评估")}</div>\n        <div style="display: flex; flex-direction: column; gap: 6px; font-size: 14px;">\n          <div><span style='color:#9ca3af'>${e("analysis.risk_level").replace("analysis.risk_level","风险等级")}:</span> <span style='font-weight:500;'>${A.level}</span></div>\n          <div><span style='color:#9ca3af'>${e("analysis.risk_score").replace("analysis.risk_score","风险分数")}:</span> ${A.score}/100</div>\n          ${A.details&&A.details.length>0?`<div><span style='color:#9ca3af'>${e("analysis.risk_factors").replace("analysis.risk_factors","风险因素")}:</span><ul style='margin:0 0 0 18px;padding:0;color:#d1d5db;'>${A.details.map((A=>`<li>${A}</li>`)).join("")}</ul></div>`:""}\n        </div>\n      `,u.appendChild(t)}const p=document.createElement("div");p.style.textAlign="center",p.style.margin="32px 0 0 0",p.style.padding="16px 0 0 0",p.style.display="flex",p.style.flexDirection="column",p.style.alignItems="center";const f=(localStorage.getItem("language")||"en-US").toLowerCase();let w="";switch(f){case"ja-jp":w="スマートな暗号資産分析と取引意思決定プラットフォーム。市場トレンドを効率的に洞察し、科学的な取引戦略をサポートします。";break;case"ko-kr":w="스마트 암호화폐 분석 및 트레이딩 의사결정 플랫폼, 시장 트렌드를 효율적으로 파악하고 과학적인 전략 수립을 지원합니다.";break;default:w="Smart crypto market analysis and trading decision platform. Efficiently gain market insights and make scientific trading strategies."}p.innerHTML=`\n      <div style="margin-bottom: 8px; font-size: 15px; color: #38bdf8; font-weight: 600;">Cooltrade</div>\n      <div style="margin-bottom: 10px; font-size: 13px; color: #9ca3af; max-width: 320px;">\n        ${w}\n      </div>\n    `;const h=document.createElement("canvas");p.appendChild(h);const m=document.createElement("div");m.style.marginTop="10px",m.style.fontSize="14px",m.style.color="#60a5fa",m.style.fontWeight="bold",m.innerText="www.cooltrade.xyz",p.appendChild(m),u.appendChild(p),document.body.appendChild(u),yield ka.toCanvas(h,"https://www.cooltrade.xyz",{width:100,margin:1});const Q=yield(l=u,c={backgroundColor:"#0F172A",scale:2,logging:!1,width:375,height:u.offsetHeight,onclone:function(A){const e=A.body.querySelector("div");e&&window.getComputedStyle(e).getPropertyValue("height")}},void 0===c&&(c={}),Ha(l,c));document.body.removeChild(u);const y=document.createElement("a"),C=a.value||"CRYPTO";y.download=`${C}_market_analysis.png`,y.href=Q.toDataURL("image/png"),y.click()}catch(u){eA({message:"保存图片失败，请重试",type:"error"})}})),gA=A=>"bullish"===A||"看涨"===A||"支持当前趋势"===A?"rgba(16,185,129,0.12)":"bearish"===A||"看跌"===A||"反对当前趋势"===A?"rgba(239,68,68,0.12)":"rgba(156,163,175,0.12)",pA={"en-US":{buy:"Buy",sell:"Sell",hold:"Hold",wait:"Wait"},"ja-JP":{buy:"買い",sell:"売り",hold:"ホールド",wait:"待機"},"ko-KR":{buy:"매수",sell:"매도",hold:"보유",wait:"대기"}},fA={"en-US":{high:"High",medium:"Medium",low:"Low"},"ja-JP":{high:"高",medium:"中",low:"低"},"ko-KR":{high:"높음",medium:"중간",low:"낮음"}},wA=(A,e)=>{if(!A)return"--";const t=pA[e]||pA["en-US"];return t[A.toLowerCase()]||t.wait||A},hA=(A,e)=>{if(!A)return"--";const t=fA[e]||fA["en-US"];return t[A.toLowerCase()]||t.medium||A},mA=f(localStorage.getItem("language")||"en-US"),QA=()=>u(this,null,(function*(){i.value=!1,o.value=null,yield oA(!0)})),yA=A=>u(this,null,(function*(){try{yield oA(!0),r.value?(i.value=!1,A.value=null):i.value=!0}catch(e){i.value=!0}})),CA=A=>{const t=e(`indicatorExplanations.${A}`);return t===`indicatorExplanations.${A}`?{RSI:"相对强弱指数（RSI），用于衡量价格动量和超买超卖状态。",BIAS:"乖离率，衡量价格偏离均线的程度。",PSY:"心理线指标，反映市场参与者的心理变化。",VWAP:"成交量加权平均价，反映市场真实交易价值。",FundingRate:"资金费率，反映合约市场多空力量对比。",ExchangeNetflow:"交易所净流入，反映资金流向。",NUPL:"未实现净盈亏比率，反映市场整体盈亏状况。",MayerMultiple:"梅耶倍数，当前价格与200日均线的比值。",MACD:"移动平均线收敛散度，用于判断趋势强弱和转折点。",BollingerBands:"布林带，用于衡量价格波动性和支撑阻力位。",DMI:"动向指标，用于判断趋势方向和强度。"}[A]||A:t};"undefined"!=typeof chrome&&chrome.runtime&&chrome.runtime.onMessage&&chrome.runtime.onMessage.addListener(((A,e,t)=>{if("SYMBOL_UPDATED"===A.type&&A.data&&A.data.symbol){const e=A.data.symbol;e&&"string"==typeof e&&(a.value=e)}return!0}));const UA=f(!1);let vA=null;const FA=()=>u(this,null,(function*(){try{if(UA.value||!a.value||"string"!=typeof a.value||!a.value.trim())return;return UA.value=!0,vA||(vA=new Promise(((A,e)=>u(this,null,(function*(){try{yield gg(a.value);let e=0;const n=5,s=2e3;for(;e<n;)try{yield new Promise((A=>setTimeout(A,s)));const t=yield dg(a.value,!0);if(t&&"not_found"!==t.status){const e=zB(t);if(e&&"object"==typeof e)return r.value=e,i.value=!1,o.value=null,void A(!0)}e++}catch(t){e++}throw new Error("Failed to generate report, please try again later")}catch(n){const A=n&&"object"==typeof n&&"message"in n?n.message:"刷新失败";eA.error(A),e(n)}finally{UA.value=!1,vA=null}})))),vA)}catch(A){throw UA.value=!1,vA=null,A}}));_((()=>{vA=null}));const bA=h((()=>{var A;if(!(null==(A=r.value)?void 0:A.last_update_time))return!0;const e=new Date(r.value.last_update_time).getTime();return Date.now()-e>432e5})),_A=f(""),xA=f(""),EA=f([]),HA=f(!1),IA=f(!1),kA=f(!1),LA={"zh-CN":"zh-CN","en-US":"en","ja-JP":"ja","ko-KR":"ko"};return m([()=>{var A,e;return null==(e=null==(A=r.value)?void 0:A.trend_analysis)?void 0:e.summary},()=>mA.value],(A=>u(this,[A],(function*([A,e]){if(A)if("en-US"!==e){HA.value=!0;try{_A.value=yield Xp(A,LA[e]||"zh-CN")}catch(t){_A.value=A}HA.value=!1}else _A.value=A;else _A.value=""}))),{immediate:!0}),m([()=>{var A,e;return null==(e=null==(A=r.value)?void 0:A.trading_advice)?void 0:e.reason},()=>mA.value],(A=>u(this,[A],(function*([A,e]){if(A)if("en-US"!==e){IA.value=!0;try{xA.value=yield Xp(A,LA[e]||"zh-CN")}catch(t){xA.value=A}IA.value=!1}else xA.value=A;else xA.value=""}))),{immediate:!0}),m([()=>{var A,e;return null==(e=null==(A=r.value)?void 0:A.risk_assessment)?void 0:e.details},()=>mA.value],(A=>u(this,[A],(function*([A,e]){if(A&&Array.isArray(A)&&0!==A.length)if("en-US"!==e){kA.value=!0;try{const t=yield Promise.all(A.map((A=>Xp(A,LA[e]||"zh-CN"))));EA.value=t}catch(t){EA.value=A}kA.value=!1}else EA.value=A;else EA.value=[]}))),{immediate:!0}),(A,t)=>{var u,g,p,f,h,m,v,b,_,V,J,Z,$,eA,tA,rA,nA,sA,aA;const iA=W("router-link");return K(),H("div",jp,[k("div",Wp,[k("header",Yp,[k("div",Zp,[x(Xg,{modelValue:l.value,"onUpdate:modelValue":t[0]||(t[0]=A=>l.value=A),onChange:y},null,8,["modelValue"])]),"china"!==l.value?(K(),H("div",zp,[k("div",qp,[k("div",$p,[k("h1",Af,S(a.value?z():M(e)("common.loading")),1),a.value?(K(),D(Ep,{key:0,symbol:a.value,"market-type":l.value,onFavoriteChanged:F,class:"scale-110"},null,8,["symbol","market-type"])):I("",!0)]),k("div",ef,[k("button",{onClick:t[1]||(t[1]=A=>c.value=!0),class:"p-2.5 rounded-xl bg-blue-500/10 hover:bg-blue-500/20 border border-blue-500/30 text-blue-400 transition-all duration-200 hover:scale-105",title:M(e)("common.search")},t[10]||(t[10]=[k("i",{class:"ri-search-line text-lg"},null,-1)]),8,tf),k("button",{onClick:t[2]||(t[2]=A=>d.value=!0),class:"p-2.5 rounded-xl bg-yellow-500/10 hover:bg-yellow-500/20 border border-yellow-500/30 text-yellow-400 transition-all duration-200 hover:scale-105",title:M(e)("common.my_favorites")},t[11]||(t[11]=[k("i",{class:"ri-bookmark-line text-lg"},null,-1)]),8,rf),"china"!==l.value?(K(),H("div",nf,[k("button",{onClick:t[3]||(t[3]=A=>B.value=!B.value),class:O(["p-2.5 border transition-all duration-200",{"bg-green-500/20 border-green-400/50 text-green-300 rounded-t-xl":B.value,"bg-green-500/10 hover:bg-green-500/20 border-green-500/30 text-green-400 rounded-xl hover:scale-105":!B.value}]),title:"crypto"===l.value?M(e)("common.popular_tokens"):M(e)("common.popular_stocks")},t[12]||(t[12]=[k("i",{class:"ri-fire-line text-lg"},null,-1)]),10,sf),B.value?(K(),H("div",{key:0,class:"absolute top-full right-0 w-full max-w-sm bg-slate-800/95 backdrop-blur-sm rounded-b-xl border-l border-r border-b border-green-400/50 shadow-2xl z-50",onClick:t[4]||(t[4]=N((()=>{}),["stop"]))},[k("div",of,[k("h3",af,[t[13]||(t[13]=k("i",{class:"ri-fire-line mr-2"},null,-1)),T(" "+S("crypto"===l.value?M(e)("common.popular_tokens"):M(e)("common.popular_stocks")),1)]),k("div",lf,[(K(!0),H(U,null,P(w.value,(A=>(K(),H("button",{key:A.symbol,onClick:e=>q(A.symbol),disabled:s.value||A.symbol===a.value,class:O(["group relative p-2.5 rounded-lg border transition-all duration-300 hover:scale-105",{"bg-blue-500/20 border-blue-400/50 text-blue-300 shadow-lg shadow-blue-500/20":A.symbol===a.value,"bg-slate-700/40 border-slate-600/50 text-slate-300 hover:bg-slate-600/50 hover:border-slate-500/60":A.symbol!==a.value&&!s.value,"bg-slate-800/30 border-slate-700/30 text-slate-500 cursor-not-allowed":s.value}])},[k("div",uf,S(A.display),1),A.symbol===a.value?(K(),H("div",df)):I("",!0)],10,cf)))),128))])])])):I("",!0)])):I("",!0)])])])):I("",!0)]),"china"===l.value?(K(),H("div",Bf,[k("div",gf,[t[15]||(t[15]=R('<div class="w-20 h-20 mx-auto bg-orange-500/20 rounded-full flex items-center justify-center" data-v-2ad6f64b><i class="ri-tools-line text-3xl text-orange-400" data-v-2ad6f64b></i></div><div class="space-y-3" data-v-2ad6f64b><h3 class="text-xl font-bold text-white" data-v-2ad6f64b>A股市场</h3><p class="text-slate-400 text-sm leading-relaxed" data-v-2ad6f64b>该功能正在开发中，敬请期待</p></div>',2)),k("div",pf,[t[14]||(t[14]=k("i",{class:"ri-time-line mr-2 text-orange-400"},null,-1)),k("span",ff,S(M(e)("common.coming_soon")),1)])])])):I("",!0),"china"!==l.value?(K(),H("main",wf,[Q.value?(K(),H("div",hf,[x(Pg,{loadingText:"Loading price data..."})])):r.value?(K(),H("div",mf,[k("div",Qf,[k("div",yf,[k("div",Cf,[k("h2",Uf,S(M(e)("analysis.snapshot_price")),1),k("div",vf,[T(S(G((null==(u=r.value)?void 0:u.snapshot_price)||0))+" ",1),t[16]||(t[16]=k("span",{class:"text-lg text-slate-400 ml-2"},"USD",-1))])]),k("div",Ff,[k("button",{onClick:dA,class:"flex items-center gap-2 px-4 py-2.5 bg-blue-500/15 hover:bg-blue-500/25 text-blue-400 rounded-xl transition-all duration-200 hover:scale-105 border border-blue-500/30"},[t[17]||(t[17]=k("i",{class:"ri-twitter-fill text-lg"},null,-1)),k("span",bf,S(M(e)("analysis.share_to_twitter")),1)]),k("button",{onClick:BA,class:"flex items-center gap-2 px-4 py-2.5 bg-slate-600/20 hover:bg-slate-600/30 text-slate-300 rounded-xl transition-all duration-200 hover:scale-105 border border-slate-600/40"},[t[18]||(t[18]=k("i",{class:"ri-image-line text-lg"},null,-1)),k("span",_f,S(M(e)("analysis.save_image")),1)])])])]),k("div",xf,[k("div",Ef,[t[19]||(t[19]=k("i",{class:"ri-time-line text-base"},null,-1)),k("span",null,S(M(e)("analysis.last_update"))+": "+S(Y(null==(g=r.value)?void 0:g.last_update_time)),1)]),x(M(AA),{content:bA.value?M(e)("analysis.refresh_report"):M(e)("analysis.refresh_report_too_soon"),placement:"top"},{default:X((()=>[k("button",{onClick:t[5]||(t[5]=A=>bA.value&&FA()),disabled:!bA.value||UA.value,class:O(["p-2.5 rounded-xl transition-all duration-200 hover:scale-105",bA.value?"bg-blue-500/15 text-blue-400 hover:bg-blue-500/25 border border-blue-500/30":"bg-slate-700/30 text-slate-500 cursor-not-allowed border border-slate-700/30"])},[k("i",{class:O(["ri-refresh-line text-lg",{"animate-spin":UA.value}])},null,2)],10,Hf)])),_:1},8,["content"])]),(null==(f=null==(p=r.value)?void 0:p.trend_analysis)?void 0:f.probabilities)?(K(),H("div",If,[k("h3",kf,S(M(e)("analysis.trend_analysis")),1),k("div",Lf,[k("div",Sf,[t[20]||(t[20]=k("div",{class:"w-8 h-8 mx-auto bg-emerald-500/20 rounded-full flex items-center justify-center"},[k("i",{class:"ri-arrow-up-line text-emerald-400"})],-1)),k("div",Kf,S(lA(null==(v=null==(m=null==(h=r.value)?void 0:h.trend_analysis)?void 0:m.probabilities)?void 0:v.up)),1),k("div",Df,S(M(e)("analysis.uptrend")),1)]),k("div",Tf,[t[21]||(t[21]=k("div",{class:"w-8 h-8 mx-auto bg-slate-500/20 rounded-full flex items-center justify-center"},[k("i",{class:"ri-subtract-line text-slate-400"})],-1)),k("div",Of,S(lA(null==(V=null==(_=null==(b=r.value)?void 0:b.trend_analysis)?void 0:_.probabilities)?void 0:V.sideways)),1),k("div",Mf,S(M(e)("analysis.sideways")),1)]),k("div",Rf,[t[22]||(t[22]=k("div",{class:"w-8 h-8 mx-auto bg-red-500/20 rounded-full flex items-center justify-center"},[k("i",{class:"ri-arrow-down-line text-red-400"})],-1)),k("div",Pf,S(lA(null==($=null==(Z=null==(J=r.value)?void 0:J.trend_analysis)?void 0:Z.probabilities)?void 0:$.down)),1),k("div",Nf,S(M(e)("analysis.downtrend")),1)])])])):I("",!0),(null==(tA=null==(eA=r.value)?void 0:eA.trend_analysis)?void 0:tA.summary)?(K(),H("div",Vf,[k("h3",Gf,S(M(e)("analysis.market_trend_analysis")),1),k("div",Jf,[k("div",Xf,[t[23]||(t[23]=k("div",{class:"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-1"},[k("i",{class:"ri-line-chart-line text-blue-400"})],-1)),k("div",jf,[k("p",Wf,[HA.value?(K(),H("span",Yf,"翻译中...")):(K(),H("span",Zf,S(_A.value),1))])])])])])):I("",!0),(null==(rA=r.value)?void 0:rA.indicators_analysis)?(K(),H("div",zf,[k("h3",qf,S(M(e)("analysis.technical_indicators")),1),k("div",$f,[(K(!0),H(U,null,P(null==(nA=r.value)?void 0:nA.indicators_analysis,((A,e)=>(K(),H(U,{key:e},[["MACD","BollingerBands","DMI"].includes(e)?I("",!0):(K(),H("div",Aw,[k("div",ew,[k("div",tw,[k("span",rw,S(e),1),x(M(AA),{content:CA(e),placement:"top"},{default:X((()=>t[24]||(t[24]=[k("i",{class:"ri-question-line text-slate-500 cursor-help text-xs"},null,-1)]))),_:2,__:[24]},1032,["content"])]),k("div",{class:O(["w-6 h-6 rounded-full flex items-center justify-center",cA(A.support_trend)]),style:j(`background:${gA(A.support_trend)}`)},[k("i",{class:O([uA(A.support_trend),"text-xs"])},null,2)],6)]),k("div",nw,S("number"==typeof A.value?A.value.toFixed(2):A.value),1)]))],64)))),128))]),k("div",sw,[(K(),H(U,null,P(["MACD","BollingerBands","DMI"],(A=>{var e;return K(),H(U,{key:A},[(null==(e=r.value)?void 0:e.indicators_analysis)&&r.value.indicators_analysis[A]?(K(),H("div",ow,[k("div",aw,[k("div",iw,[k("span",lw,S(A),1),x(M(AA),{content:CA(A),placement:"top"},{default:X((()=>t[25]||(t[25]=[k("i",{class:"ri-question-line text-slate-500 cursor-help"},null,-1)]))),_:2,__:[25]},1032,["content"])]),k("div",{class:O(["w-8 h-8 rounded-full flex items-center justify-center",cA(r.value.indicators_analysis[A].support_trend)]),style:j(`background:${gA(r.value.indicators_analysis[A].support_trend)}`)},[k("i",{class:O([uA(r.value.indicators_analysis[A].support_trend),"text-sm"])},null,2)],6)]),k("div",cw,["MACD"===A?(K(),H(U,{key:0},[k("div",uw,[t[26]||(t[26]=k("div",{class:"text-xs text-blue-300 font-medium mb-1"},"Histogram",-1)),k("div",dw,S("object"==typeof r.value.indicators_analysis[A].value&&r.value.indicators_analysis[A].value&&"histogram"in r.value.indicators_analysis[A].value&&"number"==typeof r.value.indicators_analysis[A].value.histogram?r.value.indicators_analysis[A].value.histogram.toFixed(2):"--"),1)]),k("div",Bw,[t[27]||(t[27]=k("div",{class:"text-xs text-blue-300 font-medium mb-1"},"MACD Line",-1)),k("div",gw,S("object"==typeof r.value.indicators_analysis[A].value&&r.value.indicators_analysis[A].value&&"line"in r.value.indicators_analysis[A].value&&"number"==typeof r.value.indicators_analysis[A].value.line?r.value.indicators_analysis[A].value.line.toFixed(2):"--"),1)]),k("div",pw,[t[28]||(t[28]=k("div",{class:"text-xs text-blue-300 font-medium mb-1"},"Signal Line",-1)),k("div",fw,S("object"==typeof r.value.indicators_analysis[A].value&&r.value.indicators_analysis[A].value&&"signal"in r.value.indicators_analysis[A].value&&"number"==typeof r.value.indicators_analysis[A].value.signal?r.value.indicators_analysis[A].value.signal.toFixed(2):"--"),1)])],64)):"BollingerBands"===A?(K(),H(U,{key:1},[k("div",ww,[t[29]||(t[29]=k("div",{class:"text-xs text-red-300 font-medium mb-1"},"Upper Band",-1)),k("div",hw,S("object"==typeof r.value.indicators_analysis[A].value&&r.value.indicators_analysis[A].value&&"upper"in r.value.indicators_analysis[A].value&&"number"==typeof r.value.indicators_analysis[A].value.upper?r.value.indicators_analysis[A].value.upper.toFixed(2):"--"),1)]),k("div",mw,[t[30]||(t[30]=k("div",{class:"text-xs text-slate-300 font-medium mb-1"},"Middle Band",-1)),k("div",Qw,S("object"==typeof r.value.indicators_analysis[A].value&&r.value.indicators_analysis[A].value&&"middle"in r.value.indicators_analysis[A].value&&"number"==typeof r.value.indicators_analysis[A].value.middle?r.value.indicators_analysis[A].value.middle.toFixed(2):"--"),1)]),k("div",yw,[t[31]||(t[31]=k("div",{class:"text-xs text-emerald-300 font-medium mb-1"},"Lower Band",-1)),k("div",Cw,S("object"==typeof r.value.indicators_analysis[A].value&&r.value.indicators_analysis[A].value&&"lower"in r.value.indicators_analysis[A].value&&"number"==typeof r.value.indicators_analysis[A].value.lower?r.value.indicators_analysis[A].value.lower.toFixed(2):"--"),1)])],64)):"DMI"===A?(K(),H(U,{key:2},[k("div",Uw,[t[32]||(t[32]=k("div",{class:"text-xs text-emerald-300 font-medium mb-1"},"+DI",-1)),k("div",vw,S("object"==typeof r.value.indicators_analysis[A].value&&r.value.indicators_analysis[A].value&&"plus_di"in r.value.indicators_analysis[A].value&&"number"==typeof r.value.indicators_analysis[A].value.plus_di?r.value.indicators_analysis[A].value.plus_di.toFixed(2):"--"),1)]),k("div",Fw,[t[33]||(t[33]=k("div",{class:"text-xs text-red-300 font-medium mb-1"},"-DI",-1)),k("div",bw,S("object"==typeof r.value.indicators_analysis[A].value&&r.value.indicators_analysis[A].value&&"minus_di"in r.value.indicators_analysis[A].value&&"number"==typeof r.value.indicators_analysis[A].value.minus_di?r.value.indicators_analysis[A].value.minus_di.toFixed(2):"--"),1)]),k("div",_w,[t[34]||(t[34]=k("div",{class:"text-xs text-blue-300 font-medium mb-1"},"ADX",-1)),k("div",xw,S("object"==typeof r.value.indicators_analysis[A].value&&r.value.indicators_analysis[A].value&&"adx"in r.value.indicators_analysis[A].value&&"number"==typeof r.value.indicators_analysis[A].value.adx?r.value.indicators_analysis[A].value.adx.toFixed(2):"--"),1)])],64)):I("",!0)])])):I("",!0)],64)})),64))])])):I("",!0),(null==(sA=r.value)?void 0:sA.trading_advice)?(K(),H("div",Ew,[k("h3",Hw,S(M(e)("analysis.trading_advice")),1),k("div",Iw,[k("div",kw,[k("div",Lw,S(M(e)("analysis.recommended_action")),1),k("div",{class:O(["px-3 py-1 rounded-full text-sm font-bold","买入"===r.value.trading_advice.action?"bg-emerald-500/20 text-emerald-400 border border-emerald-500/40":"卖出"===r.value.trading_advice.action?"bg-red-500/20 text-red-400 border border-red-500/40":"bg-slate-500/20 text-slate-400 border border-slate-500/40"])},S(wA(r.value.trading_advice.action,mA.value)),3)]),k("div",Sw,[k("div",Kw,[k("div",Dw,S(M(e)("analysis.entry_price")),1),k("div",Tw,S(G(r.value.trading_advice.entry_price)),1)]),k("div",Ow,[k("div",Mw,S(M(e)("analysis.stop_loss")),1),k("div",Rw,S(G(r.value.trading_advice.stop_loss)),1)]),k("div",Pw,[k("div",Nw,S(M(e)("analysis.take_profit")),1),k("div",Vw,S(G(r.value.trading_advice.take_profit)),1)])]),k("div",Gw,[k("div",Jw,[t[35]||(t[35]=k("div",{class:"w-6 h-6 bg-amber-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"},[k("i",{class:"ri-lightbulb-line text-amber-400 text-sm"})],-1)),k("div",Xw,[k("div",jw,S(M(e)("analysis.reason")),1),k("div",Ww,[IA.value?(K(),H("span",Yw,"翻译中...")):(K(),H("span",Zw,S(xA.value||r.value.trading_advice.reason),1))])])])])])])):I("",!0),(null==(aA=r.value)?void 0:aA.risk_assessment)?(K(),H("div",zw,[k("h3",qw,S(M(e)("analysis.risk_assessment")),1),k("div",$w,[k("div",Ah,[k("div",eh,S(M(e)("analysis.risk_level")),1),k("div",{class:O(["px-3 py-1 rounded-full text-sm font-bold",{"bg-red-500/20 text-red-400 border border-red-500/40":"高"===r.value.risk_assessment.level,"bg-amber-500/20 text-amber-400 border border-amber-500/40":"中"===r.value.risk_assessment.level,"bg-emerald-500/20 text-emerald-400 border border-emerald-500/40":"低"===r.value.risk_assessment.level}])},S(hA(r.value.risk_assessment.level,mA.value)),3)]),k("div",th,[k("div",rh,[k("span",nh,S(M(e)("analysis.risk_score")),1),k("span",sh,S(r.value.risk_assessment.score)+"%",1)]),k("div",oh,[k("div",{class:O(["h-full rounded-full transition-all duration-500",{"bg-gradient-to-r from-red-500 to-red-400":r.value.risk_assessment.score>70,"bg-gradient-to-r from-amber-500 to-amber-400":r.value.risk_assessment.score>30&&r.value.risk_assessment.score<=70,"bg-gradient-to-r from-emerald-500 to-emerald-400":r.value.risk_assessment.score<=30}]),style:j({width:`${r.value.risk_assessment.score}%`})},null,6)])]),r.value.risk_assessment.details&&r.value.risk_assessment.details.length>0?(K(),H("div",ah,[k("div",ih,[t[36]||(t[36]=k("div",{class:"w-6 h-6 bg-orange-500/20 rounded-full flex items-center justify-center"},[k("i",{class:"ri-alert-line text-orange-400 text-sm"})],-1)),k("span",lh,S(M(e)("analysis.risk_factors")),1)]),k("div",ch,[kA.value?(K(),H("div",uh,"翻译中...")):(K(),H("div",dh,[(K(!0),H(U,null,P(EA.value.length>0?EA.value:r.value.risk_assessment.details,((A,e)=>(K(),H("div",{key:e,class:"flex items-start space-x-2 text-sm text-slate-200"},[t[37]||(t[37]=k("div",{class:"w-1.5 h-1.5 bg-slate-400 rounded-full mt-2 flex-shrink-0"},null,-1)),k("span",null,S(A),1)])))),128))]))])])):I("",!0)])])):I("",!0)])):!i.value||n.value||s.value?!o.value||n.value||s.value?r.value||n.value||s.value||i.value||o.value?I("",!0):(K(),H("div",hh,[k("div",mh,[t[40]||(t[40]=k("div",{class:"w-16 h-16 mx-auto bg-slate-500/20 rounded-full flex items-center justify-center mb-4"},[k("i",{class:"ri-database-line text-2xl text-slate-400"})],-1)),t[41]||(t[41]=k("h3",{class:"text-lg font-bold text-white mb-2"},"暂无数据",-1)),k("p",Qh,S(M(e)("common.no_data")),1),k("button",{onClick:t[7]||(t[7]=()=>oA()),class:"px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl font-medium transition-colors"},S(M(e)("common.load_data")),1)])])):(K(),H("div",gh,[k("div",ph,[t[38]||(t[38]=k("div",{class:"w-16 h-16 mx-auto bg-red-500/20 rounded-full flex items-center justify-center mb-4"},[k("i",{class:"ri-error-warning-line text-2xl text-red-400"})],-1)),t[39]||(t[39]=k("h3",{class:"text-lg font-bold text-white mb-2"},"出现错误",-1)),k("p",fh,S(o.value),1),k("p",wh,S(M(e)("errors.try_reload_or_later")),1),k("button",{onClick:t[6]||(t[6]=()=>oA()),class:"px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl font-medium transition-colors"},S(M(e)("common.retry")),1)])])):(K(),H("div",Bh,[x(Dg,{symbol:a.value,onRefreshSuccess:QA,onRefreshError:yA},null,8,["symbol"])]))])):I("",!0),k("nav",yh,[k("div",Ch,[x(iA,{to:"/",class:"flex flex-col items-center justify-center text-blue-400 border-t-2 border-blue-400"},{default:X((()=>[t[42]||(t[42]=k("i",{class:"ri-line-chart-line text-xl"},null,-1)),k("span",Uh,S(M(e)("nav.market")),1)])),_:1,__:[42]}),x(iA,{to:"/points",class:"flex flex-col items-center justify-center text-slate-500 hover:text-slate-300 transition-colors"},{default:X((()=>[t[43]||(t[43]=k("i",{class:"ri-coin-line text-xl"},null,-1)),k("span",vh,S(M(e)("nav.points")),1)])),_:1,__:[43]}),x(iA,{to:"/profile",class:"flex flex-col items-center justify-center text-slate-500 hover:text-slate-300 transition-colors"},{default:X((()=>[t[44]||(t[44]=k("i",{class:"ri-settings-3-line text-xl"},null,-1)),k("span",Fh,S(M(e)("nav.settings")),1)])),_:1,__:[44]})])]),x(bp,{visible:c.value,onClose:t[8]||(t[8]=A=>c.value=!1),onSelect:C},null,8,["visible"]),x(Jp,{visible:d.value,onClose:t[9]||(t[9]=A=>d.value=!1),onSelect:E,onFavoriteRemoved:L},null,8,["visible"]),x(Hg,{visible:UA.value,type:"generate"},null,8,["visible"])])])}}}),_h=Rg(bh,[["__scopeId","data-v-2ad6f64b"]]),xh={class:"container mx-auto px-4 py-8"},Eh={class:"max-w-4xl mx-auto"},Hh={class:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12"},Ih={class:"card bg-dark-800 border border-dark-700"},kh={class:"space-y-4"},Lh={class:"flex flex-wrap gap-2"},Sh={class:"flex flex-wrap gap-2"},Kh={class:"mb-12"},Dh={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Th={class:"text-center"},Oh={class:"text-3xl mb-3"},Mh={class:"text-gray-400 text-sm"},Rh={class:"card bg-dark-800 border border-dark-700"},Ph={class:"grid grid-cols-2 md:grid-cols-4 gap-6 text-center"},Nh={class:"text-sm text-gray-400"},Vh=y({__name:"AboutView",setup(A){const e=["Vue 3","TypeScript","Vite","TailwindCSS","Element Plus","Vue Router","Pinia"],t=["ESLint","Prettier","PostCSS","Autoprefixer","Terser","Vue DevTools"],r=[{icon:"📊",title:"实时数据",description:"获取最新的市场数据和价格信息",color:"text-blue-400"},{icon:"📈",title:"技术分析",description:"专业的技术指标和图表分析",color:"text-green-400"},{icon:"🔔",title:"智能提醒",description:"价格预警和重要事件通知",color:"text-yellow-400"},{icon:"🌐",title:"多语言",description:"支持中文、英文、日文、韩文",color:"text-purple-400"},{icon:"📱",title:"响应式",description:"完美适配桌面端和移动端",color:"text-pink-400"},{icon:"🔒",title:"安全性",description:"数据加密和隐私保护",color:"text-red-400"}],n=[{label:"组件数量",value:"20+",color:"text-blue-400"},{label:"代码行数",value:"5K+",color:"text-green-400"},{label:"依赖包",value:"15+",color:"text-blue-400"},{label:"构建时间",value:"<30s",color:"text-yellow-400"}];return b((()=>{})),(A,s)=>(K(),H("div",xh,[k("div",Eh,[s[6]||(s[6]=k("div",{class:"text-center mb-12"},[k("h1",{class:"text-4xl font-bold mb-4 text-gray-200"}," 关于 CoolTrade "),k("p",{class:"text-xl text-gray-400"}," 了解我们的项目和技术架构 ")],-1)),k("div",Hh,[s[3]||(s[3]=R('<div class="card bg-dark-800 border border-dark-700"><h2 class="text-2xl font-semibold mb-4 text-blue-400"><i class="ri-information-line mr-2"></i> 项目简介 </h2><div class="space-y-4 text-gray-300"><p> CoolTrade 是一个专业的加密货币分析平台，致力于为用户提供实时、准确的市场分析和交易建议。 </p><p> 我们使用最新的前端技术栈构建了这个现代化的 Web 应用，确保用户体验的流畅性和数据的实时性。 </p><ul class="list-disc list-inside space-y-2 text-sm"><li>实时市场数据监控</li><li>专业技术分析工具</li><li>多语言支持</li><li>响应式设计</li></ul></div></div>',1)),k("div",Ih,[s[2]||(s[2]=k("h2",{class:"text-2xl font-semibold mb-4 text-green-400"},[k("i",{class:"ri-code-line mr-2"}),T(" 技术架构 ")],-1)),k("div",kh,[k("div",null,[s[0]||(s[0]=k("h3",{class:"font-semibold text-gray-200 mb-2"},"前端技术",-1)),k("div",Lh,[(K(),H(U,null,P(e,(A=>k("span",{key:A,class:"px-3 py-1 bg-primary-600 text-white text-xs rounded-full"},S(A),1))),64))])]),k("div",null,[s[1]||(s[1]=k("h3",{class:"font-semibold text-gray-200 mb-2"},"开发工具",-1)),k("div",Sh,[(K(),H(U,null,P(t,(A=>k("span",{key:A,class:"px-3 py-1 bg-green-600 text-white text-xs rounded-full"},S(A),1))),64))])])])])]),k("div",Kh,[s[4]||(s[4]=k("h2",{class:"text-2xl font-bold text-center mb-8 text-gray-200"}," 核心功能 ",-1)),k("div",Dh,[(K(),H(U,null,P(r,(A=>k("div",{key:A.title,class:"card bg-dark-800 border border-dark-700 hover:border-blue-500 transition-colors"},[k("div",Th,[k("div",Oh,S(A.icon),1),k("h3",{class:O(["text-lg font-semibold mb-2",A.color])},S(A.title),3),k("p",Mh,S(A.description),1)])]))),64))])]),k("div",Rh,[s[5]||(s[5]=k("h2",{class:"text-2xl font-semibold mb-6 text-center text-gray-200"}," 项目统计 ",-1)),k("div",Ph,[(K(),H(U,null,P(n,(A=>k("div",{key:A.label},[k("div",{class:O(["text-2xl font-bold mb-1",A.color])},S(A.value),3),k("div",Nh,S(A.label),1)]))),64))])])])]))}}),Gh="/assets/icon128.D2N9qAIP.png",Jh={class:"min-h-screen flex flex-col bg-[#0F172A] overflow-y-auto"},Xh={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},jh={class:"max-w-[375px] mx-auto"},Wh={class:"flex items-center px-4 py-3"},Yh={class:"text-lg font-semibold"},Zh={class:"flex-1 pt-16 pb-16"},zh={class:"max-w-[375px] mx-auto px-4"},qh={key:0,class:"p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm"},$h={for:"email",class:"block text-sm font-medium text-gray-300 mb-1"},Am=["placeholder"],em={class:"flex justify-between items-center mb-1"},tm={for:"password",class:"block text-sm font-medium text-gray-300"},rm=["placeholder"],nm=["disabled"],sm={class:"mt-6 text-center"},om={class:"text-sm text-gray-400"},am=y({__name:"LoginView",setup(A){const{t:e}=bu(),t=Y(),r=f(!1),n=f(void 0),s=f({email:"",password:""}),o=()=>{t.push("/register")},a=()=>{t.push("/forgot-password")},i=()=>u(this,null,(function*(){var A,o,a,i,l,c,u;r.value=!0,n.value=void 0;try{const r=yield lg({email:s.value.email.trim(),password:s.value.password.trim()});"success"===r.status&&(null==(A=r.data)?void 0:A.token)?(localStorage.setItem("token",r.data.token),localStorage.setItem("userInfo",JSON.stringify(r.data.user)),t.push("/")):n.value=e("errors.login_failed_no_token")}catch(d){if(null==(a=null==(o=d.response)?void 0:o.data)?void 0:a.message)if("object"==typeof d.response.data.message){const A=Object.values(d.response.data.message).flat();A.length>0?n.value=A[0]:n.value=e("errors.login_failed_check_input")}else n.value=d.response.data.message;else(null==(l=null==(i=d.response)?void 0:i.data)?void 0:l.detail)?n.value=d.response.data.detail:401===(null==(c=d.response)?void 0:c.status)?n.value=e("errors.email_or_password_incorrect"):429===(null==(u=d.response)?void 0:u.status)?n.value=e("errors.too_many_attempts"):"ECONNABORTED"===d.code?n.value=e("errors.connection_timeout"):d.message.includes("Network Error")?n.value=e("errors.network_error"):n.value=e("errors.login_failed")}finally{r.value=!1}}));return(A,l)=>(K(),H("div",Jh,[k("header",Xh,[k("div",jh,[k("div",Wh,[k("button",{onClick:l[0]||(l[0]=A=>M(t).push("/")),class:"mr-2"},l[3]||(l[3]=[k("i",{class:"ri-arrow-left-line ri-lg"},null,-1)])),k("h1",Yh,S(M(e)("auth.login")),1)])])]),k("main",Zh,[k("div",zh,[l[4]||(l[4]=k("div",{class:"flex flex-col items-center justify-center mt-8 mb-6"},[k("img",{src:Gh,alt:"Cooltrade Logo",class:"w-16 h-16 mb-2 rounded-lg shadow-lg"}),k("div",{class:"text-2xl font-bold text-white tracking-wide mb-1"},"Cooltrade")],-1)),k("form",{onSubmit:N(i,["prevent"]),class:"space-y-6"},[n.value?(K(),H("div",qh,S(n.value),1)):I("",!0),k("div",null,[k("label",$h,S(M(e)("auth.email")),1),V(k("input",{id:"email","onUpdate:modelValue":l[1]||(l[1]=A=>s.value.email=A),type:"email",class:"w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors",placeholder:M(e)("auth.email_placeholder"),required:""},null,8,Am),[[G,s.value.email]])]),k("div",null,[k("div",em,[k("label",tm,S(M(e)("auth.password")),1),k("a",{href:"#",onClick:N(a,["prevent"]),class:"text-xs text-primary hover:underline"},S(M(e)("auth.forgot_password")),1)]),V(k("input",{id:"password","onUpdate:modelValue":l[2]||(l[2]=A=>s.value.password=A),type:"password",class:"w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors",placeholder:M(e)("auth.password_placeholder"),required:""},null,8,rm),[[G,s.value.password]])]),k("button",{type:"submit",class:"w-full py-3 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors",disabled:r.value},S(r.value?M(e)("common.loading"):M(e)("auth.login")),9,nm)],32),k("div",sm,[k("p",om,[T(S(M(e)("auth.no_account"))+" ",1),k("a",{href:"#",onClick:N(o,["prevent"]),class:"text-primary hover:underline"},S(M(e)("auth.register_now")),1)])])])])]))}}),im=(A,e)=>{if("chrome-extension:"===window.location.protocol||"moz-extension:"===window.location.protocol||"extension:"===window.location.protocol){let t=`#${A}`;if(e){const A=Object.entries(e).map((([A,e])=>`${encodeURIComponent(A)}=${encodeURIComponent(e)}`)).join("&");A&&(t+=`?${A}`)}window.location.href=t}else gC.push({path:A,query:e})},lm={class:"min-h-screen flex flex-col bg-[#0F172A] overflow-y-auto"},cm={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},um={class:"max-w-[375px] mx-auto"},dm={class:"flex items-center px-4 py-3"},Bm={class:"text-lg font-semibold"},gm={class:"flex-1 pt-16 pb-16"},pm={class:"max-w-[375px] mx-auto px-4"},fm={class:"block text-sm font-medium text-gray-400 mb-1"},wm=["placeholder"],hm={key:0,class:"mt-1 text-sm text-red-500"},mm={class:"block text-sm font-medium text-gray-400 mb-1"},Qm=["placeholder"],ym={key:0,class:"mt-1 text-sm text-red-500"},Cm={class:"flex gap-2"},Um={class:"flex-1"},vm={class:"block text-sm font-medium text-gray-400 mb-1"},Fm=["placeholder"],bm={key:0,class:"mt-1 text-sm text-red-500"},_m=["disabled"],xm={class:"block text-sm font-medium text-gray-400 mb-1"},Em=["placeholder"],Hm={key:0,class:"mt-1 text-sm text-red-500"},Im=["disabled"],km={class:"mt-6 text-center"},Lm={class:"text-sm text-gray-400"},Sm=y({__name:"RegisterView",setup(A){const{t:e}=bu(),t=Y(),r=()=>{var A;im("/login",A?{redirect:A}:void 0)},n=f({email:"",password:"",code:"",invitation_code:""}),s=f(!1),o=f(!1),a=f(0),i=f({email:"",password:"",code:"",invitation_code:""}),l=f(""),c=f(null),d=f(null),B=f(null),g=f(null),p=()=>{i.value={email:"",password:"",code:"",invitation_code:""},l.value=""},w=A=>{const e={email:c,password:d,code:B,invitation_code:g}[A];e.value&&setTimeout((()=>{var A;null==(A=e.value)||A.focus()}),100)},h=()=>{localStorage.setItem("registerFormData",JSON.stringify({email:n.value.email,password:n.value.password,code:n.value.code}))},m=()=>{i.value.email="",l.value="",h()},Q=()=>{i.value.password="",l.value="",h()},y=()=>{i.value.code="",l.value="",h()},C=()=>{i.value.invitation_code=""},U=()=>u(this,null,(function*(){var A,t,r,s,c;if(p(),!n.value.email)return i.value.email=e("errors.email_required"),void w("email");o.value=!0;try{const A=yield ag({email:n.value.email.trim()});A&&"success"===A.status?((()=>{a.value=60;const A=setInterval((()=>{a.value--,a.value<=0&&clearInterval(A)}),1e3)})(),l.value=""):l.value=(null==A?void 0:A.message)||e("errors.send_code_failed")}catch(u){(null==(r=null==(t=null==(A=u.response)?void 0:A.data)?void 0:t.message)?void 0:r.email)?(i.value.email=u.response.data.message.email[0]||e("errors.invalid_email_format"),w("email")):(null==(c=null==(s=u.response)?void 0:s.data)?void 0:c.message)?l.value="string"==typeof u.response.data.message?u.response.data.message:e("errors.send_code_failed"):l.value=e("errors.send_code_failed")}finally{o.value=!1}})),v=()=>u(this,null,(function*(){var A,r;p();let o=!1;if(n.value.email||(i.value.email=e("errors.email_required"),o||(w("email"),o=!0)),n.value.password||(i.value.password=e("errors.password_required"),o||(w("password"),o=!0)),n.value.code||(i.value.code=e("errors.verification_code_required"),o||(w("code"),o=!0)),n.value.invitation_code||(i.value.invitation_code=e("errors.invitation_code_required"),o||(w("invitation_code"),o=!0)),!o){s.value=!0;try{const A={email:n.value.email.trim(),password:n.value.password.trim(),code:n.value.code.trim(),invitation_code:n.value.invitation_code.trim()},r=yield ig(A);r&&"success"===r.status?(localStorage.removeItem("registerFormData"),t.push("/login")):l.value=(null==r?void 0:r.message)||e("errors.registration_failed")}catch(a){if(null==(r=null==(A=a.response)?void 0:A.data)?void 0:r.message){const A=a.response.data.message;if("object"==typeof A){const e={email:"email",password:"password",code:"code",invitation_code:"invitation_code"};let t=!1;Object.entries(A).forEach((([A,r])=>{const n=e[A];n?(i.value[n]=Array.isArray(r)?r[0]:r,t||(w(n),t=!0)):l.value=Array.isArray(r)?r[0]:r}))}else"string"==typeof A&&(l.value=A)}else l.value=e("errors.registration_failed")}finally{s.value=!1}}}));return b((()=>{(()=>{const A=localStorage.getItem("registerFormData");if(A){const{email:e,password:t,code:r}=JSON.parse(A);n.value.email=e,n.value.password=t,n.value.code=r}})()})),(A,l)=>(K(),H("div",lm,[k("header",cm,[k("div",um,[k("div",dm,[k("button",{onClick:l[0]||(l[0]=A=>M(t).push("/login")),class:"mr-2"},l[5]||(l[5]=[k("i",{class:"ri-arrow-left-line ri-lg"},null,-1)])),k("h1",Bm,S(M(e)("auth.register")),1)])])]),k("main",gm,[k("div",pm,[l[6]||(l[6]=k("div",{class:"flex flex-col items-center justify-center mt-8 mb-6"},[k("img",{src:Gh,alt:"Cooltrade Logo",class:"w-16 h-16 mb-2 rounded-lg shadow-lg"}),k("div",{class:"text-2xl font-bold text-white tracking-wide mb-1"},"Cooltrade")],-1)),k("form",{onSubmit:N(v,["prevent"]),class:"space-y-4"},[k("div",null,[k("label",fm,S(M(e)("auth.email")),1),V(k("input",{type:"email","onUpdate:modelValue":l[1]||(l[1]=A=>n.value.email=A),onInput:m,required:"",ref_key:"emailInput",ref:c,class:O(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",i.value.email?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:M(e)("auth.email_placeholder")},null,42,wm),[[G,n.value.email]]),i.value.email?(K(),H("p",hm,S(i.value.email),1)):I("",!0)]),k("div",null,[k("label",mm,S(M(e)("auth.password")),1),V(k("input",{type:"password","onUpdate:modelValue":l[2]||(l[2]=A=>n.value.password=A),onInput:Q,required:"",ref_key:"passwordInput",ref:d,class:O(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",i.value.password?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:M(e)("auth.password_placeholder")},null,42,Qm),[[G,n.value.password]]),i.value.password?(K(),H("p",ym,S(i.value.password),1)):I("",!0)]),k("div",Cm,[k("div",Um,[k("label",vm,S(M(e)("auth.verification_code")),1),V(k("input",{type:"text","onUpdate:modelValue":l[3]||(l[3]=A=>n.value.code=A),onInput:y,required:"",ref_key:"codeInput",ref:B,class:O(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",i.value.code?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:M(e)("auth.verification_code_placeholder")},null,42,Fm),[[G,n.value.code]]),i.value.code?(K(),H("p",bm,S(i.value.code),1)):I("",!0)]),k("button",{type:"button",onClick:U,disabled:o.value||a.value>0,class:"mt-6 px-4 py-2 bg-gray-800 text-white rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed transition-colors"},S(a.value>0?M(e)("auth.retry_in_seconds",{seconds:a.value}):o.value?M(e)("common.sending"):M(e)("auth.send_code")),9,_m)]),k("div",null,[k("label",xm,S(M(e)("auth.invitation_code")),1),V(k("input",{type:"text","onUpdate:modelValue":l[4]||(l[4]=A=>n.value.invitation_code=A),onInput:C,required:"",ref_key:"invitationCodeInput",ref:g,class:O(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",i.value.invitation_code?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:M(e)("auth.invitation_code_placeholder")},null,42,Em),[[G,n.value.invitation_code]]),i.value.invitation_code?(K(),H("p",Hm,S(i.value.invitation_code),1)):I("",!0)]),k("button",{type:"submit",class:"w-full py-3 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors",disabled:s.value},S(s.value?M(e)("common.registering"):M(e)("auth.register")),9,Im)],32),k("div",km,[k("p",Lm,[T(S(M(e)("auth.have_account"))+" ",1),k("a",{href:"#",onClick:N(r,["prevent"]),class:"text-primary hover:underline"},S(M(e)("auth.login_now")),1)])])])])]))}}),Km={},Dm=function(A={}){const e=!Fl(A.globalInjection)||A.globalInjection,t=new Map,[r,n]=function(A){const e=p(),t=e.run((()=>Au(A)));if(null==t)throw Mc(Oc);return[e,t]}(A),s=ll(""),o={install(A,...t){return u(this,null,(function*(){if(A.__VUE_I18N_SYMBOL__=s,A.provide(A.__VUE_I18N_SYMBOL__,o),El(t[0])){const A=t[0];o.__composerExtend=A.__composerExtend}let r=null;e&&(r=function(A,e){const t=Object.create(null);lu.forEach((A=>{const r=Object.getOwnPropertyDescriptor(e,A);if(!r)throw Mc(Oc);const n=Q(r.value)?{get:()=>r.value.value,set(A){r.value.value=A}}:{get:()=>r.get&&r.get()};Object.defineProperty(t,A,n)})),A.config.globalProperties.$i18n=t,cu.forEach((t=>{const r=Object.getOwnPropertyDescriptor(e,t);if(!r||!r.value)throw Mc(Oc);Object.defineProperty(A.config.globalProperties,`$${t}`,r)}));const r=()=>{delete A.config.globalProperties.$i18n,cu.forEach((e=>{delete A.config.globalProperties[`$${e}`]}))};return r}(A,o.global)),__VUE_I18N_FULL_INSTALL__&&function(A,...e){const t=El(e[0])?e[0]:{};(!Fl(t.globalInstall)||t.globalInstall)&&([ou.name,"I18nT"].forEach((e=>A.component(e,ou))),[su.name,"I18nN"].forEach((e=>A.component(e,su))),[nu.name,"I18nD"].forEach((e=>A.component(e,nu))))}(A,...t);const n=A.unmount;A.unmount=()=>{r&&r(),o.dispose(),n()}}))},get global(){return n},dispose(){r.stop()},__instances:t,__getInstance:function(A){return t.get(A)||null},__setInstance:function(A,e){t.set(A,e)},__deleteInstance:function(A){t.delete(A)}};return o}({locale:(()=>{const A=localStorage.getItem("language");return A&&["zh-CN","en-US","ja-JP","ko-KR"].includes(A)?A:(localStorage.setItem("language","en-US"),"en-US")})(),fallbackLocale:"en-US",globalInjection:!0,missingWarn:!1,fallbackWarn:!1,messages:{"zh-CN":du,"en-US":Bu,"ja-JP":gu,"ko-KR":pu},warnHtmlMessage:!1,escapeParameter:!0}),Tm=A=>{if(["zh-CN","en-US","ja-JP","ko-KR"].includes(A)){if((localStorage.getItem("language")||"en-US")===A)return;localStorage.setItem("language",A),Dm.global.locale.value=A;try{(function(A,e){let t=Promise.resolve();if(e&&e.length>0){document.getElementsByTagName("link");const A=document.querySelector("meta[property=csp-nonce]"),r=(null==A?void 0:A.nonce)||(null==A?void 0:A.getAttribute("nonce"));t=Promise.allSettled(e.map((A=>{if((A=function(A){return"/"+A}(A))in Km)return;Km[A]=!0;const e=A.endsWith(".css"),t=e?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${A}"]${t}`))return;const n=document.createElement("link");return n.rel=e?"stylesheet":"modulepreload",e||(n.as="script"),n.crossOrigin="",n.href=A,r&&n.setAttribute("nonce",r),document.head.appendChild(n),e?new Promise(((e,t)=>{n.addEventListener("load",e),n.addEventListener("error",(()=>t(new Error(`Unable to preload CSS for ${A}`))))})):void 0})))}function r(A){const e=new Event("vite:preloadError",{cancelable:!0});if(e.payload=A,window.dispatchEvent(e),!e.defaultPrevented)throw A}return t.then((e=>{for(const A of e||[])"rejected"===A.status&&r(A.reason);return A().catch(r)}))})((()=>Promise.resolve().then((()=>vu))),void 0).then((e=>{e.setLocale(A)}))}catch(e){}localStorage.getItem("token")&&Om(A),window.dispatchEvent(new CustomEvent("language-changed",{detail:{language:A}})),setTimeout((()=>{window.dispatchEvent(new Event("force-refresh-i18n"))}),100)}},Om=e=>u(A,null,(function*(){try{const t=localStorage.getItem("token");if(!t)return;if("chrome-extension:"===window.location.protocol){try{const A=localStorage.getItem("userInfo");if(A){const t=JSON.parse(A);t.language=e,localStorage.setItem("userInfo",JSON.stringify(t))}}catch(A){}return}const r="/api/auth/profile/";if((yield fetch(r,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:t},body:JSON.stringify({language:e})})).ok)try{const A=localStorage.getItem("userInfo");if(A){const t=JSON.parse(A);t.language=e,localStorage.setItem("userInfo",JSON.stringify(t))}}catch(A){}}catch(t){}})),Mm={class:"min-h-screen flex flex-col bg-[#0F172A] overflow-y-scroll"},Rm={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},Pm={class:"max-w-[375px] mx-auto"},Nm={class:"flex items-center h-12 px-4"},Vm=["textContent"],Gm={class:"flex-1 pt-16 pb-16"},Jm={class:"max-w-[375px] mx-auto px-4"},Xm={key:0,class:"bg-gray-800 rounded-lg p-6 mb-6"},jm={class:"text-center"},Wm={class:"text-lg font-semibold mb-2"},Ym={class:"text-gray-400 text-sm mb-4"},Zm={class:"bg-gray-800 rounded-lg p-6 mb-6"},zm={class:"flex items-center space-x-4"},qm={class:"w-16 h-16 rounded-full bg-gradient-to-r from-primary to-blue-500 flex items-center justify-center text-2xl font-bold overflow-hidden"},$m={class:"flex-1"},AQ={class:"text-base font-semibold"},eQ={class:"text-gray-500 text-xs mt-1"},tQ=["textContent"],rQ={class:"space-y-4"},nQ=["textContent"],sQ=["textContent"],oQ={class:"ml-auto flex items-center"},aQ={class:"text-gray-400 mr-2"},iQ={key:0,class:"fixed inset-0 bg-black/70 z-50 flex items-center justify-center p-4"},lQ={class:"bg-gray-900 rounded-lg w-full max-w-sm overflow-hidden"},cQ={class:"p-4 border-b border-gray-800 flex justify-between items-center"},uQ={class:"text-lg font-medium"},dQ={class:"p-4"},BQ={class:"space-y-2"},gQ=["onClick"],pQ={class:"flex items-center"},fQ={class:"text-lg mr-3"},wQ={key:0,class:"ri-check-line text-primary"},hQ={href:"https://www.cooltrade.xyz/privacy-policy/",target:"_blank",class:"w-full py-3 px-4 bg-gray-800 text-white rounded-lg font-medium flex items-center"},mQ=["textContent"],QQ={class:"w-full py-3 px-4 bg-gray-800 text-white rounded-lg font-medium flex items-center"},yQ=["textContent"],CQ=["textContent"],UQ={class:"fixed bottom-0 w-full bg-[#0F172A]/95 backdrop-blur-md border-t border-gray-800"},vQ={class:"max-w-[375px] mx-auto"},FQ={class:"grid grid-cols-3 h-16"},bQ=["textContent"],_Q=["textContent"],xQ=["textContent"],EQ=y({__name:"ProfileView",setup(A){const e=Y(),{t:t,locale:r}=bu(),n=f({id:0,email:"",created_at:"",updated_at:"",language:"zh-CN"}),s=f(!1),o=f(r.value);m(r,(A=>{o.value=A}));const a=()=>{const A=o.value,e=i.find((e=>e.code===A));return e?e.name:"Unknown"},i=[{code:"en-US",name:"English"},{code:"zh-CN",name:"简体中文"},{code:"ja-JP",name:"日本語"},{code:"ko-KR",name:"한국어"}],l=h((()=>!!localStorage.getItem("token"))),c=A=>{if(!A)return"";const e=new Date(A);let t="en-US";const r=localStorage.getItem("language");return"zh-CN"===r?t="zh-CN":"ja-JP"===r?t="ja-JP":"ko-KR"===r&&(t="ko-KR"),e.toLocaleDateString(t,{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},d=()=>u(this,null,(function*(){if(l.value)try{const e=localStorage.getItem("userInfo");if(e)try{const A=JSON.parse(e);n.value=A}catch(A){}if("chrome-extension:"===window.location.protocol)return;const t=(yield Ag.get("/auth/profile/")).data;"success"===(null==t?void 0:t.status)&&(null==t?void 0:t.data)&&(n.value=t.data,localStorage.setItem("userInfo",JSON.stringify(t.data)))}catch(e){}})),B=A=>u(this,null,(function*(){try{"chrome-extension:"===window.location.protocol||(yield Ag.put("/auth/profile/",{language:A})),n.value.language=A,localStorage.setItem("userInfo",JSON.stringify(n.value))}catch(e){n.value.language=A,localStorage.setItem("userInfo",JSON.stringify(n.value))}})),g=()=>{localStorage.removeItem("token"),localStorage.removeItem("userInfo"),e.push("/login")};return b((()=>u(this,null,(function*(){yield d();const A=localStorage.getItem("language")||"en-US";l.value&&n.value.language&&n.value.language!==A?Tm(n.value.language):o.value=A,(()=>{let A=localStorage.getItem("language")||"en-US",e=Date.now();window.addEventListener("language-changed",(t=>{var r;const n=(null==(r=t.detail)?void 0:r.language)||localStorage.getItem("language")||"en-US",s=Date.now();n===A&&s-e<1e3||(o.value=n,A=n,e=s)})),window.addEventListener("force-refresh-i18n",(()=>{const t=localStorage.getItem("language")||"en-US",r=Date.now();t===A&&r-e<1e3||(o.value=t,A=t,e=r)}))})()})))),(A,e)=>{var r,u;const d=W("router-link");return K(),H("div",Mm,[k("header",Rm,[k("div",Pm,[k("div",Nm,[k("h1",{class:"text-lg font-semibold",textContent:S(M(t)("nav.settings"))},null,8,Vm)])])]),k("main",Gm,[k("div",Jm,[l.value?(K(),H(U,{key:1},[k("div",Zm,[k("div",zm,[k("div",qm,S((null==(u=null==(r=n.value.email)?void 0:r[0])?void 0:u.toUpperCase())||"U"),1),k("div",$m,[k("h2",AQ,S(n.value.email),1),k("p",eQ,[k("span",{textContent:S(M(t)("profile.registration_time"))},null,8,tQ),T(": "+S(c(n.value.created_at)),1)])])])]),k("div",rQ,[x(d,{to:"/change-password",class:"w-full py-3 px-4 bg-gray-800 text-white rounded-lg font-medium flex items-center"},{default:X((()=>[e[3]||(e[3]=k("i",{class:"ri-lock-password-line mr-3"},null,-1)),k("span",{textContent:S(M(t)("auth.change_password"))},null,8,nQ),e[4]||(e[4]=k("i",{class:"ri-arrow-right-s-line ml-auto"},null,-1))])),_:1,__:[3,4]}),k("div",{class:"w-full py-3 px-4 bg-gray-800 text-white rounded-lg font-medium flex items-center cursor-pointer",onClick:e[0]||(e[0]=A=>s.value=!0)},[e[6]||(e[6]=k("i",{class:"ri-global-line mr-3"},null,-1)),k("span",{textContent:S(M(t)("profile.language_settings"))},null,8,sQ),k("div",oQ,[k("span",aQ,S(a()),1),e[5]||(e[5]=k("i",{class:"ri-arrow-right-s-line"},null,-1))])]),s.value?(K(),H("div",iQ,[k("div",lQ,[k("div",cQ,[k("h3",uQ,S(M(t)("profile.language_settings")),1),k("button",{onClick:e[1]||(e[1]=A=>s.value=!1),class:"text-gray-400 hover:text-white"},e[7]||(e[7]=[k("i",{class:"ri-close-line text-xl"},null,-1)]))]),k("div",dQ,[k("div",BQ,[(K(),H(U,null,P(i,(A=>{return k("button",{key:A.code,onClick:e=>(A=>{o.value!==A?(s.value=!1,o.value=A,l.value&&B(A),Tm(A)):s.value=!1})(A.code),class:O(["w-full py-3 px-4 rounded-lg flex items-center justify-between transition-colors duration-200",o.value===A.code?"bg-primary/20 text-primary":"bg-gray-800 text-white hover:bg-gray-700"])},[k("div",pQ,[k("span",fQ,S((e=A.code,{"zh-CN":"🇨🇳","en-US":"🇺🇸","ja-JP":"🇯🇵","ko-KR":"🇰🇷"}[e]||"🌐")),1),k("span",null,S(A.name),1)]),o.value===A.code?(K(),H("i",wQ)):I("",!0)],10,gQ);var e})),64))])])])])):I("",!0),k("a",hQ,[e[8]||(e[8]=k("i",{class:"ri-shield-check-line mr-3"},null,-1)),k("span",{textContent:S(M(t)("common.privacy_policy"))},null,8,mQ),e[9]||(e[9]=k("i",{class:"ri-external-link-line ml-auto"},null,-1))]),k("button",QQ,[e[10]||(e[10]=k("i",{class:"ri-information-line mr-3"},null,-1)),k("span",{textContent:S(M(t)("common.about_us"))},null,8,yQ),e[11]||(e[11]=k("i",{class:"ri-arrow-right-s-line ml-auto"},null,-1))]),k("button",{class:"w-full py-3 px-4 bg-red-500 text-white rounded-lg font-medium flex items-center",onClick:g},[e[12]||(e[12]=k("i",{class:"ri-logout-box-line mr-3"},null,-1)),k("span",{textContent:S(M(t)("auth.logout"))},null,8,CQ)])])],64)):(K(),H("div",Xm,[k("div",jm,[e[2]||(e[2]=k("div",{class:"w-20 h-20 rounded-full bg-gradient-to-r from-primary to-blue-500 flex items-center justify-center text-3xl font-bold mx-auto mb-4"},[k("i",{class:"ri-user-3-line"})],-1)),k("h2",Wm,S(M(t)("auth.logout")),1),k("p",Ym,S(M(t)("profile.profile")),1),x(d,{to:"/login",class:"inline-block py-2 px-6 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium"},{default:X((()=>[T(S(M(t)("auth.login")),1)])),_:1})])]))])]),k("nav",UQ,[k("div",vQ,[k("div",FQ,[x(d,{to:"/",class:"flex flex-col items-center justify-center text-gray-500"},{default:X((()=>[e[13]||(e[13]=k("i",{class:"ri-line-chart-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1)),k("span",{class:"text-xs mt-0.5",textContent:S(M(t)("nav.market"))},null,8,bQ)])),_:1,__:[13]}),x(d,{to:"/points",class:"flex flex-col items-center justify-center text-gray-500"},{default:X((()=>[e[14]||(e[14]=k("i",{class:"ri-coin-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1)),k("span",{class:"text-xs mt-0.5",textContent:S(M(t)("nav.points"))},null,8,_Q)])),_:1,__:[14]}),x(d,{to:"/profile",class:"flex flex-col items-center justify-center text-primary border-t-2 border-primary"},{default:X((()=>[e[15]||(e[15]=k("i",{class:"ri-settings-3-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1)),k("span",{class:"text-xs mt-0.5",textContent:S(M(t)("nav.settings"))},null,8,xQ)])),_:1,__:[15]})])])])])}}}),HQ={class:"relative h-[600px] flex flex-col bg-[#0F172A]"},IQ={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800/50"},kQ={class:"max-w-[375px] mx-auto"},LQ={class:"flex items-center h-12 px-4"},SQ={class:"text-lg font-semibold"},KQ={class:"flex-1 pt-16 pb-16 overflow-y-auto"},DQ={class:"max-w-[375px] mx-auto px-4 space-y-6"},TQ={class:"relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-pink-600/20 p-6 backdrop-blur-sm border border-white/10"},OQ={class:"relative"},MQ={class:"flex items-center justify-between mb-4"},RQ={class:"text-gray-400 text-sm"},PQ={class:"text-3xl font-bold text-white"},NQ={class:"text-right"},VQ={class:"text-gray-400 text-sm"},GQ={class:"text-xl font-semibold text-yellow-400"},JQ={class:"flex items-center text-sm text-gray-300"},XQ={class:"rounded-2xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-5"},jQ={class:"flex items-center justify-between mb-3"},WQ={class:"flex items-center"},YQ={class:"font-semibold text-white"},ZQ={class:"text-gray-400 text-sm"},zQ={class:"bg-gray-900/50 rounded-xl p-4 border border-gray-600/30"},qQ={class:"flex items-center justify-between"},$Q={class:"text-gray-400 text-xs mb-1"},Ay={class:"font-mono text-lg font-semibold text-white"},ey={class:"text-sm"},ty={class:"rounded-2xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-5"},ry={class:"text-lg font-semibold text-white mb-4"},ny={key:0,class:"space-y-3"},sy={class:"flex items-center space-x-3"},oy={class:"w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-sm font-bold text-white"},ay={class:"font-medium text-white text-sm"},iy={class:"text-gray-400 text-xs"},ly={class:"text-green-400 font-semibold text-sm"},cy={key:1,class:"text-center py-8"},uy={class:"text-gray-400 text-sm"},dy={class:"text-gray-500 text-xs mt-1"},By={class:"sticky bottom-0 w-full z-20 bg-[#0F172A]/95 backdrop-blur-md border-t border-gray-800"},gy={class:"max-w-[375px] mx-auto"},py={class:"grid grid-cols-3 h-16"},fy={class:"text-xs mt-0.5"},wy={class:"text-xs mt-0.5"},hy={class:"text-xs mt-0.5"},my={key:0,class:"fixed bottom-24 left-0 right-0 flex justify-center z-30 px-4"},Qy={class:"bg-green-500/90 backdrop-blur-sm text-white px-6 py-3 rounded-2xl shadow-lg border border-green-400/20 flex items-center space-x-2"},yy={class:"font-medium"},Cy="temporary_invitation_uuid",Uy={class:"min-h-screen flex flex-col bg-[#0F172A] overflow-y-auto"},vy={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},Fy={class:"max-w-[375px] mx-auto"},by={class:"flex items-center px-4 py-3"},_y={class:"text-lg font-semibold"},xy={class:"flex-1 pt-16 pb-16"},Ey={class:"max-w-[375px] mx-auto px-4"},Hy={class:"block text-sm font-medium text-gray-400 mb-1"},Iy=["placeholder"],ky={key:0,class:"mt-1 text-sm text-red-500"},Ly={class:"flex gap-2"},Sy={class:"flex-1"},Ky={class:"block text-sm font-medium text-gray-400 mb-1"},Dy=["placeholder"],Ty={key:0,class:"mt-1 text-sm text-red-500"},Oy=["disabled"],My={class:"block text-sm font-medium text-gray-400 mb-1"},Ry=["placeholder"],Py={key:0,class:"mt-1 text-sm text-red-500"},Ny={class:"mt-1 text-xs text-gray-500"},Vy={class:"block text-sm font-medium text-gray-400 mb-1"},Gy=["placeholder"],Jy={key:0,class:"mt-1 text-sm text-red-500"},Xy=["disabled"],jy={class:"mt-6 text-center"},Wy={class:"text-sm text-gray-400"},Yy={class:"min-h-screen flex flex-col bg-[#0F172A] overflow-y-auto"},Zy={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},zy={class:"max-w-[375px] mx-auto"},qy={class:"flex items-center px-4 py-3"},$y={class:"text-lg font-semibold"},AC={class:"flex-1 pt-16 pb-16"},eC={class:"max-w-[375px] mx-auto px-4"},tC={key:0,class:"text-center py-8"},rC={class:"text-gray-400 mb-4"},nC={key:1},sC={class:"text-gray-400 mb-6"},oC={key:0,class:"p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm"},aC={key:1,class:"p-3 rounded-lg bg-green-500/10 border border-green-500/20 text-green-400 text-sm"},iC={for:"current_password",class:"block text-sm font-medium text-gray-300 mb-1"},lC={for:"new_password",class:"block text-sm font-medium text-gray-300 mb-1"},cC={class:"mt-1 text-xs text-gray-500"},uC={for:"confirm_password",class:"block text-sm font-medium text-gray-300 mb-1"},dC=["disabled"],BC=[{path:"/",name:"home",component:_h,meta:{title:"CoolTrade - Home",requiresAuth:!0}},{path:"/about",name:"about",component:Vh,meta:{title:"CoolTrade - About",requiresAuth:!1}},{path:"/login",name:"login",component:am,meta:{title:"CoolTrade - Login",guest:!0}},{path:"/register",name:"register",component:Sm,meta:{title:"CoolTrade - Register",guest:!0}},{path:"/profile",name:"profile",component:EQ,meta:{title:"CoolTrade - Profile",requiresAuth:!0}},{path:"/points",name:"points",component:y({__name:"PointsView",setup(A){const{t:e}=bu(),t=f({points:0,invitation_code:"",invitation_points_per_user:10,invitation_count:0,invitation_records:[]}),r=f(null),n=f(!1),s=()=>u(this,null,(function*(){if("undefined"!=typeof chrome&&chrome.runtime)try{const A="http://127.0.0.1:8000";chrome.runtime.sendMessage({type:"getCookie",data:{url:A,name:Cy}},(e=>{chrome.runtime.lastError||e&&e.error||e&&e.cookie&&(o(e.cookie.value),chrome.runtime.sendMessage({type:"removeCookie",data:{url:A,name:Cy}}))}))}catch(A){}})),o=A=>u(this,null,(function*(){try{if(!A)return;if(!localStorage.getItem("token"))return;const e=yield wg(A);"success"===e.status&&"Successfully claimed invitation and received reward"===e.message&&a()}catch(e){if("undefined"!=typeof chrome&&chrome.runtime)try{const A="http://127.0.0.1:8000";chrome.runtime.sendMessage({type:"removeCookie",data:{url:A,name:Cy}})}catch(t){}}})),a=()=>u(this,null,(function*(){try{if(!localStorage.getItem("token"))return;try{const A=yield pg();"success"===A.status&&A.data&&(t.value=A.data);const e=yield fg();"success"===e.status&&void 0!==e.ranking&&(r.value=e.ranking)}catch(A){}}catch(A){}})),i=A=>{try{const e=new Date(A),t=(new Date).getTime()-e.getTime(),r=Math.floor(t/864e5);return 0===r?"Today":1===r?"Yesterday":r<7?`${r} days ago`:e.toLocaleDateString("en-US",{month:"short",day:"numeric"})}catch(e){return A}},l=()=>{if(!t.value.invitation_code)return;const A=e("points.share_invitation_text",{code:t.value.invitation_code,points:t.value.invitation_points_per_user})+`\nhttp://127.0.0.1:8000/?code=${t.value.invitation_code}`;navigator.clipboard.writeText(A).then((()=>{n.value=!0,setTimeout((()=>{n.value=!1}),2e3)})).catch((A=>{}))};return b((()=>{a(),s()})),(A,s)=>{const o=W("router-link");return K(),H("div",HQ,[k("header",IQ,[k("div",kQ,[k("div",LQ,[k("h1",SQ,S(M(e)("points.my_points")),1)])])]),k("main",KQ,[k("div",DQ,[k("div",TQ,[s[1]||(s[1]=k("div",{class:"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5"},null,-1)),k("div",OQ,[k("div",MQ,[k("div",null,[k("p",RQ,S(M(e)("points.total_points")),1),k("h2",PQ,S(t.value.points||0),1)]),k("div",NQ,[k("p",VQ,S(M(e)("points.ranking")),1),k("h3",GQ,"#"+S(r.value||"--"),1)])]),k("div",JQ,[s[0]||(s[0]=k("i",{class:"ri-trophy-line mr-2 text-yellow-400"},null,-1)),k("span",null,S(M(e)("points.total_invited",{count:t.value.invitation_count||0})),1)])])]),k("div",XQ,[k("div",jQ,[k("div",WQ,[s[2]||(s[2]=k("div",{class:"w-10 h-10 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center mr-3"},[k("i",{class:"ri-user-add-line text-lg text-white"})],-1)),k("div",null,[k("h3",YQ,S(M(e)("points.invite_friends")),1),k("p",ZQ,"+"+S(t.value.invitation_points_per_user||10)+" "+S(M(e)("points.points")),1)])])]),k("div",zQ,[k("div",qQ,[k("div",null,[k("p",$Q,S(M(e)("points.your_invitation_code")),1),k("p",Ay,S(t.value.invitation_code||"..."),1)]),k("button",{onClick:l,class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center space-x-2"},[s[3]||(s[3]=k("i",{class:"ri-file-copy-line"},null,-1)),k("span",ey,S(M(e)("points.share")),1)])])])]),k("div",ty,[k("h3",ry,S(M(e)("points.invitation_records")),1),t.value.invitation_records&&t.value.invitation_records.length>0?(K(),H("div",ny,[(K(!0),H(U,null,P(t.value.invitation_records,(A=>(K(),H("div",{key:A.invitee_email,class:"flex items-center justify-between p-3 rounded-xl bg-gray-900/30 border border-gray-600/20"},[k("div",sy,[k("div",oy,S(A.invitee_email?A.invitee_email.charAt(0).toUpperCase():"?"),1),k("div",null,[k("p",ay,S(A.invitee_email),1),k("p",iy,S(i(A.created_at)),1)])]),k("div",ly,"+"+S(A.points_awarded),1)])))),128))])):(K(),H("div",cy,[s[4]||(s[4]=k("div",{class:"w-16 h-16 rounded-full bg-gray-700/50 flex items-center justify-center mx-auto mb-3"},[k("i",{class:"ri-user-add-line text-2xl text-gray-500"})],-1)),k("p",uy,S(M(e)("points.no_invitation_records")),1),k("p",dy,S(M(e)("points.invite_friends_to_earn_points")),1)]))])])]),k("nav",By,[k("div",gy,[k("div",py,[x(o,{to:"/",class:"flex flex-col items-center justify-center text-gray-500"},{default:X((()=>[s[5]||(s[5]=k("i",{class:"ri-line-chart-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1)),k("span",fy,S(M(e)("nav.market")),1)])),_:1,__:[5]}),x(o,{to:"/points",class:"flex flex-col items-center justify-center text-primary border-t-2 border-primary"},{default:X((()=>[s[6]||(s[6]=k("i",{class:"ri-coin-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1)),k("span",wy,S(M(e)("nav.points")),1)])),_:1,__:[6]}),x(o,{to:"/profile",class:"flex flex-col items-center justify-center text-gray-500"},{default:X((()=>[s[7]||(s[7]=k("i",{class:"ri-settings-3-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1)),k("span",hy,S(M(e)("nav.settings")),1)])),_:1,__:[7]})])])]),n.value?(K(),H("div",my,[k("div",Qy,[s[8]||(s[8]=k("i",{class:"ri-check-line text-lg"},null,-1)),k("span",yy,S(M(e)("points.copy_success")),1)])])):I("",!0)])}}}),meta:{title:"CoolTrade - Points",requiresAuth:!0}},{path:"/forgot-password",name:"forgot-password",component:y({__name:"ForgotPasswordView",setup(A){const{t:e}=bu(),t=Y(),r=f(!1),n=f(!1),s=f(0),o=f({email:"",code:"",new_password:"",confirm_password:""}),a=f({email:"",code:"",new_password:"",confirm_password:""}),i=f(null),l=f(null),c=f(null),d=f(null),B=()=>{t.push("/login")},g=()=>{a.value.email=""},p=()=>{a.value.code=""},w=()=>{a.value.new_password=""},h=()=>{a.value.confirm_password=""},m=()=>u(this,null,(function*(){var A,t,r;if(!o.value.email)return a.value.email=e("errors.email_required"),void(null==(A=i.value)||A.focus());n.value=!0;try{yield ag({email:o.value.email.trim()}),(()=>{s.value=60;const A=setInterval((()=>{s.value--,s.value<=0&&clearInterval(A)}),1e3)})()}catch(l){if(null==(r=null==(t=l.response)?void 0:t.data)?void 0:r.message)if("object"==typeof l.response.data.message){const A=Object.values(l.response.data.message).flat();A.length>0&&(a.value.email=A[0])}else a.value.email=l.response.data.message}finally{n.value=!1}})),Q=()=>u(this,null,(function*(){var A,e;r.value=!0,Object.keys(a.value).forEach((A=>{a.value[A]=""}));try{"success"===(yield cg({email:o.value.email.trim(),code:o.value.code.trim(),new_password:o.value.new_password.trim(),confirm_password:o.value.confirm_password.trim()})).status&&t.push("/login")}catch(n){(null==(e=null==(A=n.response)?void 0:A.data)?void 0:e.message)&&"object"==typeof n.response.data.message&&Object.entries(n.response.data.message).forEach((([A,e])=>{A in a.value&&(a.value[A]=Array.isArray(e)?e[0]:e)}))}finally{r.value=!1}}));return(A,u)=>(K(),H("div",Uy,[k("header",vy,[k("div",Fy,[k("div",by,[k("button",{onClick:u[0]||(u[0]=A=>M(t).push("/login")),class:"mr-2"},u[5]||(u[5]=[k("i",{class:"ri-arrow-left-line ri-lg"},null,-1)])),k("h1",_y,S(M(e)("auth.forgot_password")),1)])])]),k("main",xy,[k("div",Ey,[u[6]||(u[6]=k("div",{class:"flex flex-col items-center justify-center mt-8 mb-6"},[k("img",{src:Gh,alt:"Cooltrade Logo",class:"w-16 h-16 mb-2 rounded-lg shadow-lg"}),k("div",{class:"text-2xl font-bold text-white tracking-wide mb-1"},"Cooltrade")],-1)),k("form",{onSubmit:N(Q,["prevent"]),class:"space-y-4"},[k("div",null,[k("label",Hy,S(M(e)("auth.email")),1),V(k("input",{type:"email","onUpdate:modelValue":u[1]||(u[1]=A=>o.value.email=A),onInput:g,required:"",ref_key:"emailInput",ref:i,class:O(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",a.value.email?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:M(e)("auth.email_placeholder")},null,42,Iy),[[G,o.value.email]]),a.value.email?(K(),H("p",ky,S(a.value.email),1)):I("",!0)]),k("div",Ly,[k("div",Sy,[k("label",Ky,S(M(e)("auth.verification_code")),1),V(k("input",{type:"text","onUpdate:modelValue":u[2]||(u[2]=A=>o.value.code=A),onInput:p,required:"",ref_key:"codeInput",ref:l,class:O(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",a.value.code?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:M(e)("auth.verification_code_placeholder")},null,42,Dy),[[G,o.value.code]]),a.value.code?(K(),H("p",Ty,S(a.value.code),1)):I("",!0)]),k("button",{type:"button",onClick:m,disabled:n.value||s.value>0,class:"mt-6 px-4 py-2 bg-gray-800 text-white rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed transition-colors"},S(s.value>0?M(e)("auth.retry_in_seconds",{seconds:s.value}):n.value?M(e)("common.sending"):M(e)("auth.send_code")),9,Oy)]),k("div",null,[k("label",My,S(M(e)("auth.new_password")),1),V(k("input",{type:"password","onUpdate:modelValue":u[3]||(u[3]=A=>o.value.new_password=A),onInput:w,required:"",ref_key:"passwordInput",ref:c,class:O(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",a.value.new_password?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:M(e)("auth.new_password_placeholder")},null,42,Ry),[[G,o.value.new_password]]),a.value.new_password?(K(),H("p",Py,S(a.value.new_password),1)):I("",!0),k("p",Ny,S(M(e)("auth.password_requirements")),1)]),k("div",null,[k("label",Vy,S(M(e)("auth.confirm_new_password")),1),V(k("input",{type:"password","onUpdate:modelValue":u[4]||(u[4]=A=>o.value.confirm_password=A),onInput:h,required:"",ref_key:"confirmPasswordInput",ref:d,class:O(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",a.value.confirm_password?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:M(e)("auth.confirm_new_password_placeholder")},null,42,Gy),[[G,o.value.confirm_password]]),a.value.confirm_password?(K(),H("p",Jy,S(a.value.confirm_password),1)):I("",!0)]),k("button",{type:"submit",class:"w-full py-3 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors",disabled:r.value},S(r.value?M(e)("common.submitting"):M(e)("auth.reset_password")),9,Xy)],32),k("div",jy,[k("p",Wy,[T(S(M(e)("auth.remember_password"))+" ",1),k("a",{href:"#",onClick:N(B,["prevent"]),class:"text-primary hover:underline"},S(M(e)("auth.login_now")),1)])])])])]))}}),meta:{title:"CoolTrade - Forgot Password",guest:!0}},{path:"/change-password",name:"change-password",component:y({__name:"ChangePasswordView",setup(A){const{t:e}=bu(),t=Y(),r=f(!1),n=f(void 0),s=f(void 0),o=f({current_password:"",new_password:"",confirm_password:""}),a=h((()=>!!localStorage.getItem("token"))),i=()=>u(this,null,(function*(){var A,t,a,i,l;r.value=!0,n.value=void 0,s.value=void 0;try{"success"===(yield ug({current_password:o.value.current_password.trim(),new_password:o.value.new_password.trim(),confirm_password:o.value.confirm_password.trim()})).status&&(s.value=e("auth.password_changed"),o.value={current_password:"",new_password:"",confirm_password:""})}catch(c){if(null==(t=null==(A=c.response)?void 0:A.data)?void 0:t.message)if("object"==typeof c.response.data.message){const A=Object.values(c.response.data.message).flat();A.length>0&&(n.value=A[0])}else n.value=c.response.data.message;else(null==(i=null==(a=c.response)?void 0:a.data)?void 0:i.detail)?n.value=c.response.data.detail:401===(null==(l=c.response)?void 0:l.status)?n.value=e("errors.unauthorized"):n.value=e("errors.unknown_error")}finally{r.value=!1}}));return(A,l)=>{const c=W("router-link");return K(),H("div",Yy,[k("header",Zy,[k("div",zy,[k("div",qy,[k("button",{onClick:l[0]||(l[0]=A=>M(t).push("/profile")),class:"mr-2"},l[4]||(l[4]=[k("i",{class:"ri-arrow-left-line ri-lg"},null,-1)])),k("h1",$y,S(M(e)("auth.change_password")),1)])])]),k("main",AC,[k("div",eC,[a.value?(K(),H("div",nC,[k("p",sC,S(M(e)("auth.enter_current_and_new_password")),1),k("form",{onSubmit:N(i,["prevent"]),class:"space-y-6"},[n.value?(K(),H("div",oC,S(n.value),1)):I("",!0),s.value?(K(),H("div",aC,S(s.value),1)):I("",!0),k("div",null,[k("label",iC,S(M(e)("auth.current_password")),1),V(k("input",{id:"current_password","onUpdate:modelValue":l[1]||(l[1]=A=>o.value.current_password=A),type:"password",class:"w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors",required:""},null,512),[[G,o.value.current_password]])]),k("div",null,[k("label",lC,S(M(e)("auth.new_password")),1),V(k("input",{id:"new_password","onUpdate:modelValue":l[2]||(l[2]=A=>o.value.new_password=A),type:"password",class:"w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors",required:""},null,512),[[G,o.value.new_password]]),k("p",cC,S(M(e)("auth.password_requirements")),1)]),k("div",null,[k("label",uC,S(M(e)("auth.confirm_new_password")),1),V(k("input",{id:"confirm_password","onUpdate:modelValue":l[3]||(l[3]=A=>o.value.confirm_password=A),type:"password",class:"w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors",required:""},null,512),[[G,o.value.confirm_password]])]),k("button",{type:"submit",class:"w-full py-3 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors",disabled:r.value},S(r.value?M(e)("common.submitting"):M(e)("auth.change_password")),9,dC)],32)])):(K(),H("div",tC,[k("p",rC,S(M(e)("auth.please_login_first")),1),x(c,{to:"/login",class:"inline-block py-2 px-6 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium"},{default:X((()=>[T(S(M(e)("auth.login")),1)])),_:1})]))])])])}}}),meta:{title:"CoolTrade - Change Password",requiresAuth:!0}}],gC=Z({history:z(),routes:BC});function pC(){const A=localStorage.getItem("token"),e=localStorage.getItem("userInfo");return A&&e}gC.beforeEach(((A,e,t)=>{var r;(null==(r=A.meta)?void 0:r.title)&&(document.title=A.meta.title);const n=A.matched.some((A=>A.meta.requiresAuth)),s=A.matched.some((A=>A.meta.guest));n&&!pC()?t({path:"/login",query:{redirect:A.fullPath}}):s&&pC()?t({path:"/"}):t()}));const fC={id:"app",class:"min-h-screen bg-dark-900 text-white"},wC={class:"min-h-screen"},hC=y({__name:"App",setup:A=>(b((()=>{})),(A,e)=>{const t=W("router-view");return K(),H("div",fC,[k("main",wC,[x(t)])])})}),mC=localStorage.getItem("language");"zh-CN"!==mC&&mC||localStorage.setItem("language","en-US");const QC=q(hC);QC.use($()),QC.use(gC),QC.use(tA),QC.use(Dm),QC.use(Cu),QC.mount("#app")}},function(){return nA||(0,rA[r(rA)[0]])((nA={exports:{}}).exports,nA),nA.exports});export default sA();
